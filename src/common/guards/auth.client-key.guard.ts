import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { ConfigService } from "@nestjs/config";


@Injectable()
export class AuthClientKeyGuard implements CanActivate {
    constructor(
      private readonly configService: ConfigService
    ) {}

    canActivate(context: ExecutionContext): boolean {
        const request = context.switchToHttp().getRequest();
        const clientId = request.headers['client-id'] || null;
        const secretKey = request.headers['secret-key'] || null;
        const serverSecret = this.configService.get(`${clientId}_SECRET_KEY`) || null;
        const leadIdAllowed = this.configService.get(`${clientId}_LEAD_ID`) || null;
        const leadId = request.headers['lead_id'] || null;
        const isValid = leadIdAllowed ? leadIdAllowed.includes(leadId) : true;
        if(!isValid) {
          return false;
        }
        if (!serverSecret || serverSecret !== secretKey) {
          return false;
        }
        return true;
        // NOTE: At now, verify on hard configure on .env, will implement dynamic keys in the future.
    }
}
