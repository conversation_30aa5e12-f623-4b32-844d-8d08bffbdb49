import { Model } from 'mongoose';
import { Inject, Injectable } from '@nestjs/common';
import _ = require('lodash');
import { CommonConst } from '../../shared/constant/common.const';
import { ICodeGenerateDocument } from '../interfaces/document.interface';

@Injectable()
export class CodeGenerateRepository {
  private readonly NUMBER_PAD_START = 5;
  constructor(
    @Inject(CommonConst.QUERY_MODEL_TOKEN)
    private readonly readModel: Model<ICodeGenerateDocument>
  ) { }

  async generateCode(name, prefix, maxLength = this.NUMBER_PAD_START) {
    return await this.readModel
      .findOneAndUpdate(
        { name },
        { $set: { prefix: prefix }, $inc: { index: 1 } },
        { upsert: true }
      )
      .then((rs) => {
        if (_.isEmpty(rs)) {
          const stt = "1".padStart(maxLength, "0");
          return `${prefix}${stt}`;
        }
        if ((rs.index + 1).toString().length > maxLength) {
          return `${prefix}${(rs.index + 1).toString()}`;
        }
        return `${prefix}${(rs.index + 1)
          .toString()
          .padStart(maxLength, "0")}`;
      })
      .catch((error) => {
        return "";
      });
  }
}
