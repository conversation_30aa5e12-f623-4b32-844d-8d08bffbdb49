import { Injectable } from "@nestjs/common";
import { CodeGenerateRepository } from "./repository/query.repository";
import { CmdPatternConst } from "../../../shared-modules";
import { CommonConst } from "../shared/constant/common.const";
import { EmployeeClient } from "../mgs-sender/employee.client";

@Injectable()
export class CodeGenerateService {

  constructor(
    private readonly repository: CodeGenerateRepository,
    private readonly employeeClient: EmployeeClient
  ) { }

  async generateCode(prefix) {
    return await this.repository.generateCode('lead.index', prefix);
  }

  async generateLeadCode(user) {
    const employee = await this.employeeClient.sendDataPromise({ where: { 'account.id': user.id, active: true }, projection: { _id: 0, pos: 1, orgCode: 1 } }, CmdPatternConst.EMPLOYEE.LISTENER.GET_BY_QUERY);
    const empOrgCode = employee[0]?.companyCode || employee[0]?.pos?.code || employee[0]?.orgCode || '';
    if (!empOrgCode) {
      return '';
    }
    return await this.repository.generateCode('lead.index', empOrgCode.substring(0, 4) + CommonConst.LEAD_PREFIX, 7);
  }

  async generateLeadRepoCode(user) {
    const employee = await this.employeeClient.sendDataPromise({ where: { 'account.id': user.id, active: true }, projection: { _id: 0, pos: 1, orgCode: 1 } }, CmdPatternConst.EMPLOYEE.LISTENER.GET_BY_QUERY);
    const empOrgCode = employee[0]?.companyCode || employee[0]?.pos?.code || employee[0]?.orgCode || '';
    if (!empOrgCode) {
      return '';
    }
    return await this.repository.generateCode("lead-repo.index", empOrgCode.substring(0, 4) + CommonConst.LEAD_REPO_PREFIX, 7);
  }

  async generateLeadRepoConfigCode(user) {
    const employee = await this.employeeClient.sendDataPromise({ where: { 'account.id': user.id, active: true }, projection: { _id: 0, pos: 1, orgCode: 1 } }, CmdPatternConst.EMPLOYEE.LISTENER.GET_BY_QUERY);
    const empOrgCode = employee[0]?.companyCode || employee[0]?.pos?.code || employee[0]?.orgCode || '';
    if (!empOrgCode) {
      return '';
    }
    return await this.repository.generateCode("lead-repo-config.index", empOrgCode.substring(0, 4) + CommonConst.LEAD_REPO_CONFIG_PREFIX, 7);
  }
}
