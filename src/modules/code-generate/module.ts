import { Module } from "@nestjs/common";
import { CodeGenerateService } from "./service";
import { QueryDatabaseModule } from "../database/query/query.database.module";
import { CodeGenerateRepository } from "./repository/query.repository";
import { QueryProviders } from "./providers/query.cqrs.providers";
import { MgsSenderModule } from "../mgs-sender/mgs-sender.module";

@Module({
    imports: [QueryDatabaseModule, MgsSenderModule],
    providers: [
      CodeGenerateRepository,CodeGenerateService,...QueryProviders],
    exports: [CodeGenerateRepository, CodeGenerateModule, CodeGenerateService],
})

export class CodeGenerateModule { }