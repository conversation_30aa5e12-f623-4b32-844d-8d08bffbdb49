import { Injectable } from '@nestjs/common';
import { LoggerClient } from '../mgs-sender/logger.client';
import { ConfigService } from '@nestjs/config';
import { CmdPatternConst } from '../shared/constant/cmd-pattern.const';

@Injectable()
export class MsxLoggerService {
    constructor(
        private readonly loggerClient: LoggerClient,
        private readonly configService: ConfigService
    ) { }

    async log(context, message: string, data?: any) {
        if (this.configService.get('DEBUG') === 'true') {
            console.log(`${context} - ${message} `, data || '');
        }
        const content = { svcName: this.configService.get('SVC_NAME'), context, message, object: data };
        this.loggerClient.sendLog(content, CmdPatternConst.LOGGER.SEND_LOG);
    }

    async error(context, message: string, trace?: any) {
        if (this.configService.get('DEBUG') === 'true') {
            console.log(`${context} - ${message} `, trace);
        }
        const content = { svcName: this.configService.get('SVC_NAME'), context, message, trace };
        this.loggerClient.sendLog(content, CmdPatternConst.LOGGER.SEND_ERROR);
    }

    async logLocal(context, message: string, data?: any) {
        if (this.configService.get("DEBUG") === "true") {
            console.log(
              `${new Date().toLocaleString()} - ${context} - ${message} `,
              JSON.stringify(data) || ""
            );
        }
    }
}
