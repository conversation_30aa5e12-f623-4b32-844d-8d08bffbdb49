import { EventPublisher, <PERSON><PERSON>ommand<PERSON><PERSON><PERSON>, CommandHandler } from '@nestjs/cqrs';
import { DeleteLeadQueryCommand } from '../impl/delete-query.cmd';
import { LeadQueryRepository } from '../../repository/query.repository';
import { MsxLoggerService } from '../../../../modules/logger/logger.service';
import { CommonConst } from '../../../../modules/shared/constant/common.const';

@CommandHandler(DeleteLeadQueryCommand)
export class DeleteQueryCommandHandler implements ICommandHandler<DeleteLeadQueryCommand> {
  private readonly context = DeleteQueryCommandHandler.name;
  private readonly aggregateName: string = CommonConst.AGGREGATE_NAME_LEAD;
  constructor(
    private readonly loggerService: MsxLoggerService,
    private readonly repository: LeadQueryRepository,
    private readonly publisher: EventPublisher
  ) { }


  async execute(command: DeleteLeadQueryCommand) {
    this.loggerService.log(this.context, `Async Delete query ${this.aggregateName} cmd ...`);

    const { messagePattern, commandModel } = command;

    const cmdPublisher = this.publisher.mergeObjectContext(
      await this.repository.findAggregateModelById(commandModel.id)
    );

    cmdPublisher.deleteItem(commandModel.id);
    cmdPublisher.commit();
  }
}
