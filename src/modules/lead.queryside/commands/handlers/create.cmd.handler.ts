import { EventPublisher, <PERSON><PERSON>ommand<PERSON><PERSON><PERSON>, CommandHandler } from '@nestjs/cqrs';
import { CreateLeadQueryCommand } from '../impl/create-query.cmd';
import { LeadQueryRepository } from '../../repository/query.repository';
import { CommonConst } from '../../../../modules/shared/constant/common.const';
import { MsxLoggerService } from '../../../../modules/logger/logger.service';


@CommandHandler(CreateLeadQueryCommand)
export class CreateQueryCommandHandler implements ICommandHandler<CreateLeadQueryCommand> {
  private readonly context = CreateQueryCommandHandler.name;
  private readonly aggregateName: string = CommonConst.AGGREGATE_NAME_LEAD;
  constructor(
    private readonly loggerService: MsxLoggerService,
    private readonly repository: LeadQueryRepository,
    private readonly publisher: EventPublisher
  ) { }


  async execute(command: CreateLeadQueryCommand) {
    this.loggerService.log(this.context, `Async create query ${this.aggregateName} cmd ...`);

    const { messagePattern, id, commandModel } = command;

    const cmdPublisher = this.publisher.mergeObjectContext(
      await this.repository.findAggregateModelById(id)
    );

    cmdPublisher.addItem(commandModel);
    cmdPublisher.commit();
  }
}