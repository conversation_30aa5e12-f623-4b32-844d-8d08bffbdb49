import { EventPublisher, <PERSON><PERSON>ommand<PERSON><PERSON><PERSON>, CommandHandler } from '@nestjs/cqrs';
import { LeadQueryRepository } from '../../repository/query.repository';
import { PendingLeadQueryCommand } from '../impl/pending-query.cmd';
import { CommonConst } from '../../../../modules/shared/constant/common.const';
import { MsxLoggerService } from '../../../../modules/logger/logger.service';

@CommandHandler(PendingLeadQueryCommand)
export class PendingQueryCommandHandler implements ICommandHandler<PendingLeadQueryCommand> {
  private readonly context = PendingQueryCommandHandler.name;
  private readonly aggregateName: string = CommonConst.AGGREGATE_NAME_LEAD;
  constructor(
    private readonly loggerService: MsxLoggerService,
    private readonly repository: LeadQueryRepository,
    private readonly publisher: EventPublisher
  ) { }


  async execute(command: PendingLeadQueryCommand) {
    this.loggerService.log(this.context, `Async pending query ${this.aggregateName} cmd ...`);

    const { messagePattern, id, commandModel } = command;

    const cmdPublisher = this.publisher.mergeObjectContext(
      await this.repository.findAggregateModelById(id)
    );

    // const msg = this.aggregateName + '.' + messagePattern;
    cmdPublisher.pendingItem(commandModel);
    cmdPublisher.commit();
  }
}