import { EventPublisher, ICommand<PERSON><PERSON><PERSON>, CommandHandler } from '@nestjs/cqrs';
import { LeadQueryRepository } from '../../repository/query.repository';
import { FailLeadQueryCommand } from '../impl/fail-query.cmd';
import { CommonConst } from '../../../shared/constant/common.const';
import { MsxLoggerService } from '../../../logger/logger.service';

@CommandHandler(FailLeadQueryCommand)
export class FailQueryCommandHandler implements ICommandHandler<FailLeadQueryCommand> {
  private readonly context = FailQueryCommandHandler.name;
  private readonly aggregateName: string = CommonConst.AGGREGATE_NAME_LEAD;
  constructor(
    private readonly repository: LeadQueryRepository,
    private readonly publisher: EventPublisher,
    private readonly loggerService: MsxLoggerService,
  ) { }

  async execute(command: FailLeadQueryCommand) {
    this.loggerService.log(this.context, `Async reassign query ${this.aggregateName} cmd ...`);

    const { messagePattern, id, commandModel } = command;

    const cmdPublisher = this.publisher.mergeObjectContext(
      await this.repository.findAggregateModelById(id)
    );

    // const msg = this.aggregateName + '.' + messagePattern;
    cmdPublisher.failItem(commandModel);
    cmdPublisher.commit();
  }
}