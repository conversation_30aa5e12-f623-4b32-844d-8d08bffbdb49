import { <PERSON><PERSON>ublisher, <PERSON><PERSON>ommand<PERSON><PERSON><PERSON>, CommandHandler } from '@nestjs/cqrs';
import { LeadQueryRepository } from '../../repository/query.repository';
import { ProcessLeadQueryCommand } from '../impl/process-query.cmd';
import { CommonConst } from '../../../../modules/shared/constant/common.const';
import { MsxLoggerService } from '../../../../modules/logger/logger.service';

@CommandHandler(ProcessLeadQueryCommand)
export class ProcessQueryCommandHandler implements ICommandHandler<ProcessLeadQueryCommand> {
  private readonly context = ProcessQueryCommandHandler.name;
  private readonly aggregateName: string = CommonConst.AGGREGATE_NAME_LEAD;
  constructor(
    private readonly loggerService: MsxLoggerService,
    private readonly repository: LeadQueryRepository,
    private readonly publisher: EventPublisher
  ) { }


  async execute(command: ProcessLeadQueryCommand) {
    this.loggerService.log(this.context, `Async process query ${this.aggregateName} cmd ...`);

    const { messagePattern, id, commandModel } = command;

    const cmdPublisher = this.publisher.mergeObjectContext(
      await this.repository.findAggregateModelById(id)
    );

    // const msg = this.aggregateName + '.' + messagePattern;
    cmdPublisher.processItem(commandModel);
    cmdPublisher.commit();
  }
}