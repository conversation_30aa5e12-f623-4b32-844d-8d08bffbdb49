import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { MsxLoggerService } from '../../../logger/logger.service';
import { CommonConst } from '../../../shared/constant/common.const';
import { LeadQueryRepository } from '../../repository/query.repository';
import { ImportLeadQueryCommand } from '../impl/importLead-query.cmd';

@CommandHandler(ImportLeadQueryCommand)
export class ImportLeadQueryCommandHandler
  implements ICommandHandler<ImportLeadQueryCommand> {
  private readonly context = ImportLeadQueryCommandHandler.name;
  private readonly aggregateName: string = CommonConst.AGGREGATE_NAME_LEAD;
  constructor(
    private readonly loggerService: MsxLoggerService,
    private readonly repository: LeadQueryRepository,
    private readonly publisher: EventPublisher
  ) { }

  async execute(command: ImportLeadQueryCommand) {
    this.loggerService.log(
      this.context,
      `Async create query ${this.aggregateName} cmd ...`
    );

    const { messagePattern, id, commandModels } = command;

    const cmdPublisher = this.publisher.mergeObjectContext(
      await this.repository.findAggregateModelById(id)
    );

    cmdPublisher.addManyItems(commandModels);
    cmdPublisher.commit();
  }
}
