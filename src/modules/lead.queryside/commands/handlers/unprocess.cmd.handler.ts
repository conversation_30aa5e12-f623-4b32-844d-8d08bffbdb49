import { <PERSON><PERSON><PERSON>lisher, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CommandHandler } from '@nestjs/cqrs';
import { LeadQueryRepository } from '../../repository/query.repository';
import { UnprocessLeadQueryCommand } from '../impl/unprocess-query.cmd';
import { CommonConst } from '../../../../modules/shared/constant/common.const';
import { MsxLoggerService } from '../../../../modules/logger/logger.service';

@CommandHandler(UnprocessLeadQueryCommand)
export class UnprocessQueryCommandHandler implements ICommandHandler<UnprocessLeadQueryCommand> {
  private readonly context = UnprocessQueryCommandHandler.name;
  private readonly aggregateName: string = CommonConst.AGGREGATE_NAME_LEAD;
  constructor(
    private readonly repository: LeadQueryRepository,
    private readonly publisher: EventPublisher,
    private readonly loggerService: MsxLoggerService,
  ) { }


  async execute(command: UnprocessLeadQueryCommand) {
    this.loggerService.log(this.context, `Async unprocess query ${this.aggregateName} cmd ...`);

    const { messagePattern, id, commandModel } = command;

    const cmdPublisher = this.publisher.mergeObjectContext(
      await this.repository.findAggregateModelById(id)
    );

    // const msg = this.aggregateName + '.' + messagePattern;
    cmdPublisher.unprocessItem(commandModel);
    cmdPublisher.commit();
  }
}