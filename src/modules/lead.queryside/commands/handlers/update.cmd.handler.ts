import { EventPublisher, <PERSON><PERSON>ommand<PERSON><PERSON><PERSON>, CommandHandler } from '@nestjs/cqrs';
import { LeadQueryRepository } from '../../repository/query.repository';
import { UpdateLeadQueryCommand } from '../impl/update-query.cmd';
import { CommonConst } from '../../../../modules/shared/constant/common.const';
import { MsxLoggerService } from '../../../../modules/logger/logger.service';

@CommandHandler(UpdateLeadQueryCommand)
export class UpdateQueryCommandHandler implements ICommandHandler<UpdateLeadQueryCommand> {
  private readonly context = UpdateQueryCommandHandler.name;
  private readonly aggregateName: string = CommonConst.AGGREGATE_NAME_LEAD;
  constructor(
    private readonly repository: LeadQueryRepository,
    private readonly publisher: EventPublisher,
    private readonly loggerService: MsxLoggerService,
  ) { }

  async execute(command: UpdateLeadQ<PERSON>yCommand) {
    this.loggerService.log(this.context, `Async updating query ${this.aggregateName} cmd ...`);

    const { messagePattern, id, commandModel } = command;

    const cmdPublisher = this.publisher.mergeObjectContext(
      await this.repository.findAggregateModelById(id)
    );

    cmdPublisher.updateItem(commandModel);
    cmdPublisher.commit();
  }
}