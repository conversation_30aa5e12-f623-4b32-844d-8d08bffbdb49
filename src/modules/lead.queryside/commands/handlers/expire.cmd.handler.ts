import { EventPublisher, <PERSON><PERSON>ommand<PERSON><PERSON><PERSON>, CommandHandler } from '@nestjs/cqrs';
import { LeadQueryRepository } from '../../repository/query.repository';
import { ExpireLeadQueryCommand } from '../impl/expire-query.cmd';
import { CommonConst } from '../../../../modules/shared/constant/common.const';
import { MsxLoggerService } from '../../../../modules/logger/logger.service';

@CommandHandler(ExpireLeadQueryCommand)
export class ExpireQueryCommandHandler implements ICommandHandler<ExpireLeadQueryCommand> {
  private readonly context = ExpireQueryCommandHandler.name;
  private readonly aggregateName: string = CommonConst.AGGREGATE_NAME_LEAD;
  constructor(
    private readonly repository: LeadQueryRepository,
    private readonly publisher: EventPublisher,
    private readonly loggerService: MsxLoggerService,
  ) { }

  async execute(command: ExpireLeadQ<PERSON>yCommand) {
    this.loggerService.log(this.context, `Async expire query ${this.aggregateName} cmd ...`);

    const { messagePattern, id, commandModel } = command;

    const cmdPublisher = this.publisher.mergeObjectContext(
      await this.repository.findAggregateModelById(id)
    );

    // const msg = this.aggregateName + '.' + messagePattern;
    cmdPublisher.expireItem(commandModel);
    cmdPublisher.commit();
  }
}