import { EventPublisher, <PERSON><PERSON>ommand<PERSON><PERSON><PERSON>, CommandHandler } from '@nestjs/cqrs';
import { LeadQueryRepository } from '../../repository/query.repository';
import { AssignLeadQueryCommand } from '../impl/assign-query.cmd';
import { CommonConst } from '../../../../modules/shared/constant/common.const';
import { MsxLoggerService } from '../../../../modules/logger/logger.service';

@CommandHandler(AssignLeadQueryCommand)
export class AssignQueryCommandHandler implements ICommandHandler<AssignLeadQueryCommand> {
  private readonly context = AssignQueryCommandHandler.name;
  private readonly aggregateName: string = CommonConst.AGGREGATE_NAME_LEAD;
  constructor(
    private readonly loggerService: MsxLoggerService,
    private readonly repository: LeadQueryRepository,
    private readonly publisher: EventPublisher
  ) { }


  async execute(command: AssignLeadQueryCommand) {
    this.loggerService.log(this.context, `Async assign query ${this.aggregateName} cmd ...`);

    const { messagePattern, id, commandModel } = command;

    const cmdPublisher = this.publisher.mergeObjectContext(
      await this.repository.findAggregateModelById(id)
    );

    cmdPublisher.assignItem(commandModel);
    cmdPublisher.commit();
  }
}