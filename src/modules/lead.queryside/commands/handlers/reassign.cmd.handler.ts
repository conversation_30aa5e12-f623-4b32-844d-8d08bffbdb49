import { EventPublisher, <PERSON><PERSON>ommand<PERSON><PERSON><PERSON>, CommandHandler } from '@nestjs/cqrs';
import { LeadQueryRepository } from '../../repository/query.repository';
import { ReassignLeadQueryCommand } from '../impl/reassign-query.cmd';
import { CommonConst } from '../../../../modules/shared/constant/common.const';
import { MsxLoggerService } from '../../../../modules/logger/logger.service';

@CommandHandler(ReassignLeadQueryCommand)
export class ReassignQueryCommandHandler implements ICommandHandler<ReassignLeadQueryCommand> {
  private readonly context = ReassignQueryCommandHandler.name;
  private readonly aggregateName: string = CommonConst.AGGREGATE_NAME_LEAD;
  constructor(
    private readonly repository: LeadQueryRepository,
    private readonly publisher: EventPublisher,
    private readonly loggerService: MsxLoggerService,
  ) { }

  async execute(command: ReassignLeadQueryCommand) {
    this.loggerService.log(this.context, `Async reassign query ${this.aggregateName} cmd ...`);

    const { messagePattern, id, commandModel } = command;

    const cmdPublisher = this.publisher.mergeObjectContext(
      await this.repository.findAggregateModelById(id)
    );

    // const msg = this.aggregateName + '.' + messagePattern;
    cmdPublisher.reassignItem(commandModel);
    cmdPublisher.commit();
  }
}