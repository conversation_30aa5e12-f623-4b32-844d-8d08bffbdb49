import { Injectable, NotFoundException } from '@nestjs/common';
import { LeadQueryRepository } from './repository/query.repository';
import { IExploitHistory, ILead } from '../shared/services/lead/interfaces/lead.interface';
import { LifeCycleStatusEnum } from '../shared/enum/life-cycle-status.enum';
import { LeadMapper } from '../shared/mapper/lead.mapper';
import { LeadHistoryQueryRepository } from '../lead-history/repository/query.repository';
import { isNullOrUndefined } from 'util';
import { EmployeeQueryRepository } from '../employee/repository/query.repository';
import { ErrorConst } from '../shared/constant/error.const';
import _ = require('lodash');
import { CLeadQueryRepository } from '../c-lead.queryside/repository/query.repository';
import { IResultPaging } from '../shared/interfaces/result-paging.interface';
import { CommonConst } from '../shared/constant/common.const';
import { CmdPatternConst } from '../shared/constant/cmd-pattern.const';
import { OrgchartClient } from '../mgs-sender/orgchart.client';
import { PropertyClient } from '../mgs-sender/property.client';
import { DemandClient } from '../mgs-sender/demand.client';
import { TransactionTypeEnum } from '../shared/enum/transaction-type.enum';
import { ILeadDocument } from './interfaces/document.interface';
import { HistoryImportQueryRepository } from '../history-import/repository/query.repository';
import { HistoryImportService } from '../history-import/service';
import { ExploitEnum } from '../shared/enum/exploit.enum';
import { groupBy, intersection } from 'lodash';
import { ReportExploitationFilter } from '../shared/interfaces/queryLead.interface';

const timezone = require('moment-timezone');
const moment = require('moment');
const momentTz = require('moment-timezone');
import { Workbook, Worksheet } from 'exceljs';
import { StsClient } from '../mgs-sender/sts.client';
import { AccountInfoUtils, AwesomeLogger, BaseService, CmdPatternConst as cmd2, ErrorService } from '../../../shared-modules';
import { EmployeeClient } from '../mgs-sender/employee.client';

const validReportStatus = [ExploitEnum.DONE, ExploitEnum.CANCEL];
const validListStatus = [
  ExploitEnum.ASSIGN,
  // ExploitEnum.REASSIGN,
  ExploitEnum.PROCESSING,
  ExploitEnum.DONE,
  ExploitEnum.CANCEL,
];

@Injectable()
export class LeadService extends BaseService {

  private readonly logger = new AwesomeLogger(LeadService.name);

  constructor(
    private readonly repository: LeadQueryRepository,
    private readonly repositoryClead: CLeadQueryRepository,
    private readonly historyRepository: LeadHistoryQueryRepository,
    private readonly employeeRepository: EmployeeQueryRepository,
    private readonly orgchartClient: OrgchartClient,
    private readonly propertyClient: PropertyClient,
    private readonly demandClient: DemandClient,
    private readonly stsClient: StsClient,
    private readonly empClient: EmployeeClient,
    private readonly historyImportService: HistoryImportService,
    public readonly errorService: ErrorService
  ) {
    super(errorService);
  }

  async deleteMany() {
    this.repository.deleteMany();
  }

  async getAllByCustomer(customer, timezoneclient: string) {
    const entities = await this.repository.getByCustomer(customer.id);
    const histories = await this.historyRepository.getHistoryByCustomer(customer.id);
    const list = [...entities, ...histories];
    return await list.map(lead => {
      return this.convertDto(lead, timezoneclient);
    });
  }

  async getAll(timezoneclient: string): Promise<ILead[]> {
    const entities = await this.repository.findAll();
    return await entities.map(lead => {
      return this.convertDto(lead, timezoneclient);
    });
  }

  async getAllCommon(timezoneclient: string, query): Promise<ILead[]> {
    const entities = await this.repository.findAllCommon(query);
    return await entities.map(lead => {
      return this.convertDto(lead, timezoneclient);
    });
  }

  async getAllAdvising(timezoneclient: string): Promise<ILead[]> {
    const entities = await this.repository.findAllAdvising();
    return await entities.map(lead => {
      return this.convertDto(lead, timezoneclient);
    });
  }

  async listAllCommon(user: any, timezoneclient: string, query: any = {}): Promise<any> {
    let pageSize = parseInt(query["pageSize"]) || 10;
    let page = parseInt(query["page"]) || 1;
    return this.repository.listAllCommon(user, page, pageSize, query)
      .then(async (res: any) => {
        if (res && res[0]) {
          // Get all related accounts
          const rows = res[0].rows;
          const mappedRows = await AccountInfoUtils.mapAccountInfo(rows, this.stsClient, {
            includeCreatedBy: true,
            includeModifiedBy: true,
            replaceIds: true
          });

          const total = res[0].totalCount.length > 0 ? res[0].totalCount[0].count : 0;
          return {
            rows: mappedRows.map((lead) => this.convertDto(lead, timezoneclient)),
            page,
            pageSize,
            total,
            totalPages: Math.floor((total + pageSize - 1) / pageSize)
          };
        } else {
          return {
            rows: [],
            page,
            pageSize,
            total: 0,
            totalPages: 0
          };
        }
      });
  }

  async getAllPrimary(user, query: any, timezoneclient: string) {
    const page = parseInt(query["page"]) || 1;
    const pageSize = parseInt(query["pageSize"]) || 10;
    const entities = await this.repository.findAllPrimary(user, query);
    if (entities && entities.length > 0) {

      // Get all related accounts
      const rows = entities[0].rows;
      const mappedRows = await AccountInfoUtils.mapAccountInfo(rows, this.stsClient, {
        includeCreatedBy: true,
        includeModifiedBy: true,
        replaceIds: true
      });

      const total = entities[0].totalCount && entities[0].totalCount.length > 0 ? entities[0].totalCount[0].count : 0;
      const totalPages = Math.floor((total + pageSize - 1) / pageSize);
      return {
        page,
        pageSize,
        total,
        totalPages,
        rows: mappedRows,
      }
    } else {
      return {
        page,
        pageSize,
        total: 0,
        totalPages: 0,
        rows: [],
      }
    }
  }

  async getLeadsToDeliver(user: any, query: any) {
    const pageSize = parseInt(query["pageSize"]) || 10;
    const page = parseInt(query["page"]) || 1;
    return this.repository.getLeadsToDeliver(user, page, pageSize, query)
      .then(async (res: any) => {
        // Get all related accounts
        const rows = res[0]?.rows || [];
        const mappedRows = await AccountInfoUtils.mapAccountInfo(rows, this.stsClient, {
          includeCreatedBy: true,
          includeModifiedBy: true,
          replaceIds: true
        });

        if (res && res[0]) {
          const total = res[0].totalCount.length > 0 ? res[0].totalCount[0].count : 0;
          return {
            rows: mappedRows,
            page,
            pageSize,
            total,
            totalPages: Math.floor((total + pageSize - 1) / pageSize)
          };
        } else {
          return {
            rows: [],
            page,
            pageSize,
            total: 0,
            totalPages: 0
          };
        }
      });
  }

  async getAllByPos(posId: string, timezoneclient: string) {
    const entities = await this.repository.getAllByPos(posId);

    return await entities.map(lead => {
      return this.convertDto(lead, timezoneclient);
    });
  }

  async getLeadById(leadId: string, timezoneclient: string): Promise<any> {
    if (_.isEmpty(leadId)) {
      return this.getResponse('LEADE0002');
    }

    const entity = await this.repository.findLeadById(leadId);
    const lead = await this.convertDto(entity, timezoneclient);

    // get account id of takeCare
    const employee = await this.empClient.sendDataPromise({ where: { id: lead.takeCare.id } }, cmd2.EMPLOYEE.LISTENER.GET_BY_QUERY);
    lead.takeCare['accountId'] = employee[0]?.account?.id || '';

    const mappedLead = await AccountInfoUtils.mapSingleAccountInfo(lead, this.stsClient, {
      includeCreatedBy: true,
      includeModifiedBy: true
    });


    this.logger.debug('mappedLead', mappedLead);

    return mappedLead;
  }

  async getLeadsProcessBy(user, timezoneclient: string, type: string) {

    // const staffIds = employee.staffIds;
    const entities = await this.repository.findLeadsProcessBy(user, type);

    if (entities.others) {
      entities.others = await entities.others.map(lead => {
        return this.convertDto(lead, timezoneclient);
      });
    } else {
      entities.others = [];
    }

    if (entities.owners) {
      entities.owners = await entities.owners.map(lead => {
        return this.convertDto(lead, timezoneclient);
      });
    } else {
      entities.owners = [];
    }
    return entities;
  }

  async getSummary(user: any) {
    const employee = await this.employeeRepository.findOne({ id: user.id });

    if (isNullOrUndefined(employee)) {
      throw new NotFoundException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, 'employee') });
    }

    const promiseAll = [];

    promiseAll.push(this.repository.countLifeCycleStatusByEmployee(user.id, LifeCycleStatusEnum.REMOVED));
    promiseAll.push(this.historyRepository.countStatusToDayByEmployee(user.id, LifeCycleStatusEnum.COMPLETED));

    promiseAll.push(this.repository.countLifeCycleStatusByEmployee(user.id, LifeCycleStatusEnum.ASSIGNED));
    promiseAll.push(this.repository.countLifeCycleStatusByEmployee(user.id, LifeCycleStatusEnum.PENDING));
    promiseAll.push(this.repository.countLifeCycleStatusByEmployee(user.id, LifeCycleStatusEnum.PROCESSING));

    promiseAll.push(this.historyRepository.countStatusForLeads(employee.leadsPullLatest, LifeCycleStatusEnum.REMOVED));
    promiseAll.push(this.historyRepository.countStatusForLeads(employee.leadsPullLatest, LifeCycleStatusEnum.COMPLETED));

    promiseAll.push(this.repository.countLifeCycleStatusForLeads(employee.leadsPullLatest, LifeCycleStatusEnum.ASSIGNED));
    promiseAll.push(this.repository.countLifeCycleStatusForLeads(employee.leadsPullLatest, LifeCycleStatusEnum.PENDING));
    promiseAll.push(this.repository.countLifeCycleStatusForLeads(employee.leadsPullLatest, LifeCycleStatusEnum.PROCESSING));

    return await Promise.all(promiseAll).then(res => {
      const result = {
        removed: res[0],
        compeleted: res[1],
        assigned: res[2],
        pending: res[3],
        processing: res[4],
        total: res[0] + res[1] + res[2] + res[3] + res[4],
        timePullLatest: employee.timePullLatest,
        isPenalty: employee.isPenalty,
        removedLatest: res[5],
        completedLatest: res[6],
        assignedLatest: res[7],
        pendingLatest: res[8],
        processingLatest: res[9],
      };

      return result;
    });
  }

  convertDto(lead, client: string) {
    const timezoneclient = isNullOrUndefined(client) ? lead.timezoneclient : client;
    const mapper = new LeadMapper(timezoneclient);
    return mapper.enityToDto(lead);
  }

  async countAllTickets() {
    const countCLead: number = await this.repositoryClead.countAll();
    const countLead: number = await this.repository.count();
    return { 'countAllTickets': countCLead + countLead };

  }

  async countProcessCancelTicketsByUser(arrayUser, userLogin, from, to) {
    let query = {};
    if (!_.isEmpty(from) || !_.isEmpty(to)) {
      query["createdDate"] = {};
      if (!_.isEmpty(from)) {
        query["createdDate"]["$gte"] = moment(!isNaN(from) ? parseInt(from) : from).clone().startOf('day').toDate();
      }
      if (!_.isEmpty(to)) {
        query["createdDate"]["$lte"] = moment(!isNaN(to) ? parseInt(to) : to).clone().endOf('day').toDate();
      }
    }

    if (!isNullOrUndefined(arrayUser)) {
      query["processBy"] = {};
      query["processBy"]["$in"] = arrayUser;
    } else {
      if (!isNullOrUndefined(userLogin)) {
        const user = await this.employeeRepository.findOne({ id: userLogin });
        query["processBy"] = {};
        query["processBy"]["$in"] = user.staffIds;
      }
    }
    const demandProcess = await this.repository.countProcessTicketsDemandByUser(query);
    const consignmentProcess = await this.repository.countProcessTicketsConsignmentByUser(query);
    const demandCancel = await this.historyRepository.countCancelTicketsDemandByUser(query);
    const consignmentCancel = await this.historyRepository.countCancelTicketsConsignmentByUser(query);
    return {
      'countCancel': { 'demand': demandCancel, 'consignment': consignmentCancel },
      'countProcess': { 'demand': demandProcess, 'consignment': consignmentProcess }
    };
  }
  /**
   *
   * @param loggerUser
   * @param query
   */
  async listLeadsProcessBy(loggerUser: any, timezoneclient?: any, query: any = {},) {
    if (!_.isEmpty(query.page) && !_.isEmpty(query.pageSize)) {
      return Promise.all([
        await this.repository.listLeadsProcessBy(loggerUser, query)
          .then((res) => _.map(res, (lead: any) => this.convertDto(lead, timezoneclient))),
        await this.repository.countLeadsProcessBy(loggerUser, query)
      ])
        .then((res) => {
          const pageSize = Number(query.pageSize) || 10;
          return ({
            rows: res[0],
            total: res[1],
            page: Number(query.page) || 1,
            pageSize,
            totalPages: Math.floor((res[1] + pageSize - 1) / pageSize)
          });
        });
    } else {
      return this.getLeadsProcessBy(loggerUser, timezoneclient, query.type).then((rows) => ({ rows }));
    }
  }



  /**
   * Lấy danh sách yêu cầu tư vấn dịch vụ
   * @param timezoneclient
   * @param query
   */
  async listAllAdvising(timezoneclient: string, query: any = {}): Promise<any> {
    if (!_.isEmpty(query.page) && !_.isEmpty(query.pageSize)) {
      const page: number = Number(query.page) || 1;
      const pageSize: number = Number(query.pageSize) || 10;

      return Promise.all([
        await this.repository.listAllAdvising(page, pageSize, query)
          .then((entities) => _.map(entities, (lead) => this.convertDto(lead, timezoneclient))),
        await this.repository.findAllAdvising()
      ])
        .then((res) => ({
          rows: res[0],
          total: res[1].length,
          page, pageSize,
          totalPages: Math.floor((res[1].length + pageSize - 1) / pageSize)
        }));
    } else {
      return this.getAllAdvising(timezoneclient).then((rows) => ({ rows }));
    }
  }
  /**
   *
   * @param customerIds
   */
  getLeadByCustomerId(customerIds: string[] = [], mapping: boolean = false) {
    return this.repository.findLeadByCustomerId(customerIds, mapping);
  }

  async countLeadsByFilter(user: any, params?: any): Promise<any> {
    let query: any = {
    };
    let matchInPool: any = {};
    let matchNotInPool: any = {};
    let poss: any[] = await this.orgchartClient.sendDataPromise({ model: user }, CmdPatternConst.SERVICE.LEAD.GET_POS_ONLY_MANGER);
    let matchPosInPool: string[] = poss.map(pos => pos.id);
    if (!_.isEmpty(params.createdFrom) || !_.isEmpty(params.createdTo)) {
      matchNotInPool.assignedDate = {};
      if (!_.isEmpty(params.createdFrom)) {
        // tslint:disable-next-line: radix
        matchNotInPool.assignedDate.$gte = new Date(parseInt(params.createdFrom));
      }
      if (!_.isEmpty(params.createdTo)) {
        // tslint:disable-next-line: radix
        matchNotInPool.assignedDate.$lte = new Date(parseInt(params.createdTo));
      }
    }
    if (!_.isEmpty(params['status'])) {
      query.lifeCycleStatus = params['status'];
    }
    if (!_.isEmpty(params['id'])) {
      query.id = params['id'];
    }
    if (!_.isEmpty(params.exchangeId)) {
      matchNotInPool['employeeTakeCare.pos.parentId'] = params.exchangeId;
      let pos: any[] = [];
      const request = {
        model: { pos: { id: params.exchangeId } },
        action: CommonConst.AGGREGATES.LEAD.GET_ORG_CHILDS
      };
      pos = await this.orgchartClient.sendDataPromise(request, CmdPatternConst.SERVICE.LEAD.LISTENER);
      matchInPool['pos.id'] = { $in: pos.map(i => i.id) };
    }
    if (!_.isEmpty(params.posId)) {
      matchNotInPool['employeeTakeCare.pos.id'] = params.posId;
      matchInPool['pos.id'] = { $in: [params.posId] };
    }

    if (!_.isEmpty(params.employeeId)) {
      // let userInfo = await this.employeeRepository.findOne({ id: params["employeeId"] });
      // query['$or'] = [{ processBy: params["employeeId"] }, { processBy: { $in: userInfo.staffIds || [] } }];
      query['employeeTakeCare.id'] = params.employeeId;
    }
    // if (_.isEmpty(params["posId"]) && _.isEmpty(params["exchangeId"]) && _.isEmpty(params["employeeId"])) {
    //     let userInfo = await this.employeeRepository.findOne({ id: user.id });
    //     query['$or'] = [{ processBy: user.id }, { processBy: { $in: userInfo.staffIds || [] } }];
    // }
    query['$or'] = [{
      lifeCycleStatus: { $in: [LifeCycleStatusEnum.ASSIGNED, LifeCycleStatusEnum.PROCESSING, LifeCycleStatusEnum.COMPLETED] },
      ...matchNotInPool
    }, {
      lifeCycleStatus: LifeCycleStatusEnum.IN_POOL,
      ...matchInPool
    }];
    return await Promise.all([
      await this.repository.countReport(user, query, matchPosInPool),
      await this.historyRepository.countReport(user, query, []),
      await this.repository.countReportInPool(user, query, matchPosInPool)
    ])
      .then((res) => {
        const resAll = [...res[0], ...res[1]];
        const notAssign = res[2].filter(lead => lead.lifeCycleStatus === LifeCycleStatusEnum.IN_POOL); //YCTV chưa phân bổ
        const assign = [...res[0].filter(lead => (lead.lifeCycleStatus === LifeCycleStatusEnum.ASSIGNED || lead.lifeCycleStatus === LifeCycleStatusEnum.PROCESSING)),
        ...res[1].filter(lead => (lead.lifeCycleStatus === LifeCycleStatusEnum.COMPLETED || lead.lifeCycleStatus === LifeCycleStatusEnum.REMOVED))]; //YCTV đã phân bổ

        const processing = assign.filter(lead => lead.lifeCycleStatus === LifeCycleStatusEnum.PROCESSING); // YCTV đang xử lý
        const processed = assign.filter(lead => lead.lifeCycleStatus === LifeCycleStatusEnum.COMPLETED); // YCTV đã được xử lý
        const pending = assign.filter(lead => lead.lifeCycleStatus === LifeCycleStatusEnum.ASSIGNED); // YCTV chờ xử lý
        const removed = assign.filter(lead => lead.lifeCycleStatus === LifeCycleStatusEnum.REMOVED); // YCTV đã huỷ

        const demandNotAssign = notAssign.filter(lead => (lead.type === TransactionTypeEnum.buy || lead.type === TransactionTypeEnum.rent));
        const demand = assign.filter(lead => (lead.type === TransactionTypeEnum.buy || lead.type === TransactionTypeEnum.rent));
        const demandProcessing = processing.filter(lead => (lead.type === TransactionTypeEnum.buy || lead.type === TransactionTypeEnum.rent));
        const demandProcessed = processed.filter(lead => (lead.type === TransactionTypeEnum.buy || lead.type === TransactionTypeEnum.rent));
        const demandPending = pending.filter(lead => (lead.type === TransactionTypeEnum.buy || lead.type === TransactionTypeEnum.rent));
        const demandRemoved = removed.filter(lead => (lead.type === TransactionTypeEnum.buy || lead.type === TransactionTypeEnum.rent));

        const consignmentNotAssign = notAssign.filter(lead => (lead.type === TransactionTypeEnum.sell || lead.type === TransactionTypeEnum.lease));
        const consignment = assign.filter(lead => (lead.type === TransactionTypeEnum.sell || lead.type === TransactionTypeEnum.lease));
        const consignmentProcessing = processing.filter(lead => (lead.type === TransactionTypeEnum.sell || lead.type === TransactionTypeEnum.lease));
        const consignmentProcessed = processed.filter(lead => (lead.type === TransactionTypeEnum.sell || lead.type === TransactionTypeEnum.lease));
        const consignmentPending = pending.filter(lead => (lead.type === TransactionTypeEnum.sell || lead.type === TransactionTypeEnum.lease));
        const consignmentRemoved = removed.filter(lead => (lead.type === TransactionTypeEnum.sell || lead.type === TransactionTypeEnum.lease));
        const advisingNotAssign = notAssign.filter(lead => (lead.type === 'ADVISING'));
        const advising = assign.filter(lead => (lead.type === 'ADVISING'));
        const advisingProcessing = processing.filter(lead => (lead.type === 'ADVISING'));
        const advisingProcessed = processed.filter(lead => (lead.type === 'ADVISING'));
        const advisingPending = pending.filter(lead => (lead.type === 'ADVISING'));
        const advisingRemoved = removed.filter(lead => (lead.type === 'ADVISING'));

        return {
          all: resAll.length,
          notAssign: notAssign.length,
          assign: assign.length,
          processed: processed.length,
          processing: processing.length,
          unprocess: resAll.length - (processed.length + processing.length),
          pending: pending.length,
          removed: removed.length,
          demand: demand.length,
          demandNotAssign: demandNotAssign.length,
          demandProcessed: demandProcessed.length,
          demandProcessing: demandProcessing.length,
          demandUnprocess: demand.length - (demandProcessed.length + demandProcessing.length),
          demandPending: demandPending.length,
          demandRemoved: demandRemoved.length,
          consignmentNotAssign: consignmentNotAssign.length,
          consignment: consignment.length,
          consignmentProcessed: consignmentProcessed.length,
          consignmentProcessing: consignmentProcessing.length,
          consignmentUnprocess: consignment.length - (consignmentProcessed.length + consignmentProcessing.length),
          consignmentPending: consignmentPending.length,
          consignmentRemoved: consignmentRemoved.length,
          advisingNotAssign: advisingNotAssign.length,
          advising: advising.length,
          advisingProcessing: advisingProcessing.length,
          advisingProcessed: advisingProcessed.length,
          advisingUnProcess: advising.length - (advisingProcessed.length + advisingProcessing.length),
          advisingPending: advisingPending.length,
          advisingRemoved: advisingRemoved.length,
        };
      });
  }
  async pagingLeadsByFilter(user: any, params?: any, timezoneclient?: string): Promise<IResultPaging> {
    let query: any = {};
    const matchNotInPool: any = {};
    const matchInPool: any = {};
    const poss: any[] = await this.orgchartClient.sendDataPromise({ model: user }, CmdPatternConst.SERVICE.LEAD.GET_POS_ONLY_MANGER);
    const matchPosInPool: string[] = poss.map(pos => pos.id);
    if (!_.isEmpty(params.createdFrom) || !_.isEmpty(params.createdTo)) {
      query.assignedDate = {};
      if (!_.isEmpty(params.createdFrom)) {
        query.assignedDate.$gte = new Date(parseInt(params.createdFrom));
      }
      if (!_.isEmpty(params.createdTo)) {
        query.assignedDate.$lte = new Date(parseInt(params.createdTo));
      }
    }
    let isPaging = false;
    if (!_.isEmpty(params['page']) || !_.isEmpty(params['pageSize'])) {
      var page = parseInt(params['page']) || 1;
      query.page = page;
      var pageSize = parseInt(params['pageSize']) || 10;
      query.pageSize = pageSize;
      isPaging = true;

    }
    if (!_.isEmpty(params['status'])) {
      query.lifeCycleStatus = { $exists: true, $nin: [null, ''], $eq: params['status'] };
    }

    if (!_.isEmpty(params['employeeId'])) {
      let userInfo = await this.employeeRepository.findOne({ id: params['employeeId'] });
      query['$or'] = [{ processBy: params['employeeId'] }, { processBy: { $in: userInfo.staffIds || [] } }];
    } else if (!_.isEmpty(params['posId'])) {
      query['pos.id'] = params['posId']
    } else if (!_.isEmpty(params['exchangeId'])) {
      let pos = null;
      try {
        const request = {
          model: { pos: { id: params["exchangeId"] } },
          action: CommonConst.AGGREGATES.LEAD.GET_ORG_CHILDS
        };
        pos = await this.orgchartClient.sendDataPromise(request, CmdPatternConst.SERVICE.LEAD.LISTENER);
        query["pos.id"] = { $in: pos.map(i => i.id) };
      } catch (err) {
      }
    } else {
      let userInfo = await this.employeeRepository.findOne({ id: user.id });
      query['$or'] = [{ processBy: user.id }, { processBy: { $in: userInfo.staffIds || [] } }];
    }
    if (!_.isEmpty(params.exchangeId)) {
      matchNotInPool['employeeTakeCare.pos.parentId'] = params.exchangeId;
      let pos: any[] = [];
      const request = {
        model: { pos: { id: params.exchangeId } },
        action: CommonConst.AGGREGATES.LEAD.GET_ORG_CHILDS
      };
      pos = await this.orgchartClient.sendDataPromise(request, CmdPatternConst.SERVICE.LEAD.LISTENER);
      matchInPool['pos.id'] = { $in: pos.map(i => i.id) };
    }
    if (!_.isEmpty(params.posId)) {
      matchNotInPool['employeeTakeCare.pos.id'] = params.posId;
      matchInPool['pos.id'] = { $in: [params.posId] };
    }
    if (!_.isEmpty(params.employeeId)) {
      query['employeeTakeCare.id'] = user.id;
    }

    // query.$or = [{
    //   lifeCycleStatus: { $in: [LifeCycleStatusEnum.ASSIGNED, LifeCycleStatusEnum.PROCESSING, LifeCycleStatusEnum.COMPLETED] },
    //   ...matchNotInPool
    // }];
    // if (_.isEmpty(params["posId"]) && _.isEmpty(params["exchangeId"]) && _.isEmpty(params["employeeId"])) {
    // let userInfo = await this.employeeRepository.findOne({ id: user.id });
    // query['$or'] = [{ processBy: user.id }, { processBy: { $in: userInfo.staffIds || [] } }];
    // }

    let repository: any = this.repository;
    if (!_.isEmpty(params.isHistory) && params.isHistory === 'true') {
      repository = this.historyRepository;
    }

    console.log('query', query);
    let results = null;
    if (isPaging) {
      results = await Promise.all([
        await repository.filterReport(user, query, matchPosInPool, true),
        await repository.filterReport(user, query, matchPosInPool, false),
      ])
        .then((res) => {
          return {
            rows: res[0],
            page,
            pageSize,
            total: res[1].length,
            totalPages: Math.floor((res[1].length + pageSize - 1) / pageSize)
          };
        });
    } else {
      results = await Promise.all([await repository.filterReport(user, query, false)])
        .then((res) => {
          return {
            rows: res[0],
            page,
            pageSize,
            total: res[0].length,
            totalPages: null
          };
        });
    }

    let leads: any[] = [];
    for (const lead of results.rows) {
      // const joinLead = await this.joinLeadConsult(lead)
      const request = {
        model: { ticketId: lead.id },
        action: ''
      };
      let processedTicketCode = '';
      let processedTicketDate = null;
      //
      if (lead.type === TransactionTypeEnum.buy || lead.type === TransactionTypeEnum.rent) {
        request.action = CommonConst.AGGREGATES.LEAD.GET_DEMAND;
        const demand = await this.demandClient.sendDataPromise(request, CmdPatternConst.SERVICE.LEAD.LISTENER);
        processedTicketCode = demand ? demand.code : '';
        processedTicketDate = demand ? demand.createdDate : null;
      } else if (lead.type === TransactionTypeEnum.sell || lead.type === TransactionTypeEnum.lease) {
        request.action = CommonConst.AGGREGATES.LEAD.GET_CONSIGNMENT;
        const consignment = await this.propertyClient.sendDataPromise(request, CmdPatternConst.SERVICE.LEAD.LISTENER);
        processedTicketCode = consignment ? consignment.code : '';
        processedTicketDate = consignment ? consignment.createDate : null;
      }
      let arrayReason = !_.isEmpty(lead.processedHistory) ? lead.processedHistory : [];
      let arrayCause: any[] = [];
      // tslint:disable-next-line: prefer-for-of
      for (let index = 0; index < arrayReason.length; index++) {
        const element = arrayReason[index];
        if (!_.isEmpty(element.causeReject) && _.isArray(element.causeReject)) {
          element.causeReject.forEach(elementCause => {
            arrayCause.push(elementCause);
          });
        }
      }
      let reasons: string[] = !_.isEmpty(arrayCause) && arrayCause !== undefined ? arrayCause : [];
      const ticket = { processedTicketCode, processedTicketDate, reason: reasons.join(', '), ...lead };
      leads.push(ticket);
    }
    results.rows = leads;
    results.rows = await leads.map(lead => {
      return this.convertDto(lead, timezoneclient);
    });
    return results;
  }

  /**
   * Tính thời gian còn lại của lead/ticket
   * Return: thời gian còn lại tính theo giây
   * @param leadId
   */
  async getLeadRemainTime(leadId: string): Promise<number> {
    const leadDocument: ILeadDocument = await this.repository.findOne({ id: leadId });
    if (!leadDocument) {
      return null;
    }
    const lead: ILead = leadDocument.toObject();
    if (!lead.timeOut) {
      return null;
    }
    const now = timezone().tz(lead.timezoneclient);
    const expiredTime = timezone(lead.timeOut).tz(lead.timezoneclient);
    return expiredTime.diff(now, 'second') as number;
  }

  async countLeadReadyForAssigningByPOS(posId: string, type?: string): Promise<number> {
    return await this.repository.countLeadReadyForAssigningByPOS(posId, type);
  }

  async countLeadReadyForAssigningByEmp(empId: string, type: string): Promise<number> {
    const employee = await this.employeeRepository.findOne({ id: empId });
    return await this.repository.countLeadReadyForAssigningByEmp(employee, type);
  }

  findLeadByEmployee(employeeId: string[]) {
    return this.repository.findLeadByEmployeeId(employeeId);
  }
  listHistory(user, query) {
    return this.historyImportService.listAll(user, query);
  }

  async countPrimaryLeads(user: any, query: any) {
    const pageSize = parseInt(query["pageSize"]) || 10;
    const entities = await this.repository.countPrimaryLead(user, query);
    if (entities && entities.length > 0) {
      const total = entities[0].totalCount && entities[0].totalCount.length > 0 ? entities[0].totalCount[0].count : 0;
      const totalPages = Math.floor((total + pageSize - 1) / pageSize);
      return {
        rows: entities[0].rows,
        total,
        totalPages
      }
    }
  }

  async getExploitationReportByStatus(user: any, status: ExploitEnum, query: any) {
    const page = parseInt(query.page, 10) || 1;
    const pageSize = parseInt(query.pageSize, 10) || 10;

    const [rows, total] = await this.repository.getLeadByStatus(user, status, query, page, pageSize);

    const mappedRows = await AccountInfoUtils.mapAccountInfo(rows, this.stsClient, {
      includeCreatedBy: false,
      includeModifiedBy: true,
      replaceIds: true
    });

    const totalPages = Math.ceil(total / pageSize);

    return {
      page,
      totalPages,
      pageSize,
      total,
      rows: mappedRows,
    };
  }

  async exportExploitationReportAsExcel(user: any, query: any) {
    const leads = {};

    let statuses = query.status ? query.status.split(',') : [ExploitEnum.ASSIGN, ExploitEnum.PROCESSING, ExploitEnum.DONE, ExploitEnum.CANCEL];

    // for (const status of statuses) {
    //     leads[status] = await this.repository.getLeadsByStatusForExport(user, status, query);
    // }
    const arr = await Promise.all(statuses.map(status => this.repository.getLeadsByStatusForExport(user, status, query)));

    statuses.forEach((e, index) => leads[e] = arr[index]);

    return leads;
  }

  async downloadExploitationReportAsExcel(user: any, query: any) {
    const res = {};

    let statuses = query.status ? query.status.split(',') : [ExploitEnum.ASSIGN, ExploitEnum.PROCESSING, ExploitEnum.DONE, ExploitEnum.CANCEL];

    const arr = await Promise.all(statuses.map(status => this.repository.getLeadsByStatusForExport(user, status, query)));

    statuses.forEach((e, index) => res[e] = arr[index]);

    const workbook: Workbook = new Workbook();

    statuses.forEach(key => {
      let sheetName = '';
      switch (key) {
        case ExploitEnum.ASSIGN:
          sheetName = 'Đã phân bổ';
          break;
        case ExploitEnum.PROCESSING:
          sheetName = 'Đang khảo sát';
          break;
        case ExploitEnum.DONE:
          sheetName = 'Đã hoàn thành';
          break;
        case ExploitEnum.CANCEL:
          sheetName = 'Đã trả về';
          break;
      }

      const sheet: Worksheet = workbook.addWorksheet(sheetName);

      const headerCols = [
        'STT',
        'Mã Lead',
        'Tên',
        'Mã KHTN',
        'Số điện thoại',
        'Sàn giao dịch',
        'Nhân viên',
        'Thời gian cập nhật'
      ];

      const subHeaderCols = [];
      if (key === ExploitEnum.DONE) {
        headerCols.push('Khảo sát');
        for (const item of res[key]) {
          if (item.surveys && item.surveys.length) {
            for (const survey of item.surveys) {
              if (!subHeaderCols.includes(survey.name)) {
                subHeaderCols.push(survey.name);
              }
            }
          }
        }
      }

      const header = sheet.addRow(headerCols);
      header.eachCell((cell, number) => {
        // Merge header cols
        if (number < headerCols.length && subHeaderCols.length) {
          sheet.mergeCells(
            parseInt(cell.row),
            parseInt(cell.col),
            parseInt(cell.row) + 1,
            parseInt(cell.col)
          );
        }

        // Add subheader for surveys
        if (number === headerCols.length && subHeaderCols.length) {
          let colNum = number;
          for (const subHeader of subHeaderCols) {
            const customCell = sheet.getCell(
              parseInt(cell.row) + 1,
              colNum
            );

            customCell.value = subHeader;
            customCell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'FFFFFF00' },
              bgColor: { argb: 'FF0000FF' }
            };

            customCell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' }
            };

            sheet.getColumn(colNum).width = 30;
            colNum++;
          }

          sheet.mergeCells(
            parseInt(cell.row),
            parseInt(cell.col),
            parseInt(cell.row),
            parseInt(cell.col) + subHeaderCols.length - 1
          );
          cell.style.alignment = { horizontal: 'center' };
        }

        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFFFFF00' },
          bgColor: { argb: 'FF0000FF' }
        };

        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };

        cell.font = { bold: true };
      });

      for (const item of res[key]) {
        const data = [
          item.serial || ' ',
          item.code || ' ',
          item.name || ' ',
          item.demandCustomer && item.demandCustomer.customerCode
            ? item.demandCustomer.customerCode
            : ' ',
          item.phone
            ? item.visiblePhone ||
              (item.importedBy && item.importedBy.id === user.id)
              ? item.phone
              : this.hiddenPhone(item.phone)
            : ' ',
          item.pos || ' ',
          item.takeCare || ' ',
          this.getDate(item.updatedDate) || ' '
        ];

        for (const subHeader of subHeaderCols) {
          if (item.surveys && item.surveys.length) {
            const survey = item.surveys.find(s => s.name === subHeader);
            if (survey && survey.valueName) {
              if (survey.valueName instanceof Array) {
                data.push(survey.valueName.join(', ') || ' ');
              } else {
                data.push(survey.valueName);
              }
            } else {
              data.push(' ');
            }
          }
        }

        const row = sheet.addRow(data);

        row.eachCell((cell, number) => {
          cell.alignment = { wrapText: true };
          sheet.getColumn(cell.col).width = 40;

          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
        });
      }

      sheet.getColumn('A').width = 10;
      sheet.getColumn('B').width = 20;
      sheet.getColumn('C').width = 30;
      sheet.getColumn('D').width = 30;
      sheet.getColumn('E').width = 30;
      sheet.getColumn('F').width = 30;
      sheet.getColumn('G').width = 20;
      sheet.getColumn('H').width = 30;
    });

    return await workbook.xlsx.writeBuffer();
  }

  async downloadHistoryExploitationReportAsExcel(user: any, query: any) {
    const res = {};

    let statuses = query.status ? query.status.split(',') : [ExploitEnum.ASSIGN, ExploitEnum.PROCESSING, ExploitEnum.DONE, ExploitEnum.CANCEL];

    const arr: any = await Promise.all(statuses.map(status => this.repository.getLeadsByStatusForExport(user, status, query)));

    statuses.forEach((e, index) => res[e] = arr[index]);

    const workbook: Workbook = new Workbook();

    statuses.forEach(key => {
      let sheetName = '';
      switch (key) {
        case ExploitEnum.ASSIGN:
          sheetName = 'Đã phân bổ';
          break;
        case ExploitEnum.PROCESSING:
          sheetName = 'Đang khảo sát';
          break;
        case ExploitEnum.DONE:
          sheetName = 'Đã hoàn thành';
          break;
        case ExploitEnum.CANCEL:
          sheetName = 'Đã trả về';
          break;
      }

      const sheet: Worksheet = workbook.addWorksheet(sheetName);

      const headerCols = ['STT', 'Mã Lead', 'Tên', 'Mã KHTN', 'Số điện thoại',
        'Sàn giao dịch', 'Nhân viên', 'Thời gian cập nhật'];

      if(query.history) {
        headerCols.push('Lịch sử cuộc gọi', 'Lịch sử khai thác');
      }


      let maxLenghOfExploitHistory = 0;

      const header = sheet.addRow(headerCols);
      header.eachCell((cell, number) => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFFFFF00' },
          bgColor: { argb: 'FF0000FF' },
        };

        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };

        cell.font = { bold: true };
      });

      for (const item of res[key]) {
        const data = [
          item.serial || ' ',
          item.code || ' ',
          item.name || ' ',
          item.demandCustomer && item.demandCustomer.customerCode ? item.demandCustomer.customerCode : ' ',
          item.phone ? ((item.visiblePhone) || (item.importedBy && item.importedBy.id === user.id) ? item.phone : this.hiddenPhone(item.phone)) : ' ',
          item.pos || ' ',
          item.takeCare || ' ',
          this.getDate(item.updatedDate) || ' ',
          
        ];
        
        if (query.history && item.exploitHistory && item.exploitHistory.length) {
          item?.callHistory.map((data: any) => data && data?.answerTime !== 0 ?
            `${momentTz(data.updateDate).tz('Asia/Ho_Chi_Minh').format('HH:mm DD/MM/YYYY')} - ${data?.answerTime} giây \n` : ' ').toString()
          
            const historyCells = [];
          for (const history of item.exploitHistory) {
            if ([
              ExploitEnum.ASSIGN,
              // ExploitEnum.REASSIGN
            ].includes(history.status)) {
              historyCells.push(`${history.takeCareInfo ? history.takeCareInfo.name : ''}\n${momentTz(history.updatedAt).tz('Asia/Ho_Chi_Minh').format('HH:mm DD/MM/YYYY')}` || ' ');
            }

            if ([ExploitEnum.RENEW].includes(history.status)) {
              historyCells[historyCells.length - 1] += ` - ${momentTz(history.updatedAt).tz('Asia/Ho_Chi_Minh').format('HH:mm DD/MM/YYYY')}`;
            }

            if ([ExploitEnum.DONE, ExploitEnum.CANCEL].includes(history.status)) {
              historyCells[historyCells.length - 1] += ` - ${momentTz(history.updatedAt).tz('Asia/Ho_Chi_Minh').format('HH:mm DD/MM/YYYY')}`;
              break;
            }
          }

          if (historyCells.length && maxLenghOfExploitHistory < historyCells.length) {
            maxLenghOfExploitHistory = historyCells.length;
          }

          data.push.apply(data, historyCells);
        }

        const row = sheet.addRow(data);

        row.eachCell((cell, number) => {
          cell.alignment = { wrapText: true };
          sheet.getColumn(cell.col).width = 40;

          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          };
        });

      }

      if (maxLenghOfExploitHistory) {
        sheet.mergeCells(1, 10, 1, 10 + maxLenghOfExploitHistory - 1);
      }

      sheet.getColumn('A').width = 10;
      sheet.getColumn('B').width = 20;
      sheet.getColumn('C').width = 30;
      sheet.getColumn('D').width = 30;
      sheet.getColumn('E').width = 30;
      sheet.getColumn('F').width = 30;
      sheet.getColumn('G').width = 20;
      sheet.getColumn('H').width = 30;
      sheet.getColumn('I').width = 30;

    });


    return await workbook.xlsx.writeBuffer();
  }

  hiddenPhone(phone) {
    let chars = phone.split('');

    chars[chars.length] = '*';
    chars[chars.length - 1] = '*';
    chars[chars.length - 2] = '*';

    const newPhone = chars.join('');

    return newPhone;
  }
  getDate(date) {
    return moment(date).format('DD/MM/YYYY HH:mm');
  }

  async getLeads(user, useAggs = false) {
    const emp = await this.employeeRepository.findOne({ 'account.id': user.id });
    this.logger.debug('emp', emp);

    const where = {
      'takeCare.id': emp[0].id,
    };
    Object.assign(where, { exploitStatus: { $in: validListStatus } });

    this.logger.debug('where', where);
    this.logger.debug('useAggs', useAggs);

    let result: any;
    if (!useAggs) {
      result = await this.fetchLeads(where);
    } else {
      result = this.fetchLeadsWithAggs(where);
    }

    return result;
  }

  private async fetchLeads(where) {
    const data = await this.repository.findExploitLead(where);

    const filtered = data.filter((item) => {
      if (
        [ExploitEnum.DONE, ExploitEnum.CANCEL].includes(
          item.exploitStatus
        )
      ) {
        return true;
      }
      return !this.validateLeadIsExpired(item.exploitHistory.pop(), item.assignDuration);
    });

    if (!data?.length) return {};

    const r = groupBy(filtered, 'exploitStatus');
    return r;
  }

  private async fetchLeadsWithAggs(where) {
    const data = await this.repository.fetchLeadsWithAggs(where);

    const filtered = data.filter((item) => {
      if (
        [ExploitEnum.DONE, ExploitEnum.CANCEL].includes(
          item.exploitStatus
        )
      ) {
        return true;
      }
      return !this.validateLeadIsExpired(
        item.latestAssignHistory,
        item.assignDuration
      );
    });

    if (!data?.length) return {};

    const r = groupBy(filtered, 'exploitStatus');
    return r;
  }

  async getReportExploitationByEmployee(userId: string, filter: ReportExploitationFilter) {
    const { employeeId, from, to } = filter;
    const user = await this.employeeRepository.findOne({ id: userId });
    if (!user) return {};
    const where = {
      statuses: {
        $in: validReportStatus,
      },
    };

    let ids: string[] = [];

    // @TODO: implement query đệ quy
    ids = [userId];
    // if (!employeeId) {
    //   ids = [userId];
    // } else {
    //   ids =
    //     employeeId === 'all'
    //       ? user.staffIds
    //       : intersection(user.staffIds || [], [employeeId]);
    // }

    if (!ids?.length) return {};
    Object.assign(where, {
      'takeCare.id': { $in: ['fb1fcef2-a9f3-4e3a-b9e3-b8f3f95f065b'] },
    });

    const data = await this.repository.countExploitationByEmployee(where);
    console.log('where', where);
    if (!data?.length) return {};
    console.log('where', where);

    return data.reduce((all, item) => {
      const { status, date } = item._id;
      const dateData = all[status];
      if (!dateData) {
        all[status] = [{ [date]: item.count }]
      } else {
        all[status].push({ [date]: item.count })
      }
      return all;
    }, {});

  }

  async getTodayExploited(userId: string) {
    const data = await this.repository.getTodayExploited(userId);
    return data.filter(item => {
      return !this.validateExpire(item.exploitHistory, item.assignDuration);
    }).length;
  }

  private validateExpire(
    history: IExploitHistory[],
    duration: number
  ) {
    if (duration === -1) return false;
    const targetHistory = history.filter((item) =>
      [
        ExploitEnum.ASSIGN,
        // ExploitEnum.REASSIGN
      ].includes(item.status)
    );
    if (!targetHistory.length) return false;

    const time = targetHistory.pop();
    return (
      new Date().getTime() >
      (new Date(time.updatedAt).getTime() + duration * 60000)
    );
  }

  private validateLeadIsExpired(history: IExploitHistory, duration: number) {
    if (duration === -1) return false;
    if (!(
      [
        ExploitEnum.ASSIGN,
        // ExploitEnum.REASSIGN
      ].includes(history.status))) return false;

    return (new Date().getTime() > (new Date(history.updatedAt).getTime() + duration * 60000));
  }
}
