import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@nestjs/cqrs';
import { MsxLoggerService } from '../../logger/logger.service';
import { LeadQueryRepository } from '../repository/query.repository';
import { RenewLeadQueryEvent } from './query-renewLead.evt';

@EventsHandler(RenewLeadQueryEvent)
export class RenewLeadQueryEventHandler
  implements IEventHandler<RenewLeadQueryEvent> {
  private readonly context = RenewLeadQueryEventHandler.name;
  constructor(
    private readonly repository: LeadQueryRepository,
    private readonly loggerService: MsxLoggerService
  ) { }

  async handle(event: RenewLeadQueryEvent) {
    this.loggerService.log(
      this.context,
      'Async Lead Updated Query Event Handler...'
    );

    const { commandModels } = event;
    await this.repository.updateMany(commandModels);
  }
}
