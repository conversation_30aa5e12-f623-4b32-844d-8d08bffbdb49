import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@nestjs/cqrs';
import { MsxLoggerService } from '../../logger/logger.service';
import { LeadQueryRepository } from '../repository/query.repository';
import { ImportLeadQueryEvent } from './query-importLead.evt';

@EventsHandler(ImportLeadQueryEvent)
export class ImportLeadQueryEventHandler
  implements IEventHandler<ImportLeadQueryEvent> {
  private readonly context = ImportLeadQueryEventHandler.name;
  constructor(
    private readonly repository: LeadQueryRepository,
    private readonly loggerService: MsxLoggerService
  ) { }

  async handle(event: ImportLeadQueryEvent) {
    this.loggerService.log(
      this.context,
      'Async Lead Updated Query Event Handler...'
    );

    const { commandModels } = event;
    await this.repository.createMany(commandModels);
  }
}
