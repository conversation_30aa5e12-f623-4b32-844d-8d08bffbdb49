import { IE<PERSON><PERSON><PERSON><PERSON>, <PERSON>Handler } from '@nestjs/cqrs';
import { LeadCreatedQueryEvent } from './query-created.evt';
import { LeadDeletedQueryEvent } from './query-deleted.evt';
import { LeadQueryRepository } from '../repository/query.repository';
import { LeadProcessingQueryEvent } from './query-processing.evt';
import { LeadUnprocessingQueryEvent } from './query-unprocessing.evt';
import { LeadAssignedQueryEvent } from './query-assigned.evt';
import { LeadReassignedQueryEvent } from './query-reassigned.evt';
import { LeadExpiredQueryEvent } from './query-expired.evt';
import { LeadPendingQueryEvent } from './query-pending.evt';
import { LeadUpdateQueryEvent } from './query-update.evt';
import { MsxLoggerService } from '../../../modules/logger/logger.service';
import { LeadFailedQueryEvent } from './query-failed.evt';

@EventsHandler([
  LeadCreatedQueryEvent,
  LeadDeletedQueryEvent,
  LeadProcessingQueryEvent,
  LeadUnprocessingQueryEvent,
  LeadAssignedQueryEvent,
  LeadReassignedQueryEvent,
  LeadFailedQueryEvent,
  LeadExpiredQueryEvent,
  LeadPendingQueryEvent,
  LeadUpdateQueryEvent
])
export class QueryEventHandler
  implements
  IEventHandler<LeadCreatedQueryEvent>,
  IEventHandler<LeadProcessingQueryEvent>,
  IEventHandler<LeadUnprocessingQueryEvent>,
  IEventHandler<LeadAssignedQueryEvent>,
  IEventHandler<LeadPendingQueryEvent>,
  IEventHandler<LeadReassignedQueryEvent>,
  IEventHandler<LeadFailedQueryEvent>,
  IEventHandler<LeadExpiredQueryEvent>,
  IEventHandler<LeadUpdateQueryEvent>,
  IEventHandler<LeadDeletedQueryEvent> {
  private readonly context = QueryEventHandler.name;
  constructor(
    private readonly repository: LeadQueryRepository,
    private readonly loggerService: MsxLoggerService,
  ) { }

  handle(event: LeadCreatedQueryEvent |
    LeadDeletedQueryEvent |
    LeadProcessingQueryEvent |
    LeadUnprocessingQueryEvent |
    LeadAssignedQueryEvent |
    LeadReassignedQueryEvent |
    LeadFailedQueryEvent |
    LeadExpiredQueryEvent |
    LeadPendingQueryEvent |
    LeadUpdateQueryEvent) {

    if (event && event instanceof LeadCreatedQueryEvent) {
      this.createdHandler(event.commandModel);
    } else if (event && event instanceof LeadDeletedQueryEvent) {
      this.deletedHandler(event);
    } else if (event && event instanceof LeadProcessingQueryEvent) {
      this.processingHandler(event.commandModel);
    } else if (event && event instanceof LeadUnprocessingQueryEvent) {
      this.unprocessingHandler(event.commandModel);
    } else if (event && event instanceof LeadAssignedQueryEvent) {
      this.assignHandler(event.commandModel);
    } else if (event && event instanceof LeadReassignedQueryEvent) {
      this.reassignHandler(event.commandModel);
    } else if (event && event instanceof LeadFailedQueryEvent) {
      this.failHandler(event.commandModel);
    } else if (event && event instanceof LeadExpiredQueryEvent) {
      this.expiredHandler(event.commandModel);
    } else if (event && event instanceof LeadPendingQueryEvent) {
      this.pendingHandler(event.commandModel);
    } else if (event && event instanceof LeadUpdateQueryEvent) {
      this.updateHandler(event.commandModel);
    }
  }

  private createdHandler(event) {
    this.loggerService.log(this.context, 'Async Lead Created Query Event Handler...');

    this.repository.create(event);
  }

  private deletedHandler(event) {
    this.loggerService.log(this.context, 'Async Lead Delete Query Event Handler...');

    this.repository.delete(event.id);
  }

  private processingHandler(event) {
    this.loggerService.log(this.context, 'Async Lead Process Query Event Handler...');

    this.repository.processingLead(event.id);
  }

  private unprocessingHandler(event) {
    this.loggerService.log(this.context, 'Async Lead UnProcess Query Event Handler...');

    this.repository.unprocessingLead(event);
  }

  private assignHandler(event) {
    this.loggerService.log(this.context, 'Async Lead Assign Query Event Handler...');

    this.repository.assignLead(event);
  }

  private reassignHandler(event) {
    this.loggerService.log(this.context, 'Async Lead Reassign Query Event Handler...');

    this.repository.reassignLead(event);
  }
  private failHandler(event) {
    this.loggerService.log(this.context, 'Async Lead Fail Query Event Handler...');

    this.repository.updateOne(event);
  }
  private expiredHandler(event) {
    this.loggerService.log(this.context, 'Async Lead Expire Query Event Handler...');

    this.repository.expiredLead(event);
  }

  private pendingHandler(event) {
    this.loggerService.log(this.context, 'Async Lead Pending Query Event Handler...');

    this.repository.pendingLead(event);
  }

  private updateHandler(event) {
    this.loggerService.log(this.context, 'Async Lead Update Query Event Handler...');

    this.repository.update(event);
  }
}
