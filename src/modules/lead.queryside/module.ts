import { CqrsModule } from "@nestjs/cqrs";
import { CommandHandlers } from "./commands/handlers";
import { EventHandlers } from "./events";
import { LeadController } from "./controller";
import { LeadService } from "./service";
import { LeadQueryRepository } from "./repository/query.repository";
import { QueryDatabaseModule } from "../database/query/query.database.module";
import { QueryProviders } from "./providers/query.cqrs.providers";
import { Module } from "@nestjs/common";
import { AuthModule } from "../auth/auth.module";
import { LeadHistoryQuerySideModule } from "../lead-history/module";
import { EmployeeQuerySideModule } from "../employee/module";
import { CustomerLeadController } from "./customer-controller";
import { PublicLeadController } from "./public.controller";
import { LoggerModule } from "../logger/logger.module";
import { CLeadQuerySideModule } from "../c-lead.queryside/module";
import { MgsSenderModule } from "../mgs-sender/mgs-sender.module";
import { HistoryImportQuerySideModule } from "../history-import/module";
import { SharedModule } from "../../../shared-modules";

@Module({
  imports: [
    CqrsModule,
    QueryDatabaseModule,
    AuthModule,
    LeadHistoryQuerySideModule,
    EmployeeQuerySideModule,
    LoggerModule,
    CLeadQuerySideModule,
    MgsSenderModule,
    HistoryImportQuerySideModule,
    SharedModule,
  ],

  controllers: [LeadController, CustomerLeadController, PublicLeadController],
  providers: [
    LeadService,
    LeadQueryRepository,
    ...QueryProviders,
    ...CommandHandlers,
    ...EventHandlers,
  ],
  exports: [LeadQueryRepository, LeadQuerySideModule, LeadService],
})
export class LeadQuerySideModule {}
