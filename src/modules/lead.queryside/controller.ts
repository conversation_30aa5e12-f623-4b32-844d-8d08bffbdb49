
import { <PERSON>, <PERSON>, <PERSON>q, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, BadRequestEx<PERSON>, Query, <PERSON>er, Inject, Re<PERSON> } from '@nestjs/common';
import { LeadService } from './service';
import { ILead, ILeadResponse } from '../shared/services/lead/interfaces/lead.interface';
import { ACGuard, UseRoles } from 'nest-access-control';
import { AuthGuard } from '@nestjs/passport';
import { ApiUseTags, ApiBearerAuth } from '@nestjs/swagger';
import { PermissionConst } from '../shared/constant/permission.const';
import { ErrorConst } from '../shared/constant/error.const';
import { CommonUtils } from '../shared/classes/class-utils';
import { CommonConst } from '../shared/constant/common.const';
import { ExploitEnum } from '../shared/enum/exploit.enum';
import { ReportExploitationFilter } from '../shared/interfaces/queryLead.interface';
import { JwtAuthGuard, RoleGuard, <PERSON><PERSON>, User } from "../../../shared-modules";
import * as _ from 'lodash';
import { Response } from 'express';

@ApiBearerAuth()
@ApiUseTags('v1/lead')
@Controller('v1/lead')
@UseGuards(JwtAuthGuard)
export class LeadController {

  constructor(
    private readonly leadService: LeadService,
  ) { }

  @UseGuards(RoleGuard)
  @Roles(PermissionConst.IMPORT_LEAD)
  @Get('/common')
  getAllLeadCommon(@User() user, @Headers('timezoneclient') timezoneclient: string, @Query() query: any = {}) {
    return this.leadService.listAllCommon(user, timezoneclient, query);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionConst.GET_ALL_LEAD)
  @Get('/primary')
  getPrimaryLead(@User() user, @Headers('timezoneclient') timezoneclient: string, @Query() query: any = {}) {
    return this.leadService.getAllPrimary(user, query, timezoneclient);
  }

  @Get('/toDeliver')
  @UseGuards(RoleGuard)
  @Roles(PermissionConst.LEAD_MANUAL_DELIVER)
  async getLeadsToDeliver(@User() user, @Query() query: any = {}) {
    const res = await this.leadService.getLeadsToDeliver(user, query);
    return res;
  }

  @Get('/history-import')
  async getAll(@User() user: any, @Headers('timezoneclient') timezoneclient: string, @Query() query: any = {}) {
    return this.leadService.listHistory(user, query);
  }

  // khai thac lead
  @Get('/assign')
  async getLeadsAssigned(
    @User() user,
    @Query('aggs') aggs: string,
  ) {
    console.log('api assign', user.id);
    return this.leadService.getLeads(user, aggs === '1');
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionConst.GET_LEAD_ID)
  @Get('/:id')
  findById(@Param('id') id: string, @Headers('timezoneclient') timezoneclient: string = 'Asia/Ho_Chi_Minh'): Promise<ILead> {
    return this.leadService.getLeadById(id, timezoneclient);
  }

  @Get('/current-time')
  async getCurrentTime() {
    return new Date().getTime();
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionConst.GET_ALL_LEAD)
  @Get()
  getAllLead(@User() user, @Headers('timezoneclient') timezoneclient: string) {
    return this.leadService.getAll(timezoneclient);
  }

  /**
   * Lấy danh sách yêu cầu tư vấn dịch vụ
   * @param timezoneclient
   * @param query
   */
  @UseGuards(RoleGuard)
  @Roles(PermissionConst.GET_ALL_LEAD_ADVISING)
  @Get('/advising')
  getAllLeadAdvising(@Headers('timezoneclient') timezoneclient: string, @Query() query: any = {}) {
    return this.leadService.listAllAdvising(timezoneclient, query);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionConst.REPORT_LEAD)
  @Get('/report/filter')
  pagingLeadsByFilter(@User() user: any, @Headers('timezoneclient') timezoneclient: string, @Query() query?: any) {
    return this.leadService.pagingLeadsByFilter(user, query, timezoneclient);
  }

  @Get('/report/count')
  countLeadsByFilter(@User() user: any, @Query() query?: any) {
    return this.leadService.countLeadsByFilter(user, query);
  }

  /**
   * API endpoint lấy thời gian còn hiệu lực của lead
   * @param timezoneclient
   * @param leadId
   */
  @UseGuards(RoleGuard)
  @Roles(PermissionConst.GET_ALL_LEAD)
  @Get('/remain-time/:id')
  getLeadRemainTime(@Headers('timezoneclient') timezoneclient: string, @Param('id') leadId: string) {
    return this.leadService.getLeadRemainTime(leadId);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionConst.GET_ALL_LEAD)
  @Get('/ready-for-assigning/:id')
  async countLeadReadyForAssigningByPOS(@Param('id') id: string, @Headers('timezoneclient') timezoneclient: string): Promise<any> {
    return this.leadService.countLeadReadyForAssigningByPOS(id);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionConst.GET_ALL_LEAD)
  @Get('/ready-for-assigning-primary/:id')
  async countPrimaryLeadReadyForAssigningByPOS(@Param('id') id: string, @Headers('timezoneclient') timezoneclient: string): Promise<any> {
    return this.leadService.countLeadReadyForAssigningByPOS(id, CommonConst.TYPE.PRIMARY);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionConst.GET_ALL_LEAD)
  @Get('/count-primary-available-by-emp')
  async countPrimaryLeadReadyForAssigningByEmp(@User() user: any, @Headers('timezoneclient') timezoneclient: string): Promise<any> {
    return this.leadService.countLeadReadyForAssigningByEmp(user.id, CommonConst.TYPE.PRIMARY);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionConst.GET_ALL_LEAD_BY_POS)
  @Get('/pos/:id')
  getAllByPos(@Param('id') id: string, @Headers('timezoneclient') timezoneclient: string): Promise<ILeadResponse[]> {
    return this.leadService.getAllByPos(id, timezoneclient);
  }



  @UseGuards(RoleGuard)
  @Roles(PermissionConst.GET_LEAD_PROCESS_BY_EMPLOYEE)
  @Get('/portal/process')
  findProcessByEmployeeId(@User() user, @Headers('timezoneclient') timezoneclient: string, @Query() query: any = {}) {
    // return this.leadService.getLeadsProcessBy(user, timezoneclient);
    return this.leadService.listLeadsProcessBy(user, timezoneclient, query)
  }

  @Get('/portal/summary')
  getSummay(@User() user) {
    return this.leadService.getSummary(user);
  }

  @Get('/dashboard/countAllTickets')
  countAllTickets() {
    return this.leadService.countAllTickets();
  }

  @Get('/dashboard/countProcessCancelTicketsByUser')
  countProcessCancelTicketsByUser(@User() user,
    @Query('userId') userList: any,
    @Query('from') from: string,
    @Query('to') to: string) {
    let arrayUser = new Array();
    if (userList === 'all') {
      arrayUser = null;
    } else if (Array.isArray(userList)) {
      arrayUser = userList;
    } else {
      arrayUser.push(userList || user.id);
    }
    return this.leadService.countProcessCancelTicketsByUser(arrayUser, user.id, from, to);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionConst.IMPORT_LEAD)
  @Get('/report/exploit')
  getExploitationReport(@User() user, @Query() query) {
    return this.leadService.countPrimaryLeads(user, query);
  }

  /**
   * Get explotation report by status
   *
   * @param status
   * @param query
   */
  @UseGuards(RoleGuard)
  @Roles(PermissionConst.LEAD_PRIMARY_VIEW_MENU)
  @Get(`/report/exploit/:status(${ExploitEnum.ASSIGN}|${ExploitEnum.PROCESSING}|${ExploitEnum.DONE}|${ExploitEnum.CANCEL})`)
  getExploitationReportByStatus(@User() user: any, @Param('status') status, @Query() query) {
    return this.leadService.getExploitationReportByStatus(user, status, query);
  }

  // @UseGuards(RoleGuard)
  // @Roles(PermissionConst.LEAD_PRIMARY_VIEW_MENU)
  // @Get(`/report/exploit/export-excel`)
  // exportExploitationReportAsExcel(@User() user, @Query() query) {
  //   return this.leadService.exportExploitationReportAsExcel(user, query);
  // }

  // @UseGuards(RoleGuard)
  // @Roles(PermissionConst.LEAD_PRIMARY_VIEW_MENU)
  // @Get(`/report/exploit/export-excel/download`)
  // async exportExploitationReportAsExcelDownload(@User() user, @Query() query) {
  //   if (query.history) {
  //     return await this.leadService.downloadHistoryExploitationReportAsExcel(user, query);
  //   }
  //   return await this.leadService.downloadExploitationReportAsExcel(user, query);
  // }

  @UseGuards(RoleGuard)
  @Roles(PermissionConst.LEAD_PRIMARY_VIEW_MENU)
  @Get(`/report/exploit/export-excel`)
  async downloadFile(@User() user, @Query() query, @Res() res: Response){
    let buffer: any = await this.leadService.downloadExploitationReportAsExcel(user, query);
    
    res.set({
      'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition': 'attachment; filename="Explotation-lead-reports.xlsx"',
      'Content-Length': buffer.length,
    });

    res.end(buffer);
    return { message: "File download started successfully", size: buffer.length };
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionConst.LEAD_PRIMARY_VIEW_MENU)
  @Get(`/report/exploit/export-excel/download`)
  async exportExploitationReportAsExcelDownload(@User() user, @Query() query, @Res() res: Response){
    let buffer: any = await this.leadService.downloadHistoryExploitationReportAsExcel(user, query)
    
    res.set({
      'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition': 'attachment; filename="Explotation-lead-reports-history.xlsx"',
      'Content-Length': buffer.length,
    });

    res.end(buffer);
    return { message: "File download started successfully", size: buffer.length };
  }

  @Get('/report/exploitation')
  async ReportExploitationByEmployee(@User() user: any, @Query() filter: ReportExploitationFilter) {
    return this.leadService.getReportExploitationByEmployee(user?.id, filter);
  }

  @Get('/report/today-exploited')
  async getTodayExploited(@User() user: any) {
    return this.leadService.getTodayExploited(user?.id);
  }
}
