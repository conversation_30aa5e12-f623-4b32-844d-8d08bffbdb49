import { EmployeeQueryRepository } from './repository/query.repository';
import { QueryDatabaseModule } from '../database/query/query.database.module';
import { QueryProviders } from './providers/query.cqrs.providers';
import { Module } from '@nestjs/common';
import { AuthModule } from '../auth/auth.module';
import { LoggerModule } from '../logger/logger.module';
import { EmployeeService } from './service';
import { EmployeeController } from './controller';
import { LeadRepoQueryModule } from '../leadRepo.queryside/module';
import { SharedModule } from "../../../shared-modules";
import { MgsSenderModule } from '../mgs-sender/mgs-sender.module';

@Module({
  imports: [QueryDatabaseModule, AuthModule, LoggerModule, LeadRepoQueryModule, MgsSenderModule],
  controllers: [EmployeeController],
  providers: [
    EmployeeService,
    EmployeeQueryRepository,
    ...QueryProviders,
    SharedModule
  ],
  exports: [
    EmployeeQueryRepository,
    EmployeeQuerySideModule,
    EmployeeService
  ]
})

export class EmployeeQuerySideModule { }
