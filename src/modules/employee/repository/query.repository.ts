import { Model } from 'mongoose';
import { Inject, Injectable } from '@nestjs/common';
import { IEmployeeDocument } from '../interfaces/document.interface';
import _ = require('lodash');
import { LifeCycleStatusEnum } from '../../../modules/shared/enum/life-cycle-status.enum';
import { CommonConst } from '../../../modules/shared/constant/common.const';
import { EmployeeClient } from '../../mgs-sender/employee.client';
import { CmdPatternConst } from '../../../../shared-modules';

@Injectable()
export class EmployeeQueryRepository {

  constructor(
    @Inject(CommonConst.QUERY_MODEL_TOKEN)
    private readonly readModel: Model<IEmployeeDocument>,
    private readonly employeeClient: EmployeeClient
  ) { }

  async findAll(): Promise<IEmployeeDocument[]> {
    return await this.employeeClient.sendDataPromise({ where: { isAdmin: false }, projection: { _id: 0, id: 1, name: 1, email: 1, accountId: '$account.id' } }, CmdPatternConst.EMPLOYEE.LISTENER.GET_BY_QUERY);
  }

  async findOne(query): Promise<IEmployeeDocument> {
    return await this.employeeClient.sendDataPromise({ where: query }, CmdPatternConst.EMPLOYEE.LISTENER.GET_BY_QUERY);
  }

  async create(model): Promise<IEmployeeDocument> {
    return await this.employeeClient.sendDataPromise({ model }, CmdPatternConst.EMPLOYEE.LISTENER.CREATE);
  }

  async update(model): Promise<IEmployeeDocument> {
    return await this.employeeClient.sendDataPromise({ where: { id: model.id }, set: model }, CmdPatternConst.EMPLOYEE.LISTENER.UPDATE);
  }

  async delete(model): Promise<any> {
    return await this.employeeClient.sendDataPromise({ model }, CmdPatternConst.EMPLOYEE.LISTENER.DELETE);
  }

  /**
   * @deprecated k dùng penalty nữa
   */
  async penalty(model) {
    await this.readModel.update(
      { id: model.id },
      { $set: { isPenalty: true } }
    ).catch((error) => {
      return error;
    });
  }

  /**
   * @deprecated k dùng penalty nữa
   */
  async resetPenalty() {
    await this.readModel.update(
      { isPenalty: true },
      { $set: { isPenalty: false } }
    ).catch((error) => {
      return error;
    });
  }


  async find(query): Promise<any[]> {
    return await this.employeeClient.sendDataPromise({ where: query }, CmdPatternConst.EMPLOYEE.LISTENER.GET_BY_QUERY);
  }

  async findAndUpdate(model: any) {
    return await this.employeeClient.sendDataPromise({ model }, CmdPatternConst.EMPLOYEE.LISTENER.FIND_AND_UPDATE);
  }

  async findEmployeesAvailable(posId) {
    const employees = await this.employeeClient.sendDataPromise({ where: { 'pos.id': posId, 'isPenalty': { $ne: true } } }, CmdPatternConst.EMPLOYEE.LISTENER.GET_BY_QUERY);
    await this.readModel.bulkWrite(
      employees.map((emp) => {
        if (emp.id) {
          return {
            updateOne: {
              filter: { id: emp.id },
              update: { $set: emp },
              upsert: true
            }
          };
        }
      }).filter(Boolean)
    );

    return await this.readModel.aggregate([
      {
        $match: {
          'pos.id': posId,
          'isPenalty': { $ne: true }
        }
      },
      {
        $lookup: {
          from: 'leads',
          let: { employeeId: '$id' },
          pipeline: [
            {
              $match:
              {
                $expr:
                {
                  $and:
                    [
                      { $eq: ['$$employeeId', '$processBy'] },
                      {
                        $or: [
                          { $eq: ['$lifeCycleStatus', LifeCycleStatusEnum.ASSIGNED] },
                          { $eq: ['$lifeCycleStatus', LifeCycleStatusEnum.PROCESSING] },
                        ]

                      }
                    ]
                }
              }
            },
          ],
          as: 'leads'
        }
      },
      {
        $project: {
          id: {
            $cond: {
              if: { $anyElementTrue: ['$leads'] },
              then: null,
              else: '$id'
            }
          },
        }
      }
    ]).allowDiskUse(true);
  }

  async updateMany(query, model): Promise<IEmployeeDocument> {
    return await this.employeeClient.sendDataPromise({ where: query, set: model }, CmdPatternConst.EMPLOYEE.LISTENER.UPDATE_MANY);
  }

  async findManyWithSort(query, projection = {}) {
    return await this.employeeClient.sendDataPromise({ where: query, projection, sort: { id: -1 } }, CmdPatternConst.EMPLOYEE.LISTENER.GET_BY_QUERY);
  }

  async getStaffByIds(ids) {
    return await this.employeeClient.sendDataPromise({ where: { id: { $in: ids } }, projection: { _id: 0, code: 1, name: 1 } }, CmdPatternConst.EMPLOYEE.LISTENER.GET_BY_QUERY);
  }

  async getManagerByPos(id) {
    return await this.employeeClient.sendDataPromise({ where: { "pos.id": id, managerAt: id }, projection: { code: 1, name: 1 } }, CmdPatternConst.EMPLOYEE.LISTENER.GET_BY_QUERY);
  }

  async saveAll(models: any[]): Promise<any> {
    return await this.employeeClient.sendDataPromise({ models }, CmdPatternConst.EMPLOYEE.LISTENER.BULK_WRITE);
  }

  async bulkWrite(employees) {
    await this.readModel.bulkWrite(
      employees.map((emp) => {
        if (emp.id) {
          return {
            updateOne: {
              filter: { id: emp.id },
              update: { $set: emp },
              upsert: true
            }
          };
        }
      }).filter(Boolean)
    );
  }

  async bulkWriteDropdown() {
    const today = new Date();
    const lastSyncEmp = await this.readModel.find().sort({ lastSync: 'desc' }).limit(1);
    const lastSync = lastSyncEmp.length > 0 ? lastSyncEmp[0]['lastSync'] : null;
    const lastSyncDate = lastSync instanceof Date ? lastSync : null;

    if (lastSyncDate === null || today.getDate() != lastSyncDate.getDate() ||
      today.getMonth() != lastSyncDate.getMonth() ||
      today.getFullYear() != lastSyncDate.getFullYear()) {

      const employees = await this.employeeClient.sendDataPromise({}, CmdPatternConst.EMPLOYEE.DROPDOWN);
      const updates = employees.map((emp) => {
        if (emp.id) {
          return {
            updateOne: {
              filter: { id: emp.id },
              update: { $set: { ...emp, lastSync: today } },
              upsert: true
            }
          };
        }

        return null;
      }).filter(Boolean);

      if (updates.length > 0) {
        await this.readModel.bulkWrite(updates);
      } else {
        console.log("No updates emp to perform.");
      }
    }
  }
}
