import { Injectable } from '@nestjs/common';
import { EmployeeQueryRepository } from './repository/query.repository';
import { isNullOrUndefined, deprecate } from 'util';
import { MsxLoggerService } from '../logger/logger.service';
import { LeadRepoQueryRepository } from '../leadRepo.queryside/repositories/query.repository';
import { OrgchartClient } from '../mgs-sender/orgchart.client';
import { CmdPatternConst } from '../../../shared-modules';

@Injectable()
export class EmployeeService {
  private readonly context = EmployeeService.name;
  constructor(
    private readonly loggerService: MsxLoggerService,
    private readonly repository: EmployeeQueryRepository,
    private readonly leadRepoRepository: LeadRepoQueryRepository,
    private readonly orgClient: OrgchartClient
  ) { }

  async create(model) {
    await this.repository.create(model);
  }

  async update(model) {
    const employee = await this.repository.findOne({ id: model.id });
    if (isNullOrUndefined(employee)) {
      return;
    }
    model.isPenalty = isNullOrUndefined(model.isPenalty) ? model.isPenalty : employee.isPenalty;
    model.timePullLatest = isNullOrUndefined(model.timePullLatest) ? model.timePullLatest : employee.timePullLatest;
    return await this.repository.update(model);
  }

  async delete(model) {
    return await this.repository.delete(model);
  }

  /**
   * @deprecated Không sử dụng tham số penalty nữa - O2OWADMIN-619
   */
  async penalty(model) {
    this.loggerService.log(this.context, 'Calling deprecated function! - penalty');
    return await this.repository.penalty(model);
  }

  /**
   * @deprecated Không sử dụng tham số penalty nữa - O2OWADMIN-619
   */
  async resetPenalty() {
    this.loggerService.log(this.context, 'Calling deprecated function! - resetPenalty');
    return await this.repository.resetPenalty();
  }

  async updateList(model) {
    console.log('updateEmployee saveAll', model.length);
    await this.repository.saveAll(model);
  }

  async findAndUpdate(model) {
    await this.repository.findAndUpdate(model);
  }

  async getAllOrgchart() {
    return await this.repository.find({});
  }

  async getEmployeesAvailable(posId) {
    return this.repository.findEmployeesAvailable(posId).then(response => {
      const result = [];
      response.forEach(i => result.push(i.id));
      return result;
    });
  }

  async getById(id) {
    return this.repository.findOne({ id });
  }

  async getStaffByLeadRepoConfigCode(id, code, query) {
    const repo = await this.leadRepoRepository.findById(id);
    const config = repo.configs.find(config => config.code === code);
    const orgCharts = query?.posId ? config.orgCharts.filter(x => x.id === query.posId) : config.orgCharts;
    const ids = [];

    for (const org of orgCharts) {
      ids.push(...org.staffIds);
    }

    return this.repository.getStaffByIds(ids);
  }

  async getStaffsByPos(user) {
    const emp = await this.repository.findOne({ 'account.id': user.id });
    console.log('emp', emp);
    if (emp) {
      const orgCode = emp[0]['orgCode'];
      console.log('orgCode', orgCode);
      const orgchart = await this.orgClient.sendDataPromise({ code: orgCode }, CmdPatternConst.ORGCHART.LISTENER.GET_BY_QUERY);
      console.log('orgchart', orgchart);
      return await this.repository.getStaffByIds(orgchart.staffIds);
    }
    return [];
  }

  async getAll() {
    const employees = await this.repository.findAll();
    return employees;
  }
}