import * as mongoose from 'mongoose';
import uuid = require('uuid');


export const QuerySchema = new mongoose.Schema({
    _id: { type: String },
    id: { type: String, index: true },
    name: { type: String },
    email: { type: String },
    isPenalty: { type: Boolean, default: false },
    code: { type: String },
    pos: { type: Object },
    orgCode: { type: String },
    timePullLatest: { type: String, default: '' },
    staffIds: {type: Object},
    leadsPullLatest: {type: Object, default: []},
    active: { type: Boolean, default: true },
    level: { type: Number },
    workingAt: { type: String, default: null },
    managerAt: { type: String , default: null},
    phone: { type: String},
    lastSync: { type: Date },
});

QuerySchema.pre('save', function (next) {
    this._id = this.get('id');
    next();
});
