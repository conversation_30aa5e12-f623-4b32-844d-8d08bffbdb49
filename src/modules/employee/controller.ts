import { Controller, Post, Get, Param, UseGuards, Query } from '@nestjs/common';
import { EmployeeService } from './service';
import { JwtAuthGuard, User } from '../../../shared-modules';

@Controller('v1/employee')
export class EmployeeController {
  constructor(
    private readonly service: EmployeeService
  ) { }

  @Post('/resetPenalty')
  resetPenalty() {
    this.service.resetPenalty();
  }

  @Get('/staffs-by-repo-config-code/:id/:code')
  getStaffsByRepoConfigCode(@Param('id') id, @Param('code') code, @Query() query) {
    return this.service.getStaffByLeadRepoConfigCode(id, code, query);
  }

  @Get('/staffs')
  @UseGuards(JwtAuthGuard)
  getStaffsByPos(@User() user) {
    return this.service.getStaffsByPos(user);
  }

  @Get('')
  getAll() {
    return this.service.getAll();
  }
}