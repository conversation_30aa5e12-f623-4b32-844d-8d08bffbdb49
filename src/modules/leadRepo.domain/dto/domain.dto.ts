import { ApiModelPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsNotEmpty, IsOptional, IsString, IsUppercase, MaxLength, ValidateNested } from 'class-validator';
import { BaseModel } from '../../shared/models/base/base.model';
import {
  LeadRepoConfig,
  LeadRepoConfigHot,
  UpdateLeadRepoConfigHot,
} from '../../shared/models/leadRepo/model';
import { trim } from "lodash";

export class CreateLeadRepoDto {
  @ApiModelPropertyOptional()
  @Transform((value) => trim(value))
  @IsString()
  @IsNotEmpty()
  @MaxLength(60)
  @IsUppercase()
  name: string;

  @ApiModelPropertyOptional({ type: LeadRepoConfigHot })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => LeadRepoConfigHot)
  configHot: LeadRepoConfigHot;

  @ApiModelPropertyOptional({ type: [LeadRepoConfig] })
  @ValidateNested({ each: true })
  @IsNotEmpty()
  @Type(() => LeadRepoConfig)
  configs: LeadRepoConfig[];
}

export class UpdateLeadRepoMainDto extends BaseModel {
  @ApiModelPropertyOptional()
  @IsNotEmpty()
  id: string;

  @ApiModelPropertyOptional()
  @Transform((value) => trim(value))
  @IsString()
  @IsNotEmpty()
  @MaxLength(60)
  @IsUppercase()
  name: string;

  @ApiModelPropertyOptional({ type: UpdateLeadRepoConfigHot })
  @ValidateNested()
  @IsNotEmpty()
  @Type(() => UpdateLeadRepoConfigHot)
  configHot: UpdateLeadRepoConfigHot;
}

class UpdateLeadRepoConfig extends LeadRepoConfig {
  @ApiModelPropertyOptional()
  code: string;
}

export class UpdateLeadRepoConfigDto {
  @IsNotEmpty()
  @ApiModelPropertyOptional()
  id: string;

  @ApiModelPropertyOptional({ type: UpdateLeadRepoConfig })
  @ValidateNested({ each: true })
  @IsNotEmpty()
  @Type(() => UpdateLeadRepoConfig)
  config: UpdateLeadRepoConfig;
}

export class ActiveLeadRepoConfigDto {
  @IsNotEmpty()
  @ApiModelPropertyOptional()
  id: string;

  @IsNotEmpty()
  @ApiModelPropertyOptional()
  configCode: string;
}

export class DeleteLeadRepoConfigDto {
  @IsString()
  @IsNotEmpty()
  @ApiModelPropertyOptional()
  id: string;

  @IsString()
  @IsNotEmpty()
  @ApiModelPropertyOptional()
  configCode: string;

  @IsOptional()
  @IsString()
  @ApiModelPropertyOptional()
  softDeleteReason?: string;
}

export class ChangeStatusLeadRepoConfigDto {
  @IsString()
  @IsNotEmpty()
  @ApiModelPropertyOptional()
  id: string;

  @IsString()
  @IsNotEmpty()
  @ApiModelPropertyOptional()
  configCode: string;
}
