import {
  Body,
  Controller,
  Post,
  UseGuards,
  UseInterceptors,
  Headers,
  Delete,
  Put,
} from "@nestjs/common";
import { Api<PERSON><PERSON><PERSON>Auth, ApiUseTags } from "@nestjs/swagger";
import { LoggingInterceptor } from "../../common/interceptors/logging.interceptor";
import { ValidationPipe } from "../../common/pipes/validation.pipe";
import { PermissionConst } from "../shared/constant/permission.const";
import { Action } from "../shared/enum/action.enum";
import { IUserResquest } from "../shared/services/user/user-by-id.interface";
import {
  ChangeStatusLeadRepoConfigDto,
  CreateLeadRepoDto,
  DeleteLeadRepoConfigDto,
  UpdateLeadRepoConfigDto,
  UpdateLeadRepoMainDto,
} from "./dto/domain.dto";
import { LeadRepoDomainService } from "./service";
import { JwtAuthGuard, RoleGuard, Roles, User } from "../../../shared-modules";

@ApiBearerAuth()
@ApiUseTags("v1/lead-repo")
@Controller("v1/lead-repo")
@UseInterceptors(LoggingInterceptor)
@UseGuards(JwtAuthGuard, RoleGuard)
export class LeadRepoDomainController {
  private actionName: string = Action.NOTIFY;
  constructor(protected readonly service: LeadRepoDomainService) {}

  @Roles(PermissionConst.LEAD_CONFIG_CREATE)
  @Post()
  async createLeadRepo(
    @User() user: IUserResquest,
    @Body() dto: CreateLeadRepoDto,
    @Headers("timezoneclient") timezoneClient: string,
    @Headers("act") actionName?: string
  ) {
    return this.service.createLeadRepo(
      dto,
      actionName || this.actionName,
      user,
      timezoneClient || "Asia/Ho_Chi_Minh"
    );
  }

  // config main
  @Roles(PermissionConst.LEAD_CONFIG_UPDATE)
  @Put()
  async updateLeadRepoMain(
    @User() user: IUserResquest,
    @Body() dto: UpdateLeadRepoMainDto,
    @Headers("timezoneclient") timezoneClient: string,
    @Headers("act") actionName?: string
  ) {
    return this.service.updateLeadRepoMain(
      dto,
      actionName || this.actionName,
      user,
      timezoneClient || "Asia/Ho_Chi_Minh"
    );
  }

  // config
  @Roles(PermissionConst.LEAD_CONFIG_UPDATE)
  @Put("config")
  async updateConfig(
    @User() user: IUserResquest,
    @Body() record: UpdateLeadRepoConfigDto,
    @Headers("timezoneclient") timezoneClient: string,
    @Headers("act") actionName?: string
  ) {
    return this.service.updateLeadRepoConfig(
      record,
      actionName || this.actionName,
      user,
      timezoneClient || "Asia/Ho_Chi_Minh"
    );
  }

  // @Roles(PermissionConst.MANAGE_REPOSITORY)
  // @Post('active-config')
  // async activeConfig(
  //   @User() user: IUserResquest,
  //   @Body(new ValidationPipe()) record: ActiveLeadRepoConfigDto,
  //   @Headers('timezoneclient') timezoneClient: string,
  //   @Headers('act') actionName?: string
  // ) {
  //   return this.service.activeLeadRepoConfig(
  //     record,
  //     actionName || this.actionName,
  //     user?.id,
  //     timezoneClient || 'Asia/Ho_Chi_Minh'
  //   );
  // }

  @Roles(PermissionConst.LEAD_CONFIG_DELETE)
  @Delete("config")
  async deleteConfig(@User() user, @Body() dto: DeleteLeadRepoConfigDto) {
    return this.service.deleteConfig(user, dto);
  }

  @Roles(PermissionConst.LEAD_CONFIG_CHANGE_STATUS)
  @Put("config/changeStatus")
  async changeStatus(@User() user, @Body() dto: ChangeStatusLeadRepoConfigDto) {
    return this.service.changeStatusConfig(user, dto);
  }
}
