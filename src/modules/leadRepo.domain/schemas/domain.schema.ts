import * as mongoose from 'mongoose';
import { getEventStreamSchema } from '../../shared/schema/schema.base';
import { LeadRepoSchema } from '../../leadRepo.queryside/schemas/query.schema';

const beautifyUnique = require('mongoose-beautiful-unique-validation');

export const PayloadSchema = new mongoose.Schema({
  ...LeadRepoSchema,
  eventName: { type: String },
  actionName: { type: String },
  revision: { type: Number },
});

PayloadSchema.pre('save', function (next) {
  this._id = this.get('id');
  next();
});

PayloadSchema.plugin(beautifyUnique);

export const DomainSchema = getEventStreamSchema(PayloadSchema);

DomainSchema.pre('save', function (next) {
  this._id = this.get('id');
  const payload = this.get('payload');
  if (payload) {
    payload.processedDate = this.get('commitStamp');
  }
  next();
});
