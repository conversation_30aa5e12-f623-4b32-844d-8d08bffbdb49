import { BadRequestException, Injectable } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { PropertyClient } from '../mgs-sender/property.client';
import {
  LeadRepo,
  DateRange,
  LeadRepoConfig,
} from '../shared/models/leadRepo/model';
import * as Bluebird from 'bluebird';
import { CmdPatternConst } from '../shared/constant/cmd-pattern.const';
import { ErrorConst } from '../shared/constant/error.const';
import uuid = require('uuid');
import { Action } from '../shared/enum/action.enum';
import { CreateLeadRepoCommand } from './commands/impl/create.cmd';
import { MsxLoggerService } from '../logger/logger.service';
import { LeadRepoQueryRepository } from '../leadRepo.queryside/repositories/query.repository';
import { UpdateLeadRepoCommand } from './commands/impl/update.cmd';
import { OrgchartClient } from '../mgs-sender/orgchart.client';
import { CodeGenerateService } from '../code-generate/service';
import { cloneDeep, isEmpty } from 'lodash';
import {
  ActiveLeadRepoConfigDto,
  CreateLeadRepoDto,
  UpdateLeadRepoConfigDto,
  UpdateLeadRepoMainDto,
} from './dto/domain.dto';
import { LeadRepoCommandModel } from '../shared/models/leadRepo/command.model';
import { LeadRepoDomainModel } from './models/domain.model';
import { updateArrData } from '../shared/utils/updateArrData';
import { UpdateConfigLeadRepoCommand } from './commands/impl/updateConfig';
import { ActiveConfigLeadRepoCommand } from './commands/impl/activeConfig';
import { BulkUpdateLeadRepoCommand } from './commands/impl/bulkUpdate.cmd';
import { AwesomeLogger, BaseService, ErrorService } from '../../../shared-modules';
import { LeadQueryRepository } from '../lead.queryside/repository/query.repository';
import { ActiveEnum } from '../shared/enum/active.enum';
import { CommonConst } from '../shared/constant/common.const';
import { EmployeeClient } from '../mgs-sender/employee.client';
import { CmdPatternConst as CmdPatternConst2 } from '../../../shared-modules';

@Injectable()
export class LeadRepoDomainService extends BaseService {
  private readonly context = LeadRepoDomainService.name;
  private readonly successResponse = { success: true };
  private readonly logger = new AwesomeLogger(LeadRepoDomainService.name);

  constructor(
    private readonly commandBus: CommandBus,
    private readonly propertyClient: PropertyClient,
    private readonly queryRepo: LeadRepoQueryRepository,
    private readonly leadRepo: LeadQueryRepository,
    private readonly orgchartClient: OrgchartClient,
    protected readonly codeSrv: CodeGenerateService,
    public readonly errorService: ErrorService,
    public readonly employeeClient: EmployeeClient,
  ) {
    super(errorService);
  }

  async createLeadRepo(
    record: CreateLeadRepoDto,
    actionName: string,
    user: any,
    timezoneClient: string
  ) {
    this.logger.info(this.context, 'createLeadRepo');
    const { configs, configHot, name } = record;

    const leadRepo = await this.queryRepo.findByQuery({ name });

    // duplicate repo name
    if (leadRepo && leadRepo.length > 0) {
      return this.getResponse('LREPOE0002');
    }

    this.logger.debug('!configs?.length', !configs?.length);
    if (!configs?.length || !configHot) {
      return this.getResponse('LREPOE0006');
    }

    this.logger.debug('hotOrgCharts orgChartIds', configHot.orgChartIds);
    const hotOrgCharts = await this.validateOrgChart(configHot);

    this.logger.debug('hotOrgCharts', hotOrgCharts);
    // this.validateDuration(configHot.assignDuration, true);

    Object.assign(configHot, { orgCharts: hotOrgCharts });

    const validConfigs = [];
    await Bluebird.mapSeries(configs, async (config) => {
      await this.validateExploitTime(config.exploitTime);
      const configOrgCharts = await this.validateOrgChart(config);
      await this.validateProject(config.projectId);
      // this.validateDuration(config.assignDuration);
      const configCode = await this.codeSrv.generateLeadRepoConfigCode(user);
      Object.assign(config, {
        code: configCode,
        orgCharts: configOrgCharts,
      });
      validConfigs.push(config);
    });

    const commandId = uuid.v4();

    // generate repo code
    const leadRepoCode = await this.codeSrv.generateLeadRepoCode(user);

    Object.assign(record, {
      code: leadRepoCode,
      configs: validConfigs,
      configHot,
      createdBy: user.id,
      modifiedBy: user.id,
      timezoneClient
    });

    await this.executeCommand(
      Action.CREATE,
      actionName,
      commandId,
      record as LeadRepoCommandModel
    );

    return this.successResponse;
  }

  async updateLeadRepoMain(
    { id, configHot, name }: UpdateLeadRepoMainDto,
    actionName: string,
    user: any,
    timezoneClient: string,
    skipValidate = false
  ) {
    this.logger.info(this.context, 'updateLeadRepoMain');
    const leadRepo = await this.queryRepo.findById(id, true);
    if (!leadRepo) {
      return this.getResponse('LREPOE0003');
    }

    if (!isEmpty(name) && leadRepo.name != name) {
      const repo = await this.queryRepo.findByQuery({ name });
      if (repo && repo.length > 0) {
        return this.getResponse('LREPOE0002');
      }
    }

    if (!skipValidate) {
      const hotOrgCharts = await this.validateOrgChart(configHot);
      // this.validateDuration(configHot.assignDuration, true);
      Object.assign(configHot, { orgCharts: hotOrgCharts });
    }

    const commandId = uuid.v4();

    Object.assign(leadRepo, {
      configHot,
      name,
      updatedDate: Date.now(),
      modifiedBy: user.id,
      timezoneClient: timezoneClient || leadRepo.timezoneClient,
    });

    await this.executeCommand(
      Action.UPDATE_MAIN,
      actionName,
      commandId,
      leadRepo as LeadRepoCommandModel
    );

    return this.successResponse;
  }

  async updateLeadRepoConfig(
    { id, config }: UpdateLeadRepoConfigDto,
    actionName: string,
    user: any,
    timezoneClient: string
  ) {
    this.logger.info(this.context, 'updateLeadRepoConfig');
    const leadRepo = await this.queryRepo.findById(id, true);
    if (!leadRepo) {
      return this.getResponse('LREPOE0003');
    }

    let { configs } = leadRepo;
    await this.validateExploitTime(config.exploitTime);
    const configOrgCharts = await this.validateOrgChart(config);
    await this.validateProject(config.projectId);
    // this.validateDuration(config.assignDuration);
    Object.assign(config, { orgCharts: configOrgCharts });

    const existed = configs.find(i => i.code === config.code);
    if (!existed) {
      const newCode = await this.codeSrv.generateLeadRepoConfigCode(user);
      Object.assign(config, { code: newCode });
      configs = this.updateConfigData(configs, null, config);
    } else {
      Object.assign(existed, config);
      configs = this.updateConfigData(configs, existed.code, existed);
    }

    Object.assign(leadRepo, {
      configs,
      updatedDate: Date.now(),
      modifiedBy: user.id,
      timezoneClient: timezoneClient || leadRepo.timezoneClient,
    });

    const commandId = uuid.v4();

    await this.executeCommand(
      Action.UPDATE_CONFIG,
      actionName,
      commandId,
      leadRepo as LeadRepoCommandModel
    );

    return this.successResponse;
  }

  // async updateLeadRepoConfigs(
  //   id: string,
  //   configs: LeadRepoConfig[],
  //   actionName: string,
  //   userId: string,
  //   timezoneClient: string
  // ) {
  //   this.logger.info(this.context, 'updateLeadRepoConfig');
  //   const leadRepo = await this.queryRepo.findById(id, true);
  //   if (!leadRepo) {
  //     throw new BadRequestException({
  //       errors: ErrorConst.CommonError(
  //         ErrorConst.NOT_FOUND,
  //         'leadRepo'
  //       ),
  //     });
  //   }

  //   let { configs: defaultConfigs } = leadRepo;

  //   await Bluebird.mapSeries(configs, async config => {
  //     await this.validateExploitTime(config.exploitTime);
  //     const configOrgCharts = await this.validateOrgChart(config.orgChartIds);
  //     await this.validateProject(config.projectId);

  //     Object.assign(config, { orgCharts: configOrgCharts });

  //     const existed = defaultConfigs.find(i => i.code === config.code);
  //     if (!existed) {
  //       defaultConfigs = this.updateConfigData(defaultConfigs, null, config);
  //     } else {
  //       Object.assign(existed, config);
  //       defaultConfigs = this.updateConfigData(
  //         defaultConfigs,
  //         existed.code,
  //         existed
  //       );
  //     }
  //   });

  //   Object.assign(leadRepo, {
  //     configs,
  //     updatedDate: Date.now(),
  //     modifiedBy: userId || leadRepo.modifiedBy,
  //     timezoneClient: timezoneClient || leadRepo.timezoneClient,
  //   });

  //   const commandId = uuid.v4();

  //   await this.executeCommand(
  //     Action.UPDATE_CONFIG,
  //     actionName,
  //     commandId,
  //     leadRepo as LeadRepoCommandModel
  //   );

  //   return this.successResponse;
  // }

  async activeLeadRepoConfig(
    { id, configCode }: ActiveLeadRepoConfigDto,
    actionName: string,
    userId: string,
    timezoneClient: string
  ) {
    this.logger.info(this.context, 'activeLeadRepoConfig');
    const leadRepo = await this.queryRepo.findById(id, true);
    if (!leadRepo) {
      return this.getResponse('LREPOE0003');
    }

    const { configs } = leadRepo;

    const targetIdx = configs.findIndex((item) => item.code === configCode);
    if (targetIdx === -1) {
      return this.getResponse('LREPOE0004');
    }

    const target = cloneDeep(configs[targetIdx]);
    Object.assign(target, { active: !target.active });

    const updatedConfigs = updateArrData(target, configs, targetIdx);
    Object.assign(leadRepo, {
      configs: updatedConfigs,
      modifiedBy: userId,
      updatedDate: Date.now(),
      timezoneClient,
    });

    const commandId = uuid.v4();

    await this.executeCommand(
      Action.ACTIVE_CONFIG,
      actionName,
      commandId,
      leadRepo as LeadRepoCommandModel
    );
    return this.successResponse;
  }

  async bulkUpdateLeadConfig(
    repos: LeadRepo[],
    actionName?: string
  ) {
    const commandId = uuid.v4();
    return this.executeCommand(Action.BULK_UPDATE, actionName, commandId, repos);
  }

  private async validateOrgChart(config) {
    const { orgChartIds, orgCharts } = config;
    const orgInfos: any[] = await this.orgchartClient.sendDataPromise(
      { posIds: orgChartIds },
      CmdPatternConst.ORGCHART.GET_POS_BY_QUERY
    );

    this.logger.debug('validate orgCharts', orgInfos);
    this.logger.debug('orgChartIds', orgChartIds.length);

    if (orgInfos?.length !== orgChartIds.length) {
      return this.getResponse('ORGE0004');
    }

    // get staffs from orgCharts
    let staffIds = [];
    if(orgCharts[0]?.staffIds?.length > 0) {
      staffIds = orgCharts[0]?.staffIds;
    } else {
      staffIds = orgInfos[0]?.staffIds;
    }

    return orgCharts.map((item) => ({
      id: item.id,
      name: item.name || item.nameVN || item.nameEN,
      staffIds,
      deassignedStaffIds: []
    }));
  }

  private async validateProject(projectId: string) {
    const project = await this.propertyClient.sendDataPromise(
      { id: projectId },
      CmdPatternConst.PROJECT.GET_PROJECT_BY_ID
    );

    if (!project) {
      return this.getResponse('PRJE0011');
    }
  }

  private async validateExploitTime(exploitTime: DateRange) {
    if (!exploitTime?.from || !exploitTime?.to) {
      return this.getResponse('LREPOE0005');
    }
    if (new Date(exploitTime.from) > new Date(exploitTime.to)) {
      return this.getResponse('LREPOE0005');
    }
  }

  // private validateDuration(duration: number, hotConfig = false) {
  //   let minDuration = Number(process.env.MIN_DURATION) || 10;
  //   if (hotConfig) {
  //     minDuration = Number(process.env.MIN_DURATION_HOT) || 1;
  //   }
  //   if (duration < minDuration) {
  //     throw new BadRequestException({
  //       errors: ErrorConst.CommonError(
  //         ErrorConst.INVALID_FIELD,
  //         'assignDuration'
  //       ),
  //     });
  //   }
  // }

  private async executeCommand(
    action: string,
    actionName: string,
    commandId: string,
    item: LeadRepo | LeadRepo[]
  ) {
    switch (action) {
      case Action.CREATE:
        return this.commandBus.execute(
          new CreateLeadRepoCommand(actionName, commandId, item as any)
        );
      case Action.UPDATE_MAIN:
        return this.commandBus.execute(
          new UpdateLeadRepoCommand(actionName, commandId, item as any)
        );
      case Action.UPDATE_CONFIG:
        return this.commandBus.execute(
          new UpdateConfigLeadRepoCommand(actionName, commandId, item as any)
        );
      case Action.ACTIVE_CONFIG:
        return this.commandBus.execute(
          new ActiveConfigLeadRepoCommand(actionName, commandId, item as any)
        );
      case Action.BULK_UPDATE:
        return this.commandBus.execute(
          new BulkUpdateLeadRepoCommand(actionName, commandId, item as any)
        )
      default:
        break;
    }
  }

  private updateConfigData(src: LeadRepoConfig[], code: string, record: LeadRepoConfig) {
    if (!code) {
      src = src.concat(record);
    } else {
      const idx = src.findIndex(item => item.code === code);
      src = [...src.slice(0, idx), record, ...src.slice(idx + 1)];
    }

    return src;
  }


  async changeStatusConfig(user, dto) {
    try {
      this.logger.info(this.context, 'changeStatusConfig');
      const leadRepo = await this.queryRepo.findOne({ where: { id: dto.id, 'configs.code': dto.configCode } });
      if (!leadRepo) {
        return this.getResponse('LREPOE0003');
      }

      const { configCode } = dto;

      const { configs } = leadRepo;
      if (isEmpty(configs)) {
        return this.getResponse('LREPOE0004');
      }

      let canChangeStatus = false;
      await Bluebird.mapSeries(configs, async config => {
        if (config.code == configCode) {
          if (config.active == ActiveEnum.INACTIVE) { // inactive -> active
            config.active = ActiveEnum.ACTIVE;
            canChangeStatus = true;
          } else {
            const checkDeactive = await this.canBeDeleteOrInactive(config);
            console.log('checkDeactive', checkDeactive);
            if (checkDeactive) {
              config.active = ActiveEnum.INACTIVE;
              canChangeStatus = true;
            }
          }
        }
      });

      console.log('canChangeStatus', canChangeStatus);
      if (canChangeStatus) {
        leadRepo.modifiedBy = user.id;
        leadRepo.updatedDate = new Date();
        await this.queryRepo.update(leadRepo);
        return this.getResponse(0);
      } else {
        return this.getResponse('LREPOE0001');
      }
    } catch (error) {
      this.logger.error('error delete config', error);
    }
  }

  async deleteConfig(user, dto) {
    try {
      this.logger.info(this.context, 'deleteConfig');
      const leadRepo = await this.queryRepo.findOne({ where: { id: dto.id, 'configs.code': dto.configCode } });
      if (!leadRepo) {
        return this.getResponse('LREPOE0003');
      }

      const { configCode } = dto;

      const { configs } = leadRepo;
      if (isEmpty(configs)) {
        return this.getResponse('LREPOE0004');
      }

      let canDelete = false;
      await Bluebird.mapSeries(configs, async config => {
        if (config.code == configCode) {
          const checkDelete = await this.canBeDeleteOrInactive(config);
          if (checkDelete) {
            config.softDelete = true;
            config.softDeleteReason = dto.softDeleteReason;
            canDelete = true;
          }
        }
      });

      if (canDelete) {
        leadRepo.modifiedBy = user.id;
        leadRepo.updatedDate = new Date();
        await this.queryRepo.update(leadRepo);
        return this.getResponse(0);
      } else {
        return this.getResponse('LREPOE0001');
      }
    } catch (error) {
      this.logger.error('error delete config', error);
    }
  }

  async canBeDeleteOrInactive(config) {
    const { code } = config;

    // check exploit time
    const now = new Date();
    const exploitFrom = new Date(config.exploitTime.from);
    const exploitTo = new Date(config.exploitTime.to);
    if (exploitFrom < now || now < exploitTo) {
      // get all lead with config
      const leadsWConfig = await this.leadRepo.findMany({ repoConfigCode: code });
      if (leadsWConfig && leadsWConfig.length > 0) {
        // get all lead with config and not 'done'
        const leadsNotDone = await this.leadRepo.findMany({ repoConfigCode: code, exploitStatus: { $ne: 'done' } });
        console.log('leadsNotDone', leadsNotDone);
        if (leadsNotDone && leadsNotDone.length > 0) {
          return false;
        } else {
          return true;
        }
      } else {
        return true;
      }
    }

    return true;
  }
}
