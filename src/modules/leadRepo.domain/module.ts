import { HttpModule, Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { CodeGenerateModule } from '../code-generate/module';
import { DomainDatabaseModule } from '../database/domain/domain.database.module';
import { LeadRepoQueryModule } from '../leadRepo.queryside/module';
import { LoggerModule } from '../logger/logger.module';
import { MgsSenderModule } from '../mgs-sender/mgs-sender.module';
import { LeadRepoDomainCommandHandlers } from './commands/handlers';
import { LeadRepoDomainController } from './controller';
import { LeadRepoEventStreamHandler } from './events/domain.eventHandler';
import { LeadRepoDomainProvider } from './providers/domain.provider';
import { LeadRepoDomainRepository } from './repositories/domain.repository';
import { LeadRepoDomainSagas } from './sagas/domain.sagas';
import { LeadRepoDomainService } from './service';
import { SharedModule } from '../../../shared-modules';
import { LeadQuerySideModule } from '../lead.queryside/module';

@Module({
  imports: [
    CqrsModule,
    DomainDatabaseModule,
    LeadRepoQueryModule,
    MgsSenderModule,
    LoggerModule,
    HttpModule,
    LeadRepoQueryModule,
    CodeGenerateModule,
    SharedModule,
    LeadQuerySideModule
  ],
  exports: [LeadRepoDomainService],
  controllers: [LeadRepoDomainController],
  providers: [
    LeadRepoDomainService,
    LeadRepoDomainRepository,
    LeadRepoDomainSagas,
    ...LeadRepoDomainProvider,
    ...LeadRepoDomainCommandHandlers,
    LeadRepoEventStreamHandler,
  ],
})
export class LeadRepoDomainModule { }
