import { Module } from "@nestjs/common";
import { ClientProxyFactory, ClientsModule, Transport } from "@nestjs/microservices";
import { ConfigService } from "@nestjs/config";
import { LoggerClient } from "./logger.client";
import { MsgSenderService } from "./mgs.sender.service";
import { QueueConst } from "../shared/constant/queue.const";
import { NotifierClient } from "./notifier.client";
import { StsClient } from "./sts.client";
import { MailerClient } from "./mailer.client";
import { NotificationClient } from "./notification.client";
import { PropertyClient } from "./property.client";
import { OrgchartClient } from "./orgchart.client";
import { EmployeeClient } from "./employee.client";
import { DemandClient } from "./demand.client";
import { PrimaryContractClient } from "./primary-contract.client";
import { CareClient } from "./care.client";
import { RatingClient } from "./rating.client";

const queues = [
  { queue: QueueConst.LOGGER_QUEUE, client: LoggerClient },
  { queue: QueueConst.NOTIFIER_QUEUE, client: NotifierClient },
  { queue: QueueConst.NOTIFICATION_QUEUE, client: NotificationClient },
  { queue: QueueConst.STS_QUEUE, client: StsClient },
  { queue: QueueConst.MAILER_QUEUE, client: MailerClient },
  { queue: QueueConst.DEMAND_QUEUE, client: DemandClient },
  { queue: QueueConst.EMPLOYEE_QUEUE, client: EmployeeClient },
  { queue: QueueConst.PROPERTY_QUEUE, client: PropertyClient },
  { queue: QueueConst.ORGCHART_QUEUE, client: OrgchartClient },
  { queue: QueueConst.RATING_QUEUE, client: RatingClient },
  { queue: QueueConst.PRIMARY_CONTRACT_QUEUE, client: PrimaryContractClient },
  { queue: QueueConst.CARE_QUEUE, client: CareClient },
];

@Module({
  imports: [],
  providers: [
    MsgSenderService,
    ...queues.map(({ queue, client }) => {
      return {
        provide: queue,
        useFactory: async (configService: ConfigService) => {
          console.log(
            "test load config in msg-sender ",
            configService.get("RABBITMQ_URL")
          );
          return ClientProxyFactory.create({
            transport: Transport.RMQ,
            options: {
              urls: [configService.get("RABBITMQ_URL")],
              queue: queue,
              queueOptions: { durable: false },
            },
          });
        },
        inject: [ConfigService],
      };
    }),
    ...queues.map(({ client }) => {
      return client;
    }),
  ],
  exports: [
    LoggerClient,
    MsgSenderService,
    NotifierClient,
    StsClient,
    MailerClient,
    NotificationClient,
    PropertyClient,
    OrgchartClient,
    EmployeeClient,
    DemandClient,
    PrimaryContractClient,
    CareClient,
    RatingClient,
  ],
})
export class MgsSenderModule {}
