import { Controller, Get, Param, Query } from '@nestjs/common';
import { OrgchartService } from './service';
import * as _ from 'lodash';

interface IPagingQuery {
  page: number;
  pageSize: number;
};

@Controller('v1/orgchart')
export class OrgchartController {

  constructor(
    private readonly orgchartService: OrgchartService
  ) { }

  @Get('/dropdown')
  async getDropdown(@Query() query: IPagingQuery) {
    return await this.orgchartService.getDropdown(query);
  }

  @Get('/dropdown/:id')
  async getOrgchartById(@Param('id') id: string) {
    return await this.orgchartService.getOrgchartById(id);
  }

  @Get('/dropdownById')
  async getOrgchartByIds(@Query('id') id: string) {
    const ids = id.split(',');
    return await this.orgchartService.getOrgchartByIds(ids);
  }
}