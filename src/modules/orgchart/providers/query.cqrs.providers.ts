import { Connection } from "mongoose";
import { CommonConst } from "../../../modules/shared/constant/common.const";
import { QuerySchema } from "../schemas/query.schema";

export const OrgchartQueryProviders = [
  {
    provide: CommonConst.QUERY_MODEL_TOKEN,
    useFactory: (connection: Connection) => connection.model(CommonConst.ORGCHART, QuerySchema),
    inject: [CommonConst.QUERY_CONNECTION_TOKEN],
  },
];
