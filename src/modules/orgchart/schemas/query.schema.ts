import * as mongoose from 'mongoose';

export const QuerySchema = new mongoose.Schema({
  _id: { type: String },
  id: { type: String, index: true },
  name: { type: String },
  code: { type: String },
  staffIds: { type: Array, default: [] },
  lineManager: { type: String },
  level: { type: Number, default: null },
  type: { type: String, default: null },
  lastSync: { type: Date },
});

QuerySchema.pre('save', function (next) {
  this._id = this.get('id');
  next();
});
