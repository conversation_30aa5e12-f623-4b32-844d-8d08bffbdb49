import { Injectable } from '@nestjs/common';
import { AwesomeLogger } from '../../../shared-modules';
import { OrgchartQueryRepository } from './repository/query.repository';

@Injectable()
export class OrgchartService {

  private readonly logger = new AwesomeLogger(OrgchartService.name);

  constructor(
    private readonly repository: OrgchartQueryRepository,
  ) { }

  async getDropdown(query) {
    // prepare orgcharts employees
    await this.repository.prepareData(query);

    const result = await this.repository.getDropdown(query);
    return result;
  }

  async getOrgchartById(id) {
    const result = await this.repository.getOrgchartById(id);
    return result;
  }

  async getOrgchartByIds(ids) {
    const result = await this.repository.getOrgchartByIds(ids);
    return result;
  }
}
