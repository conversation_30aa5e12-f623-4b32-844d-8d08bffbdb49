import { Module } from '@nestjs/common';
import { OrgchartController } from './controller';
import { SharedModule } from "../../../shared-modules";
import { MgsSenderModule } from '../mgs-sender/mgs-sender.module';
import { OrgchartService } from './service';
import { OrgchartQueryRepository } from './repository/query.repository';
import { OrgchartQueryProviders } from './providers/query.cqrs.providers';
import { QueryDatabaseModule } from '../database/query/query.database.module';
import { EmployeeQuerySideModule } from '../employee/module';

@Module({
  imports: [SharedModule, MgsSenderModule, QueryDatabaseModule, EmployeeQuerySideModule],
  controllers: [OrgchartController],
  providers: [
    OrgchartService,
    OrgchartQueryRepository,
    ...OrgchartQueryProviders
  ],
  exports: [OrgchartQueryRepository, OrgchartModule]
})
export class OrgchartModule { }
