import { HttpModule, Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { QueryDatabaseModule } from '../database/query/query.database.module';
import { LoggerModule } from '../logger/logger.module';
import { MgsSenderModule } from '../mgs-sender/mgs-sender.module';
import { LeadRepoCareQueryCommandHandlers } from './command/handlers';
import { LeadRepoCareController } from './controller';
import { LeadRepoCareQueryEventHandlers } from './events/handlers';
import { LeadRepoCareQueryProviders } from './providers/query.provider';
import { LeadRepoCareQueryRepository } from './repositories/query.repository';
import { LeadRepoCareQueryService } from './service';

@Module({
    imports: [
        CqrsModule,
        QueryDatabaseModule,
        MgsSenderModule,
        LoggerModule,
        HttpModule,
    ],
    exports: [LeadRepoCareQueryService, LeadRepoCareQueryRepository],
    providers: [
        LeadRepoCareQueryService,
        ...LeadRepoCareQueryCommandHandlers,
        ...LeadRepoCareQueryEventHandlers,
        ...LeadRepoCareQueryProviders,
        LeadRepoCareQueryRepository,
    ],
    controllers: [LeadRepoCareController],
})
export class LeadRepoCareQueryModule {}
