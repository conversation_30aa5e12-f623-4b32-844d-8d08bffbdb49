
import { Controller } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { EmployeeService } from '../employee/service';
import { RawService } from '../raw/service';
import { LeadHistoryService } from '../lead-history/service';
import { LeadService } from '../lead.queryside/service';
import { CmdPatternConst } from '../shared/constant/cmd-pattern.const';
import { CommonConst } from '../shared/constant/common.const';
import { isNullOrUndefined } from 'util';
import { LeadDomainService } from '../lead.domain/service';
import { Action } from '../shared/enum/action.enum';
import { NotificationClient } from '../mgs-sender/notification.client';
import _ = require('lodash');
import { EmployeeQueryRepository } from '../employee/repository/query.repository';
import { LeadDomainServiceExtends } from '../lead.domain/service.extend';
import { LeadCareDomainServiceExtends } from "../leadCare.domain/service.extend";
import { LeadCareDomainService } from "../leadCare.domain/service";
import { ConfigService } from '@nestjs/config';
import { LeadCareService } from '../leadCare.queryside/service';
import { CmdPatternConst as CmdPatternConst2 } from '../../../shared-modules';
import { LeadQueryRepository } from '../lead.queryside/repository/query.repository';

@Controller('listener')
export class ListenerController {
  private recordNumber: number;

  constructor(
    private readonly employeeService: EmployeeService,
    private readonly leadService: LeadService,
    private readonly rawService: RawService,
    private readonly leadHistoryService: LeadHistoryService,
    private readonly leadDomainService: LeadDomainService,
    private readonly leadCareDomainService: LeadCareDomainService,
    private readonly leadDomainSrv: LeadDomainServiceExtends,
    private readonly leadCareDomainSrv: LeadCareDomainServiceExtends,
    private readonly notificationClient: NotificationClient,
    private readonly employeeQueryRepository: EmployeeQueryRepository,
    private readonly leadCareQueryService: LeadCareService,
    private readonly leadQueryRepo: LeadQueryRepository,
    private readonly configService: ConfigService
  ) {
    this.recordNumber = parseInt(this.configService.get('RECORD_NUMBER_IN_JOB')) || 10;
  }


  @MessagePattern({ cmd: CmdPatternConst.LISTENER.ORGCHART })
  listenerOrgchart(pattern: any) {
    const data = pattern.data;
    const action = data.action;
    const model = data.model;
    console.log('listenerOrgchart/action =>', action);
    switch (action) {
      case CommonConst.AGGREGATES_LISTENER.ORGCHART.UPDATED:
        this.employeeService.findAndUpdate(model);
        break;
      case CommonConst.AGGREGATES_LISTENER.ORGCHART.LIST_UPDATED:
        this.employeeService.updateList(model);
        //update pos
        let posList: any[] = _.uniqBy(model, 'pos.id');
        posList.map(async item => {
          if (item.pos && item.pos.id) {
            await this.employeeQueryRepository.updateMany(
              { 'pos.id': item.pos.id },
              {
                pos: item.pos
              });
          } else {
            await this.employeeQueryRepository.updateMany(
              { 'pos.staffIds': item.id },
              {
                $pull: {
                  'pos.staffIds': { $in: [item.id] }
                }
              });
          }
        });
        break;
      default:
        console.log('Data unsupport => ', action);
    }
  }

  @MessagePattern({ cmd: CmdPatternConst.LISTENER.EMPLOYEE })
  listenerEmployee(pattern: any) {
    const data = pattern.data;
    const action = data.action;
    const model = data.model;
    console.log('listenerEmployee/action =>', action);

    switch (action) {
      case CommonConst.AGGREGATES_LISTENER.EMPLOYEE.CREATED:
        this.employeeService.findAndUpdate(model);
        break;
      case CommonConst.AGGREGATES_LISTENER.EMPLOYEE.UPDATED:
        this.employeeService.update(model);
        break;
      case CommonConst.AGGREGATES_LISTENER.EMPLOYEE.LIST_UPDATED:
        this.employeeService.updateList(model);
        break;
      default:
        console.log('Data unsupport => ', action);
    }
  }

  @MessagePattern({ cmd: CmdPatternConst.SERVICE.SCHEDULE.RESET_PENALTY })
  resetPenalty() {
    this.employeeService.resetPenalty();
  }

  @MessagePattern({ cmd: CmdPatternConst.LISTENER.BLANK_ALL_DATA })
  listenerBlankAllData() {
    this.leadService.deleteMany();
    this.rawService.deleteMany();
    this.leadHistoryService.deleteMany();
  }

  @MessagePattern({ cmd: CmdPatternConst.SERVICE.DEMAND.LISTENER })
  demandListener(content: any) {
    const data = content.data;
    const action = data.action;
    const model = data.model;
    switch (action) {
      case CommonConst.AGGREGATES_LISTENER.DEMAND.CREATED: {
        if (!isNullOrUndefined(model.ticket.id)) {
          const submodel: any = {
            id: model.ticket.id,
            surveyCode: model.code
          };
          this.leadDomainService.completeLead(submodel, Action.COMPLETE);

          try {
            const msg = {
              title: `Yêu cầu tư vấn của bạn đã được xử lý.`,
              content: `Yêu cầu của bạn đã được xử lý bởi nhân viên của chúng tôi.
                            Bạn có thể đánh giá mức độ hài lòng thông qua link của nhân viên tư vấn.
                            Chi tiết link đã được gửi qua email của bạn.`,
              entityName: CommonConst.AGGREGATES.LEAD.NAME,
              entityId: model.ticketId,
              eventName: CommonConst.AGGREGATES.LEAD.COMPLETED
            };
            const sender = {};
            const receivers = [model.customer.id];
            this.notificationClient.sendData({ msg, sender, receivers });
          } catch (error) {
            console.log('error => ', error);
          }
        }

        if (!isNullOrUndefined(model.customer.lead.id)) {
          const params: any = {
            id: model.customer.lead.id,
            demandCustomer: {
              id: model.customer.id,
              customerCode: model.customer.code
            }
          }
          this.leadDomainService.updateLead(params, 'Update Lead', model.customer.lead.id)
        }
        break;
      }
      default:
        break;
    }

  }


  @MessagePattern({ cmd: CmdPatternConst.SERVICE.CONSIGNMENT.LISTENER })
  consignmentlistener(content: any) {
    const data = content.data;
    const action = data.action;
    const model = data.model;
    switch (action) {
      case CommonConst.AGGREGATES_LISTENER.CONSIGNMENT.CREATED: {
        if (!isNullOrUndefined(model.ticketId)) {
          const submodel: any = {
            id: model.ticketId,
            surveyCode: model.code
          };
          this.leadDomainService.completeLead(submodel, Action.COMPLETE);
          try {
            const msg = {
              title: `Yêu cầu tư vấn của bạn đã được xử lý.`,
              content: `Yêu cầu của bạn đã được xử lý bởi nhân viên của chúng tôi.
                            Bạn có thể đánh giá mức độ hài lòng thông qua link của nhân viên tư vấn.
                            Chi tiết link đã được gửi qua email của bạn.`,
              entityName: CommonConst.AGGREGATES.LEAD.NAME,
              entityId: model.ticketId,
              eventName: CommonConst.AGGREGATES.LEAD.COMPLETED
            };
            const sender = {};
            const receivers = [model.customer.id];
            this.notificationClient.sendData({ msg, sender, receivers });
          } catch (error) {
            console.log('error => ', error);
          }

        }
        break;
      }
      default:
        break;
    }

  }

  @MessagePattern({ cmd: CmdPatternConst.SERVICE.CUSTOMER.LISTENER })
  customerlistener(content: any) {
    const data = content.data;
    const action = data.action;
    const model = data.model;
    switch (action) {
      case CommonConst.AGGREGATES_LISTENER.CUSTOMER.CREATED: {
        if (!isNullOrUndefined(model.ticketId)) {
          const submodel: any = {
            id: model.ticketId,
            customerId: model.id,
            customer: {
              id: model.customer.id,
              customerCode: model.customer.code
            }
          };
          this.leadDomainService.createLead(submodel, Action.UPDATE);
        }
        break;
      }
      default:
        break;
    }

  }

  @MessagePattern({ cmd: CmdPatternConst.SERVICE.SCHEDULE.TIMEOUT_LEAD })
  timeout() {
    // this.leadDomainService.timeout();
  }

  @MessagePattern({ cmd: CmdPatternConst.SERVICE.SCHEDULE.TIMEOUT_LEAD_CARE })
  timeoutCare() {
    // this.leadCareDomainService.timeout();
  }
  @MessagePattern({ cmd: CmdPatternConst.SERVICE.SCHEDULE.NORMAL_LEAD })
  async processNormalLead() {
    await this.leadDomainSrv.deliverNormalLead(this.recordNumber);
  }

  @MessagePattern({ cmd: CmdPatternConst.SERVICE.SCHEDULE.NORMAL_LEAD_CARE })
  async processNormalLeadCare() {
    await this.leadCareDomainSrv.renewNormalLeadCare(this.recordNumber);
    await this.leadCareDomainSrv.deliverNormalLeadCare(this.recordNumber);
  }
  @MessagePattern({ cmd: CmdPatternConst.SERVICE.SCHEDULE.RENEW_NORMAL_LEAD })
  async renewNormalLead() {
    await this.leadDomainSrv.renewNormalLead(this.recordNumber);
  }

  @MessagePattern({ cmd: CmdPatternConst.SERVICE.SCHEDULE.HOT_LEAD })
  async processHotLead() {
    await this.leadDomainSrv.reAssignHotLead(this.recordNumber);
  }

  @MessagePattern({ cmd: CmdPatternConst.SERVICE.SCHEDULE.UPDATE_STATUS_LEAD })
  updateStatus() {
    // this.leadDomainService.updateStatusLead();
  }

  @MessagePattern({ cmd: CmdPatternConst.SERVICE.LEAD.GET_BY_ID })
  getLeadById(content: any) {
    if (!content || !content.data) return;
    return this.leadService.getLeadById(content.data.id, 'Asia/Ho_Chi_Minh');
  }

  @MessagePattern({ cmd: CmdPatternConst.SERVICE.LEAD.GET_BY_CUSTOMER_ID })
  getLeadByCustomerId(content: any) {
    if (!content || !content.data) return [];

    return Promise.all([
      this.leadService.getLeadByCustomerId(content.data.customerId, false)
    ])
      .then((res) => _.flatMapDeep(res as any[][]))
      .then((res) => {
        const mapping: boolean = content.data.mapping || false;
        if (mapping) {
          return _.groupBy(res, 'customerId');
        }
        return res;
      });
  }
  @MessagePattern({ cmd: CmdPatternConst.SERVICE.LEAD.GET_HISTORY_BY_EMPLOYEE_ID })
  getLeadHisByEmployeeId(content: any) {
    if (!content || !content.data) return [];
    return this.leadHistoryService.getLeadByEmployeeId(content.data.employeeId, content.data.query);
  }

  @MessagePattern({ cmd: CmdPatternConst.SERVICE.SCHEDULE.TIMEOUT_LEAD_CARE_SYSTEM_CLOSED })
  leadCareSystemClosedTimeout(content: any) {
    this.leadCareDomainService.systemClosedTicketLeadCare(Action.CLOSED_TICKET);
  }


  @MessagePattern({ cmd: CmdPatternConst.LISTENER.GET_LEAD_CARE_BY_ID })
  getLeadCareById(content: any) {
    return this.leadCareQueryService.getProjectInfoByLeadCare(content.data);
  }

  @MessagePattern({ cmd: CmdPatternConst.LISTENER.UPDATE_RATE_INFO_IN_LEAD_CARE })
  updateRateAndRateDescriptionLeadCareById(content: any) {
    return this.leadCareDomainService.updateRateInfoLeadCareById(content.data);
  }

  @MessagePattern({ cmd: CmdPatternConst2.LEAD.UPDATE_BY_CODE })
  updateByCode(content: any) {
    return this.leadQueryRepo.updateByCode(content.data);
  }

  @MessagePattern({ cmd: CmdPatternConst2.LEAD.LISTENER.GET_BY_QUERY })
  getByQuery(content: any) {
    return this.leadQueryRepo.findOne(content.data);
  }
}
