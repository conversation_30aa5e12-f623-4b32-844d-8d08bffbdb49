import { Module } from '@nestjs/common';
import { JwtStrategy } from './passport/jwt.strategy';
import { AuthService } from './auth.service';
import { LoggerModule } from '../logger/logger.module';
import { MgsSenderModule } from '../mgs-sender/mgs-sender.module';

@Module({
  imports: [LoggerModule, MgsSenderModule],
  providers: [AuthService, JwtStrategy],
  exports: [AuthService]
})

export class AuthModule { }