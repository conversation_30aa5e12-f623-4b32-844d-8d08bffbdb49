import { Injectable } from "@nestjs/common";
import { EmployeeQueryRepository } from "../employee/repository/query.repository";
import { LeadRepoQueryRepository } from "./repositories/query.repository";
import {
  AccountInfoUtils,
  AwesomeLogger,
  BaseService,
  CmdPatternConst,
  ErrorService,
} from "../../../shared-modules";
import { StsClient } from "../mgs-sender/sts.client";
import * as _ from "lodash";

@Injectable()
export class LeadRepoQueryService extends BaseService {
  private readonly logger = new AwesomeLogger(LeadRepoQueryService.name);

  constructor(
    protected readonly repository: LeadRepoQueryRepository,
    private readonly employeeRepository: EmployeeQueryRepository,
    private readonly stsClient: StsClient,
    public errorService: ErrorService
  ) {
    super(errorService);
  }

  async findAll(user, query) {
    const { createFrom, createTo, updateFrom, updateTo } = query;
    if (createFrom && createTo) {
      const cF = new Date(createFrom);
      const cT = new Date(createTo);
      if (cF > cT) {
        return this.getResponse('COME0009');
      }
    }
    if (updateFrom && updateTo) {
      const uF = new Date(updateFrom);
      const uT = new Date(updateTo);
      if (uF > uT) {
        return this.getResponse('COME0009');
      }
    }
    return await this.repository.findAll(user, query);
  }

  // async getLeadRepoList(
  //   user: any,
  //   page: number,
  //   pageSize: number,
  //   keywords: string
  // ) {
  //   const employee = await this.employeeRepository.findOne({ $or: [{ 'account.id': user.id }, { 'accounts.id': user.id }] });
  //   this.logger.debug('employee', employee);
  //   const whereFilterWithSearch = {
  //     modifiedBy: {
  //       $in: employee.staffIds
  //         ? employee.staffIds.concat(...user.id)
  //         : [user.id],
  //     },
  //     $or: [
  //       { code: { $regex: new RegExp(keywords), $options: "i" } },
  //       { name: { $regex: new RegExp(keywords), $options: "i" } },
  //     ],
  //   };
  //   const total = await this.repository.count(whereFilterWithSearch as any);
  //   const rows = await this.repository.findMany({
  //     where: whereFilterWithSearch as any,
  //     skip: Math.floor(pageSize * page - pageSize),
  //     limit: pageSize,
  //     sort: {
  //       createdDate: -1,
  //     },
  //   });

  //   if (!_.isEmpty(rows)) {
  //     // get all related accounts
  //     // const ids = [user.id];
  //     const accInfo = await this.stsClient.sendDataPromise(
  //       {},
  //       CmdPatternConst.STS.ACCOUNT.GET_INFO
  //     );
  //     rows.map((row) => {
  //       // replace id with email
  //       row.createdBy = accInfo.listEmailById[row.createdBy];
  //       row.modifiedBy = accInfo.listEmailById[row.modifiedBy];

  //       // replace configs
  //       row.configs.map((c) => {
  //         c.createdBy = accInfo.listEmailById[c.createdBy];
  //         c.modifiedBy = accInfo.listEmailById[c.modifiedBy];
  //       });
  //       // replace configHot
  //       row.configHot.createdBy = accInfo.listEmailById[row.configHot.createdBy];
  //       row.configHot.modifiedBy = accInfo.listEmailById[row.configHot.modifiedBy];
  //     });
  //   }

  //   return {
  //     page,
  //     pageSize,
  //     rows,
  //     total,
  //     totalPages: Math.ceil(total / pageSize),
  //   };
  // }

  async getLeadRepoDetail(id: string) {
    const rows = await this.repository.getDetail(id);
    this.logger.debug('rows', rows);
    let mappedRows = [];
    if (rows && rows.length > 0) {
      // get all related accounts
      mappedRows = await AccountInfoUtils.mapAccountInfo(rows, this.stsClient, {
        includeCreatedBy: true,
        includeModifiedBy: true,
        replaceIds: true
      });
    }

    return mappedRows[0];
  }
}
