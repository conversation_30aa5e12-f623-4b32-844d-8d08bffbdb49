import { <PERSON>, Get, Param, Query, UseGuards, UseInterceptors } from "@nestjs/common";
import { A<PERSON><PERSON><PERSON>erA<PERSON>, ApiUseTags } from "@nestjs/swagger";
import { LoggingInterceptor } from "../../common/interceptors/logging.interceptor";
import { LeadRepoQueryService } from "./service";
import { JwtAuthGuard, RoleGuard, Roles, User } from "../../../shared-modules";
import { PermissionConst } from "../shared/constant/permission.const";

interface IPagingQuery {
  page: number;
  pageSize: number;
  search: string;
  createdBy: string;
  createFrom: string;
  createTo: string;
  updateFrom: string;
  updateTo: string;
};

@ApiBearerAuth()
@ApiUseTags("v1/lead-repo")
@Controller("v1/lead-repo")
@UseInterceptors(LoggingInterceptor)
@UseGuards(JwtAuthGuard, RoleGuard)
export class LeadRepoController {
  constructor(protected readonly service: LeadRepoQueryService) { }

  @Get()
  @Roles(PermissionConst.LEAD_CONFIG_GET_ALL)
  async findAll(
    @User() user,
    @Query() query: IPagingQuery
  ) {
    return this.service.findAll(user, query);
  }

  // @Get()
  // @Roles(PermissionConst.LEAD_CONFIG_GET_ALL)
  // async getLeadRepoList(
  //   @User() user,
  //   @Query() { keywords, page, pageSize }: IPagingQuery
  // ) {
  //   page = Number(page);
  //   pageSize = Number(pageSize);
  //   return this.service.getLeadRepoList(user, page, pageSize, keywords);
  // }

  @Get(":id")
  @Roles(PermissionConst.LEAD_CONFIG_GET_ID)
  async getLeadRepoDetail(@Param("id") id: string) {
    return this.service.getLeadRepoDetail(id);
  }
}
