import { Inject, Injectable } from "@nestjs/common";
import { Model } from "mongoose";
import { CommonConst } from "../../shared/constant/common.const";
import { LeadRepo } from "../../shared/models/leadRepo/model";
import { BaseRepository } from "../../shared/services/baseRepository/repository.base";
import { ILeadRepoQueryDocument } from "../interfaces/document.interface";
import { AccountInfoUtils, AwesomeLogger, CmdPatternConst } from "../../../../shared-modules";
import * as _ from "lodash";
import { StsClient } from "../../mgs-sender/sts.client";

@Injectable()
export class LeadRepoQueryRepository extends BaseRepository<
  ILeadRepoQueryDocument,
  LeadRepo
> {
  private readonly logger = new AwesomeLogger(LeadRepoQueryRepository.name);

  constructor(
    @Inject(CommonConst.QUERY_MODEL_TOKEN)
    protected readonly repository: Model<ILeadRepoQueryDocument>,
    private readonly stsClient: StsClient
  ) {
    super();
  }

  find(query) {
    return this.repository.find(query);
  }

  async update(model): Promise<ILeadRepoQueryDocument> {
    return await this.repository
      .updateOne({ id: model.id }, model)
      .then((response) => {
        this.logger.info("update lead repo at query side");
        return response;
      })
      .catch((error) => {
        this.logger.error("[error] update lead repo at query side", error);
        return error;
      });
  }

  async findAll(user, query) {
    let {
      page,
      pageSize,
      search,
      createdBy,
      createFrom,
      createTo,
      updateFrom,
      updateTo,
    } = query;
    page = Number(page);
    pageSize = Number(pageSize);

    const whereFilterWithSearch: any = {};

    if (search) {
      whereFilterWithSearch.$or = [
        { code: { $regex: new RegExp(search), $options: "i" } },
        { name: { $regex: new RegExp(search), $options: "i" } },
      ];
    }
    if (createdBy) {
      whereFilterWithSearch.createdBy = { $eq: createdBy };
    }

    let cDateFrom = 0;
    let cDateTo = 0;
    if (createFrom && createTo) {
      cDateFrom = Number(new Date(createFrom));
      cDateTo = Number(new Date(createTo)) + 86399999;
      whereFilterWithSearch.createdDate = {
        $gte: new Date(cDateFrom),
        $lte: new Date(cDateTo),
      };
    } else if (createFrom) {
      cDateFrom = Number(new Date(createFrom));
      whereFilterWithSearch.createdDate = { $gte: new Date(cDateFrom) };
    } else if (createTo) {
      cDateTo = Number(new Date(createTo)) + 86399999;
      whereFilterWithSearch.createdDate = { $lte: new Date(cDateTo) };
    }

    let mDateFrom = 0;
    let mDateTo = 0;
    if (updateFrom && updateTo) {
      mDateFrom = Number(new Date(updateFrom));
      mDateTo = Number(new Date(updateTo)) + 86399999;
      whereFilterWithSearch.updatedDate = {
        $gte: new Date(mDateFrom),
        $lte: new Date(mDateTo),
      };
    } else if (updateFrom) {
      mDateFrom = Number(new Date(updateFrom));
      whereFilterWithSearch.updatedDate = { $gte: new Date(mDateFrom) };
    } else if (updateTo) {
      mDateTo = Number(new Date(updateTo)) + 86399999;
      whereFilterWithSearch.updatedDate = { $lte: new Date(mDateTo) };
    }

    let [rows, total] = await Promise.all([
      this.repository.aggregate([
        { $match: whereFilterWithSearch },
        { $sort: { updatedDate: -1 } },
        { $skip: Math.floor(pageSize * page - pageSize) },
        { $limit: pageSize },
        {
          $project: {
            _id: 0,
            id: "$id",
            name: "$name",
            code: "$code",
            createdDate: "$createdDate",
            createdBy: "$createdBy",
            updatedDate: "$updatedDate",
            updatedBy: "$modifiedBy",
            configHot: 1,
            configs: {
              $filter: {
                input: '$configs',
                as: 'config',
                cond: { $eq: ['$$config.softDelete', false] }
              }
            },
          },
        },
      ]),
      this.repository.countDocuments(whereFilterWithSearch),
    ]);
    this.logger.debug("rows info", rows);

    let mappedRows = [];
    if (!_.isEmpty(rows)) {
      // get all related accounts
      mappedRows = await AccountInfoUtils.mapAccountInfo(rows, this.stsClient, {
        includeCreatedBy: true,
        includeUpdatedBy: true,
        replaceIds: true
      });
      mappedRows.map((row) => {
        // count number of configs (exclude softDelete true)
        let count = 0;
        if (row.configs && row.configs.length > 0) {
          count += row.configs.length;
        }
        if (row.configHot) {
          count += 1;
        }
        row.configNumber = count;

        delete row.configs;
        delete row.configHot;
      });
    }

    return {
      rows: mappedRows,
      page,
      pageSize,
      total,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  async getDetail(id) {
    return await this.repository.aggregate([
      { $match: { id } },
      {
        $project: {
          _id: 0,
          id: "$id",
          code: "$code",
          name: "$name",
          createdBy: "$createdBy",
          createdDate: "$createdDate",
          modifiedBy: "$modifiedBy",
          updatedDate: "$updatedDate",
          configs: {
            $filter: {
              input: "$configs",
              as: "item",
              cond: { $eq: ["$$item.softDelete", false] },
            },
          },
          configHot: "$configHot",
        },
      },
    ]);
  }
}
