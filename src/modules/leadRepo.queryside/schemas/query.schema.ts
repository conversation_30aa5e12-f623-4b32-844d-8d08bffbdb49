import * as mongoose from "mongoose";
import { setKeywords } from "../../shared/plugins/keywords";
import { BaseSchema } from "../../shared/schema/schema.base";
import { DeliverTypeEnum } from "../../shared/enum/deliver-type.enum";

export const DateRangeSchema = new mongoose.Schema(
  {
    from: { type: Date },
    to: { type: Date },
  },
  { _id: false }
);

export const NotificationInstanceSchema = new mongoose.Schema(
  {
    title: { type: String },
    content: { type: String },
    active: { type: Boolean },
  },
  { _id: false }
);

export const NotificationSchema = new mongoose.Schema(
  {
    email: NotificationInstanceSchema,
    web: NotificationInstanceSchema,
    app: NotificationInstanceSchema,
    sms: NotificationInstanceSchema,
    smsCus: NotificationInstanceSchema,
    notiUser: { type: Object },
  },
  { _id: false }
);

export const ValueSchema = new mongoose.Schema(
  {
    name: { type: String },
    code: { type: String },
    value: { type: Boolean, default: false },
  },
  { _id: false }
);

export const SurveySchema = new mongoose.Schema(
  {
    type: { type: String },
    name: { type: String },
    values: { type: [ValueSchema] },
    text: { type: String },
    multilineText: { type: String },
  },
  { _id: false }
);

export const OrgChartQueueSchema = new mongoose.Schema(
  {
    id: { type: String },
    employeeQueue: { type: [Object] },
  },
  { _id: false }
);

export const OrgChartSchema = new mongoose.Schema(
  {
    id: { type: String },
    name: { type: String },
    staffIds: { type: [String] },
    deassignedStaffIds: { type: [String], default: [] },
  },
  { _id: false }
);

export const ProjectSchema = new mongoose.Schema(
  {
    id: { type: String },
    name: { type: String },
    staffIds: { type: [String] },
    deassignedStaffIds: { type: [String], default: [] },
  },
  { _id: false }
);

export const WorkingTimeSchema = new mongoose.Schema(
  {
    startTime: String,
    endTime: String,
  },
  { _id: false }
);

export const LeadRepoConfigSchema = new mongoose.Schema(
  {
    orgChartIds: { type: [String] },
    orgChartQueue: [OrgChartQueueSchema],
    orgCharts: [OrgChartSchema],
    assignDuration: { type: Number },
    deassignLimit: { type: Number, default: 0 },
    notification: NotificationSchema,
    projectId: { type: String },
    project: ProjectSchema,
    exploitTime: DateRangeSchema,
    code: { type: String },
    name: { type: String },
    active: { type: Number, default: 1 },
    surveys: [SurveySchema],
    visiblePhone: { type: Boolean, default: false },
    // manualDeliver: { type: Boolean, default: false },
    deliverType: { type: DeliverTypeEnum, default: 1 },
    isWorkingTime: { type: Boolean, default: false },
    workingTime: [WorkingTimeSchema],

    softDelete: { type: Boolean, default: false },
    softDeleteReason: { type: String, default: null },
  },
  { _id: false }
);

export const LeadRepoConfigHotSchema = new mongoose.Schema(
  {
    assignDuration: { type: Number },
    deassignLimit: { type: Number, default: 0 },
    orgChartIds: { type: [String] },
    orgChartQueue: [OrgChartQueueSchema],
    orgCharts: [OrgChartSchema],
    notification: NotificationSchema,
    visiblePhone: { type: Boolean, default: false },
    // manualDeliver: { type: Boolean, default: false },
    deliverType: { type: DeliverTypeEnum, default: 1 },
    isWorkingTime: { type: Boolean, default: false },
    workingTime: [WorkingTimeSchema],
  },
  { _id: false }
);

export const LeadRepoSchema: mongoose.SchemaDefinition = {
  ...BaseSchema,
  code: { type: String, index: true },
  name: { type: String },
  configHot: LeadRepoConfigHotSchema,
  configs: [LeadRepoConfigSchema],

  createdBy: { type: String },
  modifiedBy: { type: String },
  createdDate: { type: Date, default: () => Date.now() },
  updatedDate: { type: Date, default: () => Date.now() },
};

export const QuerySchema = new mongoose.Schema({
  ...LeadRepoSchema,
  keywords: { type: [String], text: true, index: true },
});

QuerySchema.pre("save", function (next) {
  this._id = this.get("id");
  next();
});

QuerySchema.plugin(setKeywords(["code", "name"]));
