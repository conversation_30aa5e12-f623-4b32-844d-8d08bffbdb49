import { forwardRef, HttpModule, Module } from "@nestjs/common";
import { CqrsModule } from "@nestjs/cqrs";
import { QueryDatabaseModule } from "../database/query/query.database.module";
import { EmployeeQuerySideModule } from "../employee/module";
import { LoggerModule } from "../logger/logger.module";
import { MgsSenderModule } from "../mgs-sender/mgs-sender.module";
import { LeadRepoQueryCommandHandlers } from "./command/handlers";
import { LeadRepoController } from "./controller";
import { LeadRepoQueryEventHandlers } from "./events/handlers";
import { LeadRepoQueryProviders } from "./providers/query.provider";
import { LeadRepoQueryRepository } from "./repositories/query.repository";
import { LeadRepoQueryService } from "./service";
import { SharedModule } from "../../../shared-modules";

@Module({
  imports: [
    CqrsModule,
    QueryDatabaseModule,
    MgsSenderModule,
    LoggerModule,
    HttpModule,
    forwardRef(() => EmployeeQuerySideModule),
    SharedModule,
  ],
  exports: [LeadRepoQueryService, LeadRepoQueryRepository],
  providers: [
    LeadRepoQueryService,
    ...LeadRepoQueryCommandHandlers,
    ...LeadRepoQueryEventHandlers,
    ...LeadRepoQueryProviders,
    LeadRepoQueryRepository,
  ],
  controllers: [LeadRepoController],
})
export class LeadRepoQueryModule {}
