import { ValidatorConstraint, ValidationArguments, ValidatorConstraintInterface } from 'class-validator';
import { isNullOrUndefined } from 'util';
import { ExploitEnum } from '../../shared/enum/exploit.enum';
import { MaritalStatusEnum } from '../../shared/enum/marital-status.enum';
import { SexEnum } from '../../shared/enum/sex.enum';
import { SurveyTypeEnum } from '../enum/survey.enum';

@ValidatorConstraint({ name: 'stringNotBlank', async: false })
export class IsStringNotBlank implements ValidatorConstraintInterface {

    validate(text: string, args: ValidationArguments) {
        if (isNullOrUndefined(text)) {
            return false;
        }
        return text.trim().length > 0; // validate String is not blank
    }

    defaultMessage(args: ValidationArguments) {
        return 'Value is blank or empty';
    }
}
@ValidatorConstraint({ name: 'legalLeadStatus', async: false })
export class IsLegalLeadStatus implements ValidatorConstraintInterface {

    validate(text: string, args: ValidationArguments) {
        if (![ExploitEnum.ASSIGN, ExploitEnum.NEW, ExploitEnum.CANCEL, ExploitEnum.DONE, ExploitEnum.PROCESSING, ExploitEnum.RENEW,
            // ExploitEnum.REASSIGN
        ].includes(text as any)) {
            return false;
        }
        return true;
    }

    defaultMessage(args: ValidationArguments) {
        return `Value must be ${ExploitEnum.ASSIGN}, ${ExploitEnum.NEW}, ${ExploitEnum.CANCEL}, ${ExploitEnum.DONE}, ${ExploitEnum.PROCESSING}, ${ExploitEnum.RENEW}`;
    }
}
@ValidatorConstraint({ name: 'legalSex', async: false })
export class IsLegalSex implements ValidatorConstraintInterface {

    validate(text: string, args: ValidationArguments) {
        if (!!text) {
            if (![SexEnum.FEMALE, SexEnum.MALE, SexEnum.OTHER].includes(text as any)) {
                return false;
            }
            return true;
        }
        return true;
    }

    defaultMessage(args: ValidationArguments) {
        return `Value must be ${SexEnum.FEMALE}, ${SexEnum.MALE}, ${SexEnum.OTHER}`;
    }
}
@ValidatorConstraint({ name: 'legalMaritalStatus', async: false })
export class IsLegalMaritalStatus implements ValidatorConstraintInterface {

    validate(text: string, args: ValidationArguments) {
        if (!!text) {
            if (![MaritalStatusEnum.ALONE, MaritalStatusEnum.MARRIED, MaritalStatusEnum.OTHER].includes(text as any)) {
                return false;
            }
            return true;
        }
        return true;
    }

    defaultMessage(args: ValidationArguments) {
        return `Value must be ${MaritalStatusEnum.ALONE}, ${MaritalStatusEnum.MARRIED}, ${MaritalStatusEnum.OTHER}`;
    }
}
@ValidatorConstraint({ name: 'legalIdentify', async: false })
export class IsLegalIdentify implements ValidatorConstraintInterface {

    validate(input: any, args: ValidationArguments) {
        let flag: boolean = true;
        if (!!input) {
            Object.keys(input).every((key: string) => {
                if (!['type', 'num', 'date', 'issueBy'].includes(key)) {
                    flag = false;
                    return false;
                }
            });
            return flag;
        }
        return true;
    }

    defaultMessage(args: ValidationArguments) {
        return `Value must be a object with key includes: type, num, date, issueBy`;
    }
}

@ValidatorConstraint({ name: 'legalSurvey', async: false })
export class IsLegalSurvey implements ValidatorConstraintInterface {

    validate(input: any, args: ValidationArguments) {
        let flag: boolean = true;
        if (!!input && Array.isArray(input)) {
            input.every((item: object) => {
                Object.keys(item).every((key: string) => {
                    if (!['type', 'name', 'value', 'code'].includes(key)) {
                        flag = false;
                        return false;
                    }
                });
            });
            return flag;
        } else flag = false;
        return true;
    }

    defaultMessage(args: ValidationArguments) {
        return `Value must be a array object with key includes: type, name, value, code`;
    }
}

@ValidatorConstraint({ name: 'surveyValuesValidator', async: false })
export class IsSurveyValuesValid implements ValidatorConstraintInterface {
    validate(values: any[], args: ValidationArguments) {
        const object = args.object as any;

        // If type is TEXTBOX or MULTILINETEXTBOX, values field is optional
        if (object.type === SurveyTypeEnum.TEXTBOX || object.type === SurveyTypeEnum.MULTILINETEXTBOX) {
            return true;
        }

        // For other types, values must be an array with valid items
        if (!Array.isArray(values) || values.length === 0) {
            return false;
        }

        // Check each value object has required properties
        return values.every(value =>
            value &&
            typeof value.name === 'string' &&
            value.name.length <= 50 &&
            value.name.length > 0 &&
            typeof value.code === 'string' &&
            value.code.length <= 10 &&
            value.code.length > 0
        );
    }

    defaultMessage(args: ValidationArguments) {
        return 'Values array is required and must contain valid items with non-empty name (max 50 chars) and code (max 10 chars) for this survey type';
    }
}
