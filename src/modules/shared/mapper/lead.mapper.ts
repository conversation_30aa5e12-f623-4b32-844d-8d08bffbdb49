import { isNullOrUndefined } from "util";
import { StatusEnum } from "../enum/status.enum";
import { ILead } from "../services/lead/interfaces/lead.interface";

const moment = require('moment-timezone');
const objectMapper = require('object-mapper');

export class LeadMapper {
  private timezoneclient: string;
  constructor(timezoneclient: string) {
    this.timezoneclient = timezoneclient;
    process.env.timezone = timezoneclient;
  }
  leadResponse = {
    'id': 'id',
    'name': 'name',
    "address": "address",
    "phone": "phone",
    "email": "email",
    "profileUrl": "profileUrl",
    "pos": {
      key: "pos",
      transform: function (value) {
        if (isNullOrUndefined(value)) return;
        return { id: value.id, name: value.name, parentId: value.parentId };
      }
    },
    "status": [
      { key: "status" },
      {
        key: "periodStatus",
        transform: (value => {
          let rs = '';
          if (isNullOrUndefined(value)) return;
          switch (value) {
            case StatusEnum.GREEN:
              rs = 'Mới';
              break;
            case StatusEnum.YELLOW:
              rs = 'Chờ';
              break;
            case StatusEnum.RED:
              rs = 'Ưu tiên';
              break;
            default:
              break;
          }
          return rs;
        })
      }],
    "lifeCycleStatus": "lifeCycleStatus",
    "type": "type",
    "timezoneclient": "timezoneClient",
    "createdDate": {
      key: "createdDate",
      transform: (value => this.convertDate(value))
    },
    "updatedDate": {
      key: "updatedDate",
      transform: (value => this.convertDate(value))
    },
    "processedTicketDate": {
      key: "processedTicketDate",
      transform: (value => this.convertDate(value))
    },
    "assignedDate": {
      key: "assignedDate",
      transform: (value => this.convertDate(value))
    },
    "t0": {
      key: "t0",
      transform: (value => this.convertDate(value))
    },
    "t1": {
      key: "t1",
      transform: (value => this.convertDate(value))
    },
    "t2": {
      key: "t2",
      transform: (value => this.convertDate(value))
    },
    "t3": {
      key: "t3",
      transform: (value => this.convertDate(value))
    },
    "timeOut": {
      key: "timeOut",
      transform: (value => this.convertDate(value))
    },
    "notes": "notes",
    "description": "description",
    "reason": "reason",
    "decisionDate": "decisionDate",
    "customerId": "customerId",
    "source": "source",
    "code": "code",
    "customer": "customer",
    "employee": "employee",
    "property": "property",
    "surveys": "surveys",
    "processedDate": "processedDate",
    "processBy": "processBy",
    "employeeTakeCare": "employeeTakeCare",
    "processedTicketCode": "processedTicketCode",
    "isCalled": "isCalled",
    'advisingType': 'advisingType',
    'images': 'images',
    'price': 'price',
    'categoryId': 'categoryId',
    'configData': 'configData',
    'desirablePrice': 'desirablePrice',
    'category': 'category',
    'updatedName': 'updatedName',
    'updatedPhone': 'updatedPhone',
    'updatedEmail': 'updatedEmail',
    'updatedProfileUrl': 'updatedProfileUrl',
    'isInNeed': 'isInNeed',
    'reasonNoNeed': 'reasonNoNeed',
    'otherReason': 'otherReason',
    'interestedProduct': 'interestedProduct',
    'direction': 'direction',
    'needLoan': 'needLoan',
    'isAppointment': 'isAppointment',
    'isVisited': 'isVisited',
    'note': 'note',
    'callHistory': 'callHistory',
    'callHistoryCount': 'callHistoryCount',
    'callHistoryMinuteCount': 'callHistoryMinuteCount',
    'importedBy': 'importedBy',
    'exploitStatus': 'exploitStatus',
    'createdBy': 'createdBy',
    'modifiedBy': 'modifiedBy',
    'createdByObj': 'createdByObj',
    'updatedByObj': 'updatedByObj',
    'modifiedByObj': 'modifiedByObj',
    'takeCare': 'takeCare',
    exploitHistory: 'exploitHistory',
    project: 'project',
    isHot: 'isHot',
    visiblePhone: 'visiblePhone',
    repo: 'repo',
    demandCustomer: 'demandCustomer',
    orgCode: 'orgCode',
    orgName: 'orgName'
  };

  convertDate(value) {
    if (isNullOrUndefined(value)) return;
    return moment(value).tz(this.timezoneclient || 'Asia/Ho_Chi_Minh').format();
  }

  enityToDto(entity) {
    // console.log('entity', entity);
    return objectMapper(entity, this.leadResponse);
  }

  enitiesToDtos(entities) {
    let dtos = [];
    if (isNullOrUndefined(entities)) {
      return;
    }

    entities.forEach(entity => {
      dtos.push(this.enityToDto(entity));
    });
    return dtos;
  }
}