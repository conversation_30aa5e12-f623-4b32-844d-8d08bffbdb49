import { Schema, SchemaDefinition } from 'mongoose';
import uuid = require('uuid');

export const BaseSchema: SchemaDefinition = {
    _id: { type: String },
    id: { type: String, default: uuid.v4, index: true },
    description: { type: String },
    active: { type: Boolean },
    softDelete: { type: Boolean },
    createdBy: { type: String },
    modifiedBy: { type: String },
    timezoneClient: { type: String },
    createdDate: { type: Date, default: () => Date.now() },
    updatedDate: { type: Date, default: () => Date.now() },
};

export const getEventStreamSchema = (payload: Schema): Schema => {
    return new Schema({
        ...BaseSchema,
        payload,
        payloads: [payload],
        streamId: { type: String, index: true },
        aggregate: String,
        aggregateId: String,
        context: String,
        streamRevision: Number,
        commitId: String,
        commitSequence: Number,
        commitStamp: { type: Date, default: () => Date.now() },
        eventName: { type: String },
    });
};
