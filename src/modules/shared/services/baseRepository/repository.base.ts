import { BadRequestException, Injectable } from '@nestjs/common';
import { Document, Model } from 'mongoose';
import { ErrorConst } from '../../constant/error.const';
import {
  FindManyFilter,
  FindOneFilter,
  WhereFilter,
} from '../../interfaces/baseFilter.interface';
import * as util from 'util';

@Injectable()
export class BaseRepository<
  T extends Document,
  TFilter = T,
  TInput = T,
  TUpdate = Partial<T>
> {
  protected readonly repository: Model<T>;

  async bulkUpdate(records: T[] = []) {
    if (records.length) {
      const bulkData = records.map((item) => {
        if (typeof (item as any).toObject === 'function') {
          item = (item as any).toObject();
        }
        return {
          updateOne: {
            filter: { id: item.id },
            update: { $set: { _id: item.id, ...item } },
            upsert: true,
            setDefaultsOnInsert: true
          },
        };
      });

      return this.repository
        .bulkWrite(bulkData)
        .then((r) => {
          const { upsertedCount, modifiedCount } = r;
          console.log(
            `BulkWrite ${this.repository.modelName} result `,
            { upsertedCount, modifiedCount }
          );
        })
        .catch((e) => console.error(e));
    }
  }

  async aggregate(aggs: [Record<string, any>]) {
    return this.repository.aggregate(aggs);
  }

  async count(where: WhereFilter<TFilter>): Promise<number> {
    return this.repository.count(this.parseSearchFilter(where)).exec();
  }

  async findByQuery(where) {
    return this.repository.find(where).exec();
  }

  async getLeadRepoCareByProject(projectId) {
    return this.repository.aggregate([{
      $match: { 'configs.project.id': projectId }
    },
    {
      $unwind: '$configs',
    },
    {
      $match: { 'configs.project.id': projectId }
    },
    {
      $project: {
        id: '$configs.id',
        code: '$configs.code',
        name: '$configs.name',
        type: '$type',
      }
    }]).exec();
  }

  async findMany(
    filter: FindManyFilter<TFilter>,
    lean = false
  ): Promise<T[]> {
    const { where, projection, sort, skip, limit } = filter;
    const query = this.repository.find(
      this.parseSearchFilter(where),
      projection,
      {
        sort,
        skip,
        limit,
      }
    );
    if (lean) return query.lean();
    return query;
  }

  async findOne(filter: FindOneFilter<TFilter>, lean = false): Promise<T> {
    const { sort, where, projection } = filter;
    const query = this.repository
      .findOne(this.parseSearchFilter(where), projection)
      .sort(sort);
    if (lean) return query.lean();
    return query;
  }

  async findById(id: string, lean = false): Promise<T> {
    const query = this.repository.findById(id);
    if (lean) return query.lean();
    return query;
  }

  async create(record: TInput): Promise<T> {
    const data = await this.repository.create(record);
    if (!data) {
      throw new BadRequestException({
        errors: ErrorConst.CommonError(
          ErrorConst.INTERNAL_SERVER,
          'Create data'
        ),
      });
    }
    return data;
  }

  async updateById(_id: string, record: TUpdate) {
    await this.repository
      .findOneAndUpdate({ _id }, record)
      .then((res) => {
        return res;
      })
      .catch((e) => {
        throw new BadRequestException({
          errors: ErrorConst.CommonError(
            ErrorConst.INTERNAL_SERVER,
            'Update data'
          ),
        });
      });
  }

  async removeById(_id: string) {
    await this.repository
      .findOneAndRemove({ _id })
      .then((res) => res)
      .catch((e) => {
        throw new BadRequestException({
          errors: ErrorConst.CommonError(
            ErrorConst.INTERNAL_SERVER,
            'Update data'
          ),
        });
      });
  }

  private parseSearchFilter(where: WhereFilter<TFilter> = {}) {
    const { _search } = where;
    if (_search) {
      Object.assign(where, { $text: { $search: `"${_search}"` } });
    }
    delete where._search;
    return where;
  }
}
