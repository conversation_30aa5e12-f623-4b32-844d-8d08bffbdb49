import { ExploitEnum } from '../../../enum/exploit.enum';
import { IBaseInterface } from '../../../interfaces/base.interface';

export interface IExploitHistory {
    status: ExploitEnum;
    updatedAt: Date;
    updatedBy?: string;
    takeCareId?: string;
    takeCareInfo?: ITakeCare;
}

export interface ITakeCare {
  id: string;
  name: string;
  email: string;
  phone: string;
}

export interface IProject {
  id: string;
  name: string;
}

export interface ILead extends IBaseInterface {
    id: string;
    name: string;
    address: string;
    phone: string;
    email: string;
    profileUrl: string;
    pos: Object;
    status: string;
    lifeCycleStatus: string;
    processBy: string;
    type: string;
    createdDate: Date;
    updatedDate: Date;
    t0: Date;
    t1: Date;
    t2: Date;
    t3: Date;
    timeOut: Date;
    customerId: string;
    timezoneclient: string;
    notes: Object[];
    demandCustomer: object;
    property: object;
    source: string;
    code: string;
    processedDate: Date;
    processedHistory: ILeadProcessed[];
    isCalled: boolean;
    advisingType: string;
    assignedDate: Date;

    updatedName: string;
    updatedPhone: string;
    updatedEmail: string;
    updatedProfileUrl: string;

    isInNeed: string;
    reasonNoNeed: string;
    otherReason: string;
    interestedProduct: Object[];
    direction: Object[];
    needLoan: boolean;
    isAppointment: boolean;
    isVisited: boolean;
    note: string;
    callHistory: Object[];

    // lead repo
    exploitStatus: ExploitEnum;
    exploitStatusModifiedBy: string;
    exploitHistory: IExploitHistory[];
    latestAssignHistory: IExploitHistory;
    repoId: string;
    repoConfigCode: string;
    assignDuration: number;
    visiblePhone: boolean;
    takeCare: ITakeCare;
    isHot: boolean;
    project: IProject;
    expireTime: Date;
    countAssign: number;
    notiUser?: any;
}

export interface ILeadProcessed {
    id: string;
    processedDate: Date;
    processBy: string;
    isReject: boolean;
    isTimeOut: boolean;
    causeReject: string[];
}

export interface ILeadResponse {
    id?: string;
    name: string;
    email: string;
    address: string;
    phone: string;
    pos: Object;
    customer: object;
    property: object;
    source: string;
    code: string;
    processedDate: Date;
    employeeTakeCare: object;
}

export interface ILeadByIdRequest {
    propertyAttributeId: string;
}
