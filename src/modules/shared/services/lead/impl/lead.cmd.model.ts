import { ExploitEnum } from '../../../enum/exploit.enum';
import { Payload } from '../../../eventStream/models/payload.model';
import { IExploitHistory, ILead, IProject, ITakeCare } from '../interfaces/lead.interface';

export class CommandModel extends Payload implements ILead {
    customerId: string;
    email: string;
    profileUrl: string;
    name: string;
    address: string;
    phone: string;
    pos: Object;
    description: string;
    status: string;
    lifeCycleStatus: string;
    processBy: string;
    type: string;
    t0: Date;
    t1: Date;
    t2: Date;
    t3: Date;
    timeOut: Date;
    timezoneclient: string;
    notes: Object[];
    demandCustomer: object;
    property: object;
    source: string;
    code: string;
    processedDate: Date;
    processedHistory: any[];
    assignedDate: Date;
    isCalled: boolean;
    advisingType: string;
    updatedName: string;
    updatedPhone: string;
    updatedEmail: string;
    updatedProfileUrl: string;
    isInNeed: string;
    reasonNoNeed: string;
    otherReason: string;
    interestedProduct: Object[];
    direction: Object[];
    needLoan: boolean;
    isAppointment: boolean;
    isVisited: boolean;
    note: string;
    callHistory: Object[];
    importedBy: Object;
    exploitStatus: ExploitEnum;
    exploitStatusModifiedBy: string;
    exploitHistory: IExploitHistory[];
    latestAssignHistory: IExploitHistory;
    visiblePhone: boolean;
    repoId: string;
    repoConfigCode: string;
    isHot: boolean;
    assignDuration: number;
    takeCare: ITakeCare;
    project: IProject;
    expireTime: Date;
    countAssign: number;
}
