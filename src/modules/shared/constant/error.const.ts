import { isNullOrUndefined } from "util";

export class ErrorConst {
  static CommonError(type, name?, pushError?) {
    let error = {};
    let key = "";
    key = type.key.replace("{name}", name);
    error[key] = type.text.replace("{name}", name);
    if (!isNullOrUndefined(pushError)) error = Object.assign(pushError, error);
    return error;
  }

  static Error(
    type,
    object?: string,
    property?: string,
    value?: string,
    pushError?
  ) {
    let error = {};
    let key = type.key;
    let text = type.text;
    if (!object) object = "";

    key = key.replace("{object}", object);
    text = text.replace("{object}", object);

    if (!property) property = "";
    // if (property && property.length > 0) {
    key = key.replace("{property}", property);
    text = text.replace("{property}", property);
    // }
    if (!value) value = "";
    // if (value && value.length > 0) {
    key = key.replace("{value}", value);
    text = text.replace("{value}", value);
    // }
    error[key] = text;
    if (!isNullOrUndefined(pushError)) error = Object.assign(pushError, error);
    return error;
  }

  static INVALID_FIELD = {
    key: `invalid.field.{name}.error`,
    text: `{name} nhập sai dữ liệu`,
  };
  static INVALID_INPUT = {
    key: `{name}.invalid.input.error`,
    text: `{name} nhập sai dữ liệu. Xin vui lòng thử lại`,
  };
  static NOT_FOUND = {
    key: `{name}.not.found.error`,
    text: `{name} không tìm thấy`,
  };

  static USER_TAKE_CARE_NOT_FOUND = {
    key: `user.not.found.error`,
    text: `Không tìm thấy nhân viên`,
  };
  static DATA_EXISTED = {
    key: `{name}.existed.error`,
    text: `{name} đã tồn tại. Xin vui lòng kiểm tra lại`,
  };
  static INTERNAL_SERVER = {
    key: `internal.server.error`,
    text: `Lỗi hệ thống`,
  };
  static UNAUTHORIZED = {
    key: `unauthorized.error`,
    text: `Bạn không có quyền truy cập chức năng này.`,
  };

  static EMPLOYEE_PENALTY = {
    key: `employee.penalty.error`,
    text: `Bạn đã từ chối xử lý yêu cầu của khách hàng trong ngày hôm nay. Vui lòng thực hiện lại vào ngày mai`,
  };
  static LEAD_ASSIGNED = {
    key: `lead.is.assigned.error`,
    text: `Lead is assigned`,
  };
  static EMPLOYEE_NOT_ASSIGN_POS = {
    key: `emmployee.not.assign.error`,
    text: `Bạn không được gán vào POS nào, không thể lấy được yêu cầu`,
  };
  static EMPLOYEE_NOT_PROCESSED_YET = {
    key: `emmployee.not.processed.yet.error`,
    text: `Bạn chưa xử lý hết yêu cầu được giao, không thể lấy được yêu cầu`,
  };
  static HAVE_NOT_ANY_LEAD = {
    key: `have.not.any.lead.error`,
    text: `POS không có yêu cầu nào từ khách hàng, không thể lấy được yêu cầu`,
  };
  static INVALID_MARK_PROCESSING = {
    key: `invalid.mark.processing.error`,
    text: `Bạn đang xử lý yêu cầu nên không thể lấy thêm yêu cầu mới. Xin vui lòng thử lại sau`,
  };

  static STATUS_NOT_INVALID = {
    key: `satus.not.invalid.error`,
    text: `Trạng thái không hợp lệ`,
  };

  static USER_NOT_INVALID = {
    key: `user.not.invalid.error`,
    text: `user không hợp lệ`,
  };

  static PROJECT_ID_NOT_FOUND = {
    key: `project.not.found.error`,
    text: `Không tìm thấy dự án`,
  };

  static USER_ID_NOT_FOUND = {
    key: `user.not.found.error`,
    text: `Không tìm thấy user trong dự án này`,
  };

  static LEAD_CARE_CAN_NOT_SURVEY = {
    key: `lead.care.can.not.survey`,
    text: `Không cho phép khảo sát yêu cầu này`,
  };

  static TRANSFER_NOT_ALLOWED = {
    key: `tranfer.not.allow`,
    text: `Căn hộ đang trong quá trình phát hành hợp đồng mua bán, quý khách vui lòng không gửi yêu cầu chuyển nhượng trong thời gian này.`,
  };

  static CANT_TRANSFER = {
    key: `cant.transfer`,
    text: `Bạn không thể gửi yêu cầu chuyển nhượng do dự án {name} đang trong giai đoạn làm giấy chứng nhận, xin lỗi vì sự bất tiện này.`,
  };

  static IS_NOT_MONTHLY = {
    key: `is.not.monthly`,
    text: `Công việc không phải là hằng tháng.`,
  };

  static IMPLEMENT_MAX_DATE_IS_60 = {
    key: `implement.max.date.is.60`,
    text: `Ngày kết thúc cách Ngày thực hiện tối đa 5 năm.`,
  };

  static LEAD_SOURCE_IN_USE = {
    key: `lead.source.is.use`,
    text: `Nguồn lead đang được sử dụng trong một lead.`,
  };
}
