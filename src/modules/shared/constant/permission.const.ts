export class PermissionConst {
  // Define prermission const
  static CREATE_LEAD = "lead.create";
  static UPDATE_LEAD = "lead.update";
  static DELETE_LEAD = "lead.delete";
  static PULL_LEAD = "lead.pull";
  static REJECT_LEAD = "lead.reject";
  static FAIL_LEAD = "lead.fail";
  static PROCESS_LEAD = "lead.process";
  static UNPROCESS_LEAD = "lead.unprocess";
  static PENDING_LEAD = "lead.pending";
  static ASSIGN_LEAD = "lead.assign";

  // 
  static HANDLE_TRANSFER_REQUEST = "handle.transfer.request";

  // Define prermission const
  static CREATE_LEAD_CARE = "lead.care.create";
  static UPDATE_LEAD_CARE = "lead.care.update";
  static DELETE_LEAD_CARE = "lead.care.delete";
  static PULL_LEAD_CARE = "lead.care.pull";
  static REJECT_LEAD_CARE = "lead.care.reject";
  static FAIL_LEAD_CARE = "lead.care.fail";
  static PROCESS_LEAD_CARE = "lead.care.process";
  static UNPROCESS_LEAD_CARE = "lead.care.unprocess";
  static PENDING_LEAD_CARE = "lead.care.pending";
  static ASSIGN_LEAD_CARE = "lead.care.assign";

  static GET_ALL_LEAD_HISTORY = "lead.get.all.lead.history";
  static GET_EVENTSTREAM = "lead.get.lead.eventstream";
  static GET_ALL_LEAD = "lead.get.all.lead";
  static GET_ALL_LEAD_BY_POS = "lead.get.all.by.pos";
  static GET_LEAD_ID = "lead.get.id";
  static GET_LEAD_PROCESS_BY_EMPLOYEE = "lead.get.process.by.employee";
  static IMPORT_LEAD = "lead.import";
  static GET_ALL_LEAD_ADVISING = "lead.get.all.lead.advising";
  static REPORT_LEAD = "lead.report";
  static REPORT_EXPLOIT_LEAD = "lead.report.exploit";
  static LEAD_PRIMARY_VIEW_MENU = "lead.primary.view.menu";
  static LEAD_VIEW_MENU = "lead.view.menu";
  static LEAD_COMMON_VIEW_MENU = "lead.common.view.menu";
  static LEAD_REPORT_BY_IMPORTER = "lead.report.by.importer";
  static LEAD_REPORT_BY_EMP = "lead.report.by.emp";
  static LEAD_REPORT_VIEW_ALL = "lead.report.view.all";
  static LEAD_REPORT_VIEW_MENU = "lead.report.view.menu";
  static LEAD_MANUAL_DELIVER = "lead.manual.deliver";
  static MANAGE_REPOSITORY = "lead.repository.manage";
  static LEAD_CONFIG_CREATE = "lead.config.create";
  static LEAD_CONFIG_UPDATE = "lead.config.update";
  static LEAD_CONFIG_GET_ID = "lead.config.get.id";
  static LEAD_CONFIG_GET_ALL = "lead.config.get.all";
  static LEAD_CONFIG_DELETE = "lead.config.delete";
  static LEAD_CONFIG_CHANGE_STATUS = "lead.config.changeStatus";
  static LEAD_REVOKE_DELIVER = "lead.revoke.deliver";

  static GET_ALL_LEAD_HISTORY_CARE = "lead.care.get.all.lead.history";
  static GET_EVENTSTREAM_CARE = "lead.care.get.lead.eventstream";
  static GET_ALL_LEAD_CARE = "lead.care.get.all.lead";
  static GET_ALL_LEAD_BY_POS_CARE = "lead.care.get.all.by.pos";
  static GET_ID_LEAD_CARE = "lead.care.get.id";
  static GET_LEAD_PROCESS_BY_EMPLOYEE_CARE =
    "lead.care.get.process.by.employee";
  static IMPORT_LEAD_CARE = "lead.care.import";
  static GET_ALL_LEAD_ADVISING_CARE = "lead.care.get.all.lead.advising";
  static REPORT_LEAD_CARE = "lead.care.report";
  static REPORT_EXPLOIT_LEAD_CARE = "lead.care.report.exploit";
  static LEAD_PRIMARY_VIEW_MENU_CARE = "lead.care.primary.view.menu";
  static LEAD_VIEW_MENU_CARE = "lead.care.view.menu";
  static LEAD_COMMON_VIEW_MENU_CARE = "lead.care.common.view.menu";
  static LEAD_REPORT_BY_IMPORTER_CARE = "lead.care.report.by.importer";
  static LEAD_REPORT_BY_EMP_CARE = "lead.care.report.by.emp";
  static LEAD_REPORT_BY_PROJECT_CARE = "lead.care.report.by.project";
  static LEAD_REPORT_VIEW_ALL_CARE = "lead.care.report.view.all";
  static LEAD_REPORT_QUERY_ALL_CARE = "lead.care.report.query.all";
  static LEAD_REPORT_VIEW_MENU_CARE = "lead.care.report.view.menu";

  static LEAD_CARE_GET_ALL = "lead.care.get.all";
  static LEAD_CARE_GET_BY_ADMIN = "lead.care.get.by.admin";
  static LEAD_CARE_BQL_GET_ALL = "lead.care.bql.get.all";

  static MANAGE_REPOSITORY_CARE = "lead.care.repository.manage";

  static ADMIN_GET_ALL_TRANSER_HISTORY = "admin.get.all.transfer.history";
  // leadjob
  static LEADJOB_CREATE = "leadjob.create";
  static LEADJOB_UPDATE = "leadjob.update";
  static LEADJOB_PROCESS = "leadjob.process";
  static LEADJOB_DELETE = "leadjob.delete";
  static LEADJOB_GET_ALL = "leadjob.get.all";
  static LEADJOB_GET_ID = "leadjob.get.id";
  static LEADJOB_GET_BY_ADMIN = "leadjob.get.by.admin";
  static LEADJOB_GET = "leadjob.get";
  static LEADJOB_VIEW_MENU = "leadjob.view.menu";
  static LEADJOB_VIEW_PLAN_MENU = "leadjob.view.plan.menu";
  static LEADJOB_GET_BY_POS = "leadjob.get.by.pos";

  // lead source
  static LEAD_SOURCE_CREATE = "lead.source.create";
  static LEAD_SOURCE_UPDATE = "lead.source.update";
  static LEAD_SOURCE_DELETE = "lead.source.delete";
  static LEAD_SOURCE_GET = "lead.source.get";
  static LEAD_SOURCE_GET_ALL = "lead.source.get.all";

  // lead
  static LEAD_GET_ALL_SUPER_ADMIN = "lead.get.all.super.admin"; // Lấy tất cả dữ liệu lead
  static LEAD_GET_ALL_COMPANY = "lead.get.all.company"; // Lấy tất cả dữ liệu lead theo các đơn vị của công ty
  static LEAD_GET_ALL_UNIT = "lead.get.all.unit"; // Lấy tất cả dữ liệu của nhân viên đơn vị sàn, team

  static SERVICE = {
    LEAD: {
      NAME: "lead",
      ROLE: [
        PermissionConst.CREATE_LEAD,
        PermissionConst.UPDATE_LEAD,
        PermissionConst.DELETE_LEAD,
        PermissionConst.PULL_LEAD,
        PermissionConst.REJECT_LEAD,
        PermissionConst.FAIL_LEAD,
        PermissionConst.PROCESS_LEAD,
        PermissionConst.UNPROCESS_LEAD,
        PermissionConst.ASSIGN_LEAD,
        PermissionConst.PENDING_LEAD,
        PermissionConst.LEAD_PRIMARY_VIEW_MENU,
        PermissionConst.LEAD_VIEW_MENU,
        PermissionConst.LEAD_COMMON_VIEW_MENU,
        PermissionConst.LEAD_MANUAL_DELIVER,
        PermissionConst.LEAD_REVOKE_DELIVER,

        PermissionConst.GET_EVENTSTREAM,
        PermissionConst.GET_ALL_LEAD_HISTORY,
        PermissionConst.GET_ALL_LEAD,
        PermissionConst.GET_ALL_LEAD_BY_POS,
        PermissionConst.GET_LEAD_ID,
        PermissionConst.GET_LEAD_PROCESS_BY_EMPLOYEE,
        PermissionConst.IMPORT_LEAD,
        PermissionConst.REPORT_LEAD,
        PermissionConst.LEAD_PRIMARY_VIEW_MENU,
        PermissionConst.LEAD_REPORT_BY_IMPORTER,
        PermissionConst.LEAD_REPORT_BY_EMP,
        PermissionConst.LEAD_REPORT_VIEW_ALL,
        PermissionConst.LEAD_REPORT_VIEW_MENU,
        PermissionConst.GET_ALL_LEAD_ADVISING,
        PermissionConst.MANAGE_REPOSITORY,
        PermissionConst.LEAD_CONFIG_CREATE,
        PermissionConst.LEAD_CONFIG_UPDATE,
        PermissionConst.LEAD_CONFIG_GET_ALL,
        PermissionConst.LEAD_CONFIG_GET_ID,
        PermissionConst.LEAD_CONFIG_DELETE,
        PermissionConst.LEAD_CONFIG_CHANGE_STATUS,
        PermissionConst.LEAD_GET_ALL_SUPER_ADMIN,
        PermissionConst.LEAD_GET_ALL_COMPANY,
        PermissionConst.LEAD_GET_ALL_UNIT,
      ],
    },
    LEAD_CARE: {
      NAME: "leadCare",
      ROLE: [
        PermissionConst.CREATE_LEAD_CARE,
        PermissionConst.UPDATE_LEAD_CARE,
        PermissionConst.DELETE_LEAD_CARE,
        PermissionConst.PULL_LEAD_CARE,
        PermissionConst.REJECT_LEAD_CARE,
        PermissionConst.FAIL_LEAD_CARE,
        PermissionConst.PROCESS_LEAD_CARE,
        PermissionConst.UNPROCESS_LEAD_CARE,
        PermissionConst.ASSIGN_LEAD_CARE,
        PermissionConst.PENDING_LEAD_CARE,
        PermissionConst.LEAD_PRIMARY_VIEW_MENU_CARE,
        PermissionConst.LEAD_VIEW_MENU_CARE,
        PermissionConst.LEAD_COMMON_VIEW_MENU_CARE,

        PermissionConst.HANDLE_TRANSFER_REQUEST,

        PermissionConst.GET_EVENTSTREAM_CARE,
        PermissionConst.GET_ALL_LEAD_HISTORY_CARE,
        PermissionConst.GET_ALL_LEAD_CARE,
        PermissionConst.GET_ALL_LEAD_BY_POS_CARE,
        PermissionConst.GET_ID_LEAD_CARE,
        PermissionConst.GET_LEAD_PROCESS_BY_EMPLOYEE_CARE,
        PermissionConst.IMPORT_LEAD_CARE,
        PermissionConst.REPORT_LEAD_CARE,
        PermissionConst.LEAD_PRIMARY_VIEW_MENU_CARE,
        PermissionConst.LEAD_REPORT_BY_IMPORTER_CARE,
        PermissionConst.LEAD_REPORT_BY_EMP_CARE,
        PermissionConst.LEAD_REPORT_BY_PROJECT_CARE,
        PermissionConst.LEAD_REPORT_VIEW_ALL_CARE,
        PermissionConst.LEAD_REPORT_QUERY_ALL_CARE,
        PermissionConst.LEAD_REPORT_VIEW_MENU_CARE,
        PermissionConst.GET_ALL_LEAD_ADVISING_CARE,
        PermissionConst.MANAGE_REPOSITORY_CARE,
        PermissionConst.LEAD_CARE_GET_ALL,
        PermissionConst.LEAD_CARE_GET_BY_ADMIN,
        PermissionConst.LEAD_CARE_BQL_GET_ALL,
        PermissionConst.ADMIN_GET_ALL_TRANSER_HISTORY,
      ],
    },
    LEAD_JOB: {
      NAME: "leadJob",
      ROLE: [
        PermissionConst.LEADJOB_CREATE,
        PermissionConst.LEADJOB_UPDATE,
        PermissionConst.LEADJOB_PROCESS,
        PermissionConst.LEADJOB_DELETE,
        PermissionConst.LEADJOB_GET_ALL,
        PermissionConst.LEADJOB_GET_ID,
        PermissionConst.LEADJOB_GET_BY_ADMIN,
        PermissionConst.LEADJOB_GET,
        PermissionConst.LEADJOB_VIEW_MENU,
        PermissionConst.LEADJOB_VIEW_PLAN_MENU,
        PermissionConst.LEADJOB_GET_BY_POS,
      ],
    },
    LEAD_SOURCE: {
      NAME: "leadSource",
      ROLE: [
        PermissionConst.LEAD_SOURCE_CREATE,
        PermissionConst.LEAD_SOURCE_UPDATE,
        PermissionConst.LEAD_SOURCE_DELETE,
        PermissionConst.LEAD_SOURCE_GET,
        PermissionConst.LEAD_SOURCE_GET_ALL,
      ],
    },
  };

  // Use to get all permission packages
  static PERMISSION_PACKAGES(): Object[] {
    let packs = [];
    Object.keys(this.SERVICE).map((key) =>
      packs.push({
        permissions: this.SERVICE[key].ROLE,
        msxName: this.SERVICE[key].NAME,
      })
    );
    console.log("PERMISSION_PACKAGES => ", packs);
    return packs;
  }

  // Use to get all role
  static ALL_ROLES(): Object[] {
    let permissions = [];
    Object.keys(this.SERVICE).map((key) =>
      permissions.push(...this.SERVICE[key].ROLE)
    );
    return permissions;
  }
}
