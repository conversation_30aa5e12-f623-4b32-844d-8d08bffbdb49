export class CommonConst {
    static DATABASE_SERVICE_READ_MODEL = 'msx-lead-readmodel';
    static DATABASE_SERVICE_EVENTS_MODEL = 'msx-lead-eventstore';
    static HEADER_PARTERN_STR = 'pattern';

    static DOMAIN_MODEL_TOKEN = 'Domain-ModelToken';
    static QUERY_MODEL_TOKEN = 'Query-ModelToken';
    static DOMAIN_CONNECTION_TOKEN = 'Lead-Domain-DbConnectionToken';
    static QUERY_CONNECTION_TOKEN = 'Lead-Query-DbConnectionToken';

    static LEAD_EVENTS = 'events-lead';
    static LEAD_COLLECTION = 'lead';
    static LEAD_REPO_EVENTS = 'events-lead-repo';
    static LEAD_REPO_COLLECTION = 'lead-repo';
    static LEAD_CARE_EVENTS = 'events-lead-care';
    static LEAD_CARE_COLLECTION = 'lead-care';
    static LEAD_REPO_CARE_EVENTS = 'events-lead-repo-care';
    static LEAD_REPO_CARE_COLLECTION = 'lead-repo-care';
    static C_LEAD_COLLECTION = 'c-lead';
    static C_LEAD_CARE_COLLECTION = 'c-lead-care';
    static EMPLOYEE_COLLECTION = 'employee';
    static RAW_COLLECTION = 'raw';
    static LEAD_HISTORY_COLLECTION = 'lead-history';
    static LEAD_SOURCE_COLLECTION = 'source';
    static LEAD_CARE_HISTORY_COLLECTION = 'lead-care-history';
    static HISTORY_IMPORT_COLLECTION = 'history-import';
    static CODE_COLLECTION = 'code-generate';
    // static LEAD_PREFIX = 'YC-';
    static LEAD_CARE_PREFIX = 'SR-';
    static LEAD_JOB_PREFIX = 'CV-';
    static LEAD_REPO_PREFIX = '101';
    static LEAD_REPO_CONFIG_PREFIX = '102';
    static LEAD_PREFIX = '103'
    static LEAD_ADVISING_PREFIX = 'TVDV-';
    static REGEX_EMAIL = /^[0-9-A-z][A-z0-9_\.-]{1,32}@[A-z0-9-_]{2,}(\.[A-z0-9]{2,}){1,2}$/;
    static REGEX_VN_PHONE = /[0-9\+]{9,15}/gm;

    static LEADJOB_QUERY_MODEL_TOKEN = "Leadjob-Query-ModelToken";
    static LEADJOB_AGGREGATE_NAME = "leadJob";
    static LEADJOB_COLLECTION = "leadJobs";


  static TYPE = {
        SELL: 'SELL',
        BUY: 'BUY',
        ADVISING: 'ADVISING',
        RENT: 'RENT',
        PRIMARY: 'PRIMARY'
    };

    // aggregate
    static AGGREGATE_NAME_LEAD = 'lead';
    static AGGREGATE_NAME_LEAD_CARE = 'leadCare';
    static AGGREGATE_NAME_LEAD_REPO = 'leadRepo';
    static AGGREGATE_NAME_LEAD_REPO_CARE = 'leadRepoCare';

    static AGGREGATE_NAMES(): Object[] {
        return Object.keys(this.AGGREGATES)
            .map(key => this.AGGREGATES[key].NAME);
    }

    static AGGREGATES = {
        LEAD : {
                NAME: CommonConst.AGGREGATE_NAME_LEAD,
                CREATED: CommonConst.AGGREGATE_NAME_LEAD + 'Created',
                UPDATED: CommonConst.AGGREGATE_NAME_LEAD + 'Updated',
                CHANGE_STATUS: CommonConst.AGGREGATE_NAME_LEAD + 'ChangedStatus',
                COMPLETED: CommonConst.AGGREGATE_NAME_LEAD + 'Completed',
                FAILED: CommonConst.AGGREGATE_NAME_LEAD + 'Failed',
                PULLED: CommonConst.AGGREGATE_NAME_LEAD + 'Pulled',
                REJECTED: CommonConst.AGGREGATE_NAME_LEAD + 'Rejected',
                PROCESSING: CommonConst.AGGREGATE_NAME_LEAD + 'Processing',
                UNPROCESSING: CommonConst.AGGREGATE_NAME_LEAD + 'Unprocessing',
                ASSIGNED: CommonConst.AGGREGATE_NAME_LEAD + 'Assigned',
                ASSIGN: CommonConst.AGGREGATE_NAME_LEAD + 'Assign',
                PENDING: CommonConst.AGGREGATE_NAME_LEAD + 'Pending',
                REASSIGNED: CommonConst.AGGREGATE_NAME_LEAD + 'Reassigned',
                EXPIRED: CommonConst.AGGREGATE_NAME_LEAD + 'Expired',
                GET_ORG_CHILDS: CommonConst.AGGREGATE_NAME_LEAD + 'GetOrgChilds',
                GET_CONSIGNMENT: CommonConst.AGGREGATE_NAME_LEAD + 'GetConsignment',
                GET_DEMAND: CommonConst.AGGREGATE_NAME_LEAD + 'GetDemand',
                IMPORT_LEAD: CommonConst.AGGREGATE_NAME_LEAD + 'ImportLead',
                RENEW_LEAD: CommonConst.AGGREGATE_NAME_LEAD + 'RenewLead',
                // EVENTS: 'events-' + CommonConst.AGGREGATE_NAME_LEAD,
                // COLLECTION: CommonConst.AGGREGATE_NAME_LEAD
        },
        LEAD_CARE : {
                NAME: CommonConst.AGGREGATE_NAME_LEAD_CARE,
                CREATED: CommonConst.AGGREGATE_NAME_LEAD_CARE + 'Created',
                UPDATED: CommonConst.AGGREGATE_NAME_LEAD_CARE + 'Updated',
                CHANGE_STATUS: CommonConst.AGGREGATE_NAME_LEAD_CARE + 'ChangedStatus',
                COMPLETED: CommonConst.AGGREGATE_NAME_LEAD_CARE + 'Completed',
                FAILED: CommonConst.AGGREGATE_NAME_LEAD_CARE + 'Failed',
                PULLED: CommonConst.AGGREGATE_NAME_LEAD_CARE + 'Pulled',
                REJECTED: CommonConst.AGGREGATE_NAME_LEAD_CARE + 'Rejected',
                PROCESSING: CommonConst.AGGREGATE_NAME_LEAD_CARE + 'Processing',
                UNPROCESSING: CommonConst.AGGREGATE_NAME_LEAD_CARE + 'Unprocessing',
                ASSIGNED: CommonConst.AGGREGATE_NAME_LEAD_CARE + 'Assigned',
                PENDING: CommonConst.AGGREGATE_NAME_LEAD_CARE + 'Pending',
                REASSIGNED: CommonConst.AGGREGATE_NAME_LEAD_CARE + 'Reassigned',
                EXPIRED: CommonConst.AGGREGATE_NAME_LEAD_CARE + 'Expired',
                GET_ORG_CHILDS: CommonConst.AGGREGATE_NAME_LEAD_CARE + 'GetOrgChilds',
                GET_CONSIGNMENT: CommonConst.AGGREGATE_NAME_LEAD_CARE + 'GetConsignment',
                GET_DEMAND: CommonConst.AGGREGATE_NAME_LEAD_CARE + 'GetDemand',
                IMPORT_LEAD: CommonConst.AGGREGATE_NAME_LEAD_CARE + 'ImportLead',
                CUSTOMER_CLOSED: CommonConst.AGGREGATE_NAME_LEAD_CARE + 'CustomerClosedTicket'
                // EVENTS: 'events-' + CommonConst.AGGREGATE_NAME_LEAD,
                // COLLECTION: CommonConst.AGGREGATE_NAME_LEAD
        },
        LEAD_REPO: {
            CREATED: CommonConst.AGGREGATE_NAME_LEAD_REPO + 'Created',
            UPDATED: CommonConst.AGGREGATE_NAME_LEAD_REPO + 'Updated',
            REMOVE: CommonConst.AGGREGATE_NAME_LEAD_REPO + 'Removed',
            UPDATE_CONFIG:
                CommonConst.AGGREGATE_NAME_LEAD_REPO + 'UpdateConfig',
            ACTIVE_CONFIG:
                CommonConst.AGGREGATE_NAME_LEAD_REPO + 'ActiveConfig',
            BULK_UPDATE: CommonConst.AGGREGATE_NAME_LEAD_REPO + 'BulkUpdate'
        },
        LEAD_REPO_CARE: {
            CREATED: CommonConst.AGGREGATE_NAME_LEAD_REPO_CARE + 'Created',
            UPDATED: CommonConst.AGGREGATE_NAME_LEAD_REPO_CARE + 'Updated',
            REMOVE: CommonConst.AGGREGATE_NAME_LEAD_REPO_CARE + 'Removed',
            UPDATE_CONFIG:
                CommonConst.AGGREGATE_NAME_LEAD_REPO_CARE + 'UpdateConfig',
            ACTIVE_CONFIG:
                CommonConst.AGGREGATE_NAME_LEAD_REPO_CARE + 'ActiveConfig',
        },
        LEADJOB: {
          NAME: CommonConst.LEADJOB_AGGREGATE_NAME,
          CREATED: CommonConst.LEADJOB_AGGREGATE_NAME + "Created",
          UPDATED: CommonConst.LEADJOB_AGGREGATE_NAME + "Updated",
          DELETED: CommonConst.LEADJOB_AGGREGATE_NAME + "Deleted",
          EVENTS: "events-" + CommonConst.LEADJOB_AGGREGATE_NAME,
          COLLECTION: CommonConst.LEADJOB_AGGREGATE_NAME,
        },
    };

    static ORGCHART = 'orgchart';
    static AGGREGATES_LISTENER = {
        EMPLOYEE : {
            NAME: 'employee',
            CREATED: 'employeeCreated',
            UPDATED: 'employeeUpdated',
            LIST_UPDATED: 'employeeListUpdated',
            DELETED: 'employeeDeleted',
        },
        DEMAND : {
            NAME: 'demand',
            CREATED: 'demandCreated',
            UPDATED: 'demandUpdated',
            DELETED: 'demandDeleted',
            PENDED: 'demandPended',
        },
        CONSIGNMENT : {
            NAME: 'consigment',
            CREATED: 'consignmentCreated',
            UPDATED: 'consignmentUpdated',
            DELETED: 'consignmentDeleted',
        },
        ORGCHART: {
            NAME: CommonConst.ORGCHART,
            LIST_UPDATED: CommonConst.ORGCHART + 'ListUpdated',
            UPDATED: CommonConst.ORGCHART + 'Updated'
        },
        CUSTOMER : {
            NAME: 'customer',
            CREATED: 'customerCreated',
            UPDATED: 'customerUpdated',
            DELETED: 'customerDeleted',
        },
    };
}
