import { ApiModelPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { ArrayMaxSize, IsBoolean, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, MaxLength, Min, Validate, ValidateIf, ValidateNested } from 'class-validator';
import { BaseModel } from '../base/base.model';
import { DeliverTypeEnum } from '../../enum/deliver-type.enum';
import { IsSurveyValuesValid } from '../../classes/class-validation';
import { trim } from "lodash";

export class Project {
  @ApiModelPropertyOptional()
  @IsNotEmpty()
  id: string;

  @ApiModelPropertyOptional()
  @IsNotEmpty()
  name: string;
}

export class ValueDto {
  @ApiModelPropertyOptional()
  @Transform((value) => trim(value))
  @IsString()
  @MaxLength(50)
  name: string;

  @ApiModelPropertyOptional()
  @Transform((value) => trim(value))
  @IsString()
  @MaxLength(10)
  code: string;

  @ApiModelPropertyOptional()
  @IsOptional()
  @IsBoolean()
  value: boolean;
}

export class Survey {
  @ApiModelPropertyOptional()
  @Transform((value) => trim(value))
  @IsString()
  type: string;

  @ApiModelPropertyOptional()
  @Transform((value) => trim(value))
  @IsString()
  @MaxLength(50)
  name: string;

  @ApiModelPropertyOptional({ type: [ValueDto] })
  @Validate(IsSurveyValuesValid)
  @ValidateNested({ each: true })
  @Type(() => ValueDto)
  values: ValueDto[];

  @ApiModelPropertyOptional()
  @IsOptional()
  @IsString()
  @MaxLength(250)
  text: string;

  @ApiModelPropertyOptional()
  @IsOptional()
  @IsString()
  @MaxLength(500)
  multilineText: string;
}

export class OrgChartQueueItem {
  @IsOptional()
  id: string;

  @IsOptional()
  employeeQueue?: Object[];
}

export class OrgChart {
  @ApiModelPropertyOptional()
  @IsOptional()
  id: string;

  @ApiModelPropertyOptional()
  @IsOptional()
  name: string;

  @IsOptional()
  staffIds: string[];

  @IsOptional()
  deassignedStaffIds: string[];
}

export class DateRange {
  @ApiModelPropertyOptional()
  @IsNotEmpty()
  from: string;

  @IsNotEmpty()
  @ApiModelPropertyOptional()
  to: string;
}

export class NotificationInstance {
  @ApiModelPropertyOptional()
  @IsOptional()
  title: string;

  @ApiModelPropertyOptional()
  @IsOptional()
  content: string;

  @ApiModelPropertyOptional()
  @IsOptional()
  active: boolean;
}

export class Notification {
  @ApiModelPropertyOptional({ type: NotificationInstance })
  @IsOptional()
  email?: NotificationInstance;

  @ApiModelPropertyOptional({ type: NotificationInstance })
  @IsOptional()
  web?: NotificationInstance;

  @ApiModelPropertyOptional({ type: NotificationInstance })
  @IsOptional()
  app?: NotificationInstance;

  @ApiModelPropertyOptional({ type: NotificationInstance })
  @IsOptional()
  sms?: NotificationInstance;

  @ApiModelPropertyOptional({ type: NotificationInstance })
  @IsOptional()
  smsCus?: NotificationInstance;
}

export class LeadRepoConfigHot {
  @ApiModelPropertyOptional({ type: Notification })
  @IsOptional()
  notification: Notification;

  @ApiModelPropertyOptional()
  @IsNotEmpty()
  orgChartIds: string[];

  @ApiModelPropertyOptional({ type: [OrgChart] })
  @ValidateNested({ each: true })
  @IsNotEmpty()
  @Type(() => OrgChart)
  @ArrayMaxSize(1) // crm new
  orgCharts: OrgChart[];

  @ApiModelPropertyOptional()
  @IsOptional()
  @Min(1)
  assignDuration: number;

  @ApiModelPropertyOptional()
  @IsOptional()
  visiblePhone?: boolean;

  @ApiModelPropertyOptional()
  @IsNumber()
  @IsEnum(DeliverTypeEnum)
  deliverType: number;

  @ApiModelPropertyOptional()
  @IsOptional()
  @IsNumber()
  deassignLimit: number;

  @IsOptional()
  orgChartQueue?: OrgChartQueueItem[];
  // manualDeliver: boolean;

  @IsBoolean()
  isWorkingTime: boolean;

  @ValidateIf((body) => body.isWorkingTime)
  workingTime: any[];
}

export class UpdateLeadRepoConfigHot extends LeadRepoConfigHot {
  @ApiModelPropertyOptional()
  @IsOptional()
  @IsNumber()
  @IsEnum(DeliverTypeEnum)
  deliverType: number;

  @IsOptional()
  @IsBoolean()
  visiblePhone?: boolean;
}

export class LeadRepoConfig extends LeadRepoConfigHot {
  @ApiModelPropertyOptional()
  @IsNotEmpty()
  projectId: string;

  @ApiModelPropertyOptional({ type: Project })
  @ValidateNested({ each: true })
  @IsNotEmpty()
  @Type(() => Project)
  project: Project;

  @ApiModelPropertyOptional({ type: DateRange })
  @ValidateNested({ each: true })
  @Type(() => DateRange)
  exploitTime: DateRange;

  @IsOptional()
  code: string;

  @ApiModelPropertyOptional()
  @Transform((value) => trim(value))
  @IsNotEmpty()
  name: string;

  @ApiModelPropertyOptional()
  @IsOptional()
  active: number;

  @ApiModelPropertyOptional({ type: [Survey] })
  @IsOptional()
  @ValidateNested({ each: true })
  @IsNotEmpty()
  @Type(() => Survey)
  surveys: Survey[];

  @IsOptional()
  softDelete?: boolean;

  @IsOptional()
  softDeleteReason?: string;
}

export class LeadRepo extends BaseModel {
  @IsOptional()
  code: string;

  @IsOptional()
  name: string;

  @IsOptional()
  configHot: LeadRepoConfigHot;

  @IsOptional()
  configs: LeadRepoConfig[];

  @IsOptional()
  createdBy?: string;

  @IsOptional()
  createdDate?: Date;

  @IsOptional()
  modifiedBy?: string;

  @IsOptional()
  updatedDate?: Date;
}
