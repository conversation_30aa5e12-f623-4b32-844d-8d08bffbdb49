import { Model } from 'mongoose';
import { Inject, Injectable } from '@nestjs/common';
import { ILeadHistoryDocument } from '../interfaces/document.interface';
import _ = require('lodash');
import { CommonConst } from '../../../modules/shared/constant/common.const';
import { LifeCycleStatusEnum } from '../../../modules/shared/enum/life-cycle-status.enum';
import { TransactionTypeEnum } from '../../../modules/shared/enum/transaction-type.enum';
const moment = require('moment');

@Injectable()
export class LeadHistoryQueryRepository {

  constructor(
    @Inject(CommonConst.QUERY_MODEL_TOKEN)
    private readonly readModel: Model<ILeadHistoryDocument>
  ) { }

  async create(readmodel): Promise<ILeadHistoryDocument> {
    return await this.readModel.create(readmodel)
      .then((response) => {
        return response;
      }).catch((error) => {
        return error;
      });
  }

  async deleteMany(): Promise<ILeadHistoryDocument> {
    return await this.readModel.deleteMany({})
      .then((response) => {
        return response;
      }).catch((error) => {
        return error;
      });
  }

  async getHistoryByCustomer(customerId) {
    return await this.readModel.find({ customerId })
      .exec()
      .then(result => {
        return result;
      });
  }

  async countStatusToDayByEmployee(employeeId: String, lifeCycleStatus: string) {
    const currentDate = moment().startOf('day');
    return await this.readModel.find(
      {
        processBy: employeeId,
        lifeCycleStatus,
        decisionDate: { $gte: currentDate }
      })
      .countDocuments()
      .then(rs => {
        return rs;
      })
      .catch(ex => {
        return ex;
      });
  }

  async findAll(page: number = 1, pageSize = 10, query: any = {}, sortString: string = '') {
    let sort: any = {
      decisionDate: -1
    };
    if (!_.isEmpty(sortString)) {
      sort = this.transformSort(sortString);
    }
    return await this.readModel.find(query)
      .sort(sort)
      .skip(Math.floor(((pageSize * page) - pageSize)))
      .limit(pageSize)
      .exec();
  }

  async findAllStatus() {
    return await this.readModel.aggregate([
      { $group: { _id: '$lifeCycleStatus' } }
    ])
      .then(rs => {
        return rs;
      });
  }

  async countStatusForLeads(leadIds, lifeCycleStatus) {
    return await this.readModel.find(
      {
        id: { $in: leadIds },
        lifeCycleStatus
      })
      .countDocuments()
      .then(rs => {
        return rs;
      })
      .catch(ex => {
        return ex;
      });
  }
  async countCancelTicketsDemandByUser(query) {
    return await this.readModel.find(
      {
        $and:
          [
            query,
            { 'lifeCycleStatus': 'removed' },
            {
              $or: [
                { type: 'BUY' },
                { type: 'RENT' }
              ]
            }
          ]
      }
    ).countDocuments()
      .exec()
      .then(demand => {
        return demand;
      });
  }
  async countCancelTicketsConsignmentByUser(query) {
    return await this.readModel.find(
      {
        $and:
          [
            query,
            { 'lifeCycleStatus': 'removed' },
            {
              $or: [
                { type: 'SELL' },
                { type: 'LEASE' }
              ]
            }
          ]
      }
    ).countDocuments()
      .exec()
      .then(consignment => {
        return consignment;
      });
  }
  async count(query: any = {}): Promise<number> {
    return await this.readModel.countDocuments(query).exec();
  }

  /**
  * 
  * @param {Array<String>} customerId 
  */
  findLeadByCustomerId(customerId: string[] = [], mapping: boolean = false) {
    return this.readModel.find({ customerId: { $in: customerId, $exists: true, $nin: [null, ''] } }).exec()
      .then((res) => {
        if (mapping) {
          return _.groupBy(res, 'customerId');
        }
        return res;
      });
  }
  protected transformSort(paramSort?: String) {
    let sort: any = paramSort;
    if (_.isString(sort)) {
      sort = sort.split(',');
    }
    if (Array.isArray(sort)) {
      let sortObj = {};
      sort.forEach(s => {
        if (s.startsWith('-'))
          sortObj[s.slice(1)] = -1;
        else
          sortObj[s] = 1;
      });
      return sortObj;
    }

    return sort;
  }
  async filter(query: any = {}, isPaging: boolean = false) {
    const limit = parseInt(query['pageSize']) || 10;
    const skip = (parseInt(query['page']) || 1) * limit - limit;
    const { page, pageSize, ...q } = query;
    let sort: any = {
      createdDate: -1
    };
    if (!_.isEmpty(query.sort)) {
      sort = this.transformSort(query.sort);
    }
    console.log(q);
    const aggregate = [
      {
        $match: q
      },
      {
        $lookup: {
          from: 'customers',
          localField: 'customerId',    // field in the orders collection
          foreignField: 'id',  // field in the items collection
          as: 'customers'
        }
      },
      {
        $lookup: {
          from: 'employees',
          localField: 'processBy',    // field in the orders collection
          foreignField: 'id',  // field in the items collection
          as: 'employees'
        }
      },
      {
        $addFields: {
          customer: {
            $ifNull: ['$customer', { $arrayElemAt: ['$customers', 0] }]
          },
          employeeTakeCare: { $arrayElemAt: ['$employees', 0] }
        }
      },
      {
        $project: {
          customers: 0,
          employees: 0
        }
      }
    ];
    if (isPaging) {
      return await this.readModel.aggregate(aggregate)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .exec();
    } else {
      return await this.readModel.aggregate(aggregate)
        .sort(sort)
        .exec();
    }
  }
  async filterReport(user: any = {}, query: any = {}, posId: string[] = [], isPaging: boolean = false) {
    const limit = parseInt(query['pageSize']) || 10;
    const skip = (parseInt(query['page']) || 1) * limit - limit;
    const { page, pageSize, sort = '', ...q } = query;
    let sortObject: any = {
      createdDate: -1
    };
    if (!_.isEmpty(query.sort)) {
      sortObject = this.transformSort(query.sort);
    }
    const aggregate = [
      {
        $match: {
          type: { $in: [TransactionTypeEnum.buy, TransactionTypeEnum.lease, TransactionTypeEnum.rent, TransactionTypeEnum.sell] },
          lifeCycleStatus: { $in: [LifeCycleStatusEnum.COMPLETED] }
        }
      },
      {
        $lookup: {
          from: 'customers',
          let: { customerId: '$customerId' },
          pipeline: [
            {
              $match: {
                $expr:
                {
                  $and:
                    [
                      { $eq: ['$$customerId', '$id'] },
                    ]
                }


              }
            },
            {
              $addFields: {
                'personalInfo.phone': {
                  $cond: { if: { $ne: ['$modifiedBy', user.id] }, then: '******', else: '$personalInfo.phone' },
                },
                'phone': {
                  $cond: { if: { $ne: ['$modifiedBy', user.id] }, then: '******', else: '$phone' },
                }
              }
            }
          ],
          as: 'customers'
        }
      },
      {
        $lookup: {
          from: 'employees',
          let: { employeeId: '$processBy' },
          pipeline: [
            {
              $match:
              {
                $expr:
                {
                  $and:
                    [
                      { $in: ['$$employeeId', '$staffIds'] },
                      { $eq: [user.id, '$id'] },
                    ]
                }


              }
            },
          ],
          as: 'employeeTakeCare'
        }
      },
      { $unwind: '$employeeTakeCare' },
      {
        $lookup: {
          from: 'employees',
          let: { employeeId: '$processBy' },
          pipeline: [
            {
              $match: {
                $expr:
                {
                  $and:
                    [
                      { $eq: ['$$employeeId', '$id'] },
                    ]
                }


              }
            },
          ],
          as: 'employeeTakeCare'
        }
      },
      { $unwind: '$employeeTakeCare' },
      {
        $addFields: {
          customer: {
            $ifNull: ['$customer', { $arrayElemAt: ['$customers', 0] }]
          },
          // employeeTakeCare: { $arrayElemAt: ['$employees', 0] }
        }
      },
      {
        $project: {
          customers: 0,
          employees: 0,
          phone: 0
        }
      },
      { $match: q }
    ];
    if (isPaging) {
      return await this.readModel.aggregate(aggregate)
        .sort(sortObject)
        .skip(skip)
        .limit(limit)
        .allowDiskUse(true)
        .exec();
    } else {
      return await this.readModel.aggregate(aggregate)
        .sort(sortObject)
        .allowDiskUse(true)
        .exec();
    }
  }
  findLeadByEmployeeId(employeeId: string[] = [], query: any = {}) {
    let match: any = {
      processBy: { $in: employeeId, $exists: true, $nin: [null, ''] }, type: { $in: [TransactionTypeEnum.buy, TransactionTypeEnum.lease, TransactionTypeEnum.rent, TransactionTypeEnum.sell] },
      lifeCycleStatus: { $in: [LifeCycleStatusEnum.COMPLETED] }
    };
    if ((query.dateFrom !== undefined && query.dateFrom != null) || (query.dateTo !== undefined && query.dateTo != null)) {
      match.assignedDate = {};
      if ((query.dateFrom !== undefined && query.dateFrom != null)) {
        match.assignedDate.$gte = new Date(parseInt(query.dateFrom));
      }
      if ((query.dateTo !== undefined && query.dateTo != null)) {
        match.assignedDate.$lte = new Date(parseInt(query.dateTo));
      }
    }
    return this.readModel.find(match).sort({ assignedDate: -1 }).exec();
  }
  countReport(user: any = {}, query: any = {}, posInPool: any[] = [],) {
    const { page, pageSize, ...q } = query;
    const aggregate = [
      {
        $match: {
          type: { $in: [TransactionTypeEnum.buy, TransactionTypeEnum.lease, TransactionTypeEnum.rent, TransactionTypeEnum.sell] },
          lifeCycleStatus: LifeCycleStatusEnum.COMPLETED
        }
      },
      {
        $lookup: {
          from: 'customers',
          let: { customerId: '$customerId' },
          pipeline: [
            {
              $match: {
                $expr:
                {
                  $and:
                    [
                      { $eq: ['$$customerId', '$id'] },
                    ]
                }
              }
            }
          ],
          as: 'customers'
        }
      },
      {
        $lookup: {
          from: 'employees',
          let: { employeeId: '$processBy' },
          pipeline: [
            {
              $match:
              {
                $expr:
                {
                  $and:
                    [
                      { $in: ['$$employeeId', '$staffIds'] },
                      { $eq: [user.id, '$id'] },
                    ]
                }


              }
            },
          ],
          as: 'employeeTakeCare'
        }
      },
      { $unwind: '$employeeTakeCare' },
      {
        $lookup: {
          from: 'employees',
          let: { employeeId: '$processBy' },
          pipeline: [
            {
              $match: {
                $expr:
                {
                  $and:
                    [
                      { $eq: ['$$employeeId', '$id'] },
                    ]
                }


              }
            },
          ],
          as: 'employeeTakeCare'
        }
      },
      { $unwind: '$employeeTakeCare' },
      {
        $addFields: {
          customer: {
            $ifNull: ['$customer', { $arrayElemAt: ['$customers', 0] }]
          },
          // employeeTakeCare: { $arrayElemAt: ['$employees', 0] }
        }
      },
      {
        $project: {
          customers: 0,
          employees: 0,
          phone: 0
        }
      },
      { $match: q }
    ];
    return this.readModel.aggregate(aggregate)
      .allowDiskUse(true)
      .exec();
  }
}
