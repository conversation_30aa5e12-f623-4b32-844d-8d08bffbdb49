import { LeadHistoryQueryRepository } from './repository/query.repository';

import { QueryDatabaseModule } from '../database/query/query.database.module';
import { QueryProviders } from './providers/query.cqrs.providers';
import { Module } from '@nestjs/common';
import { AuthModule } from '../auth/auth.module';
import { LeadHistoryController } from './controller';
import { LeadHistoryService } from './service';
import { EmployeeQuerySideModule } from '../employee/module';
import { SharedModule } from '../../../shared-modules';

@Module({
  imports: [QueryDatabaseModule, AuthModule, EmployeeQuerySideModule, SharedModule],
  controllers: [LeadHistoryController],
  providers: [
    LeadHistoryService,
    LeadHistoryQueryRepository,
    ...QueryProviders,
  ],
  exports: [
    LeadHistoryQueryRepository,
    LeadHistoryQuerySideModule,
    LeadHistoryService
  ]
})

export class LeadHistoryQuerySideModule {}
