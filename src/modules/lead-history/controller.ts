import {
    Controller,
    Headers,
    UseGuards,
    UseInterceptors,
    Get, Req, Query,
} from '@nestjs/common';
import { LoggingInterceptor } from '../../common/interceptors/logging.interceptor';
import { LeadHistoryService } from './service';
import { Action } from '../shared/enum/action.enum';
import { ApiUseTags, ApiBearerAuth, ApiForbiddenResponse, ApiCreatedResponse } from '@nestjs/swagger';
import { PermissionConst } from '../shared/constant/permission.const';
import {
    JwtAuthGuard,
    Roles,
    RoleGuard,
    User
} from "../../../shared-modules";

@ApiBearerAuth()
@ApiUseTags('v1/history/lead')
@Controller('v1/history/lead')
@UseInterceptors(LoggingInterceptor)
@UseGuards(JwtAuthGuard)
export class LeadHistoryController {

    private actionName: string = Action.NOTIFY;
    constructor(
        private readonly leadService: LeadHistoryService
    ) { }

    @UseGuards(RoleGuard)
    @Roles(PermissionConst.GET_ALL_LEAD_HISTORY)
    @Get()
    @ApiCreatedResponse({ description: 'Get all lead history.' })
    @ApiForbiddenResponse({ description: 'Forbidden.' })
    async getAll(
        @User() user,
        // @Query('status') status: string,
        // @Query('startDate') startDate: string,
        // @Query('toDate') toDate: string,
        @Req() req,
        @Headers('timezoneclient') timezoneclient: string,
        @Headers('act') actionName?: string) {
        this.actionName = (actionName || this.actionName);
        // let data = [{
        //     status: status,
        //     status: status,
        //     status: status
        // }];
        return await this.leadService.getAllBy(user, timezoneclient, req);
    }

    @Get('statuslist')
    @ApiCreatedResponse({ description: 'Get all status for filter.' })
    @ApiForbiddenResponse({ description: 'Forbidden.' })
    async getList(
        @Headers('act') actionName?: string) {
        this.actionName = (actionName || this.actionName);
        return await this.leadService.getAllStatus();
    }

}
