import * as mongoose from 'mongoose';

export const QuerySchema = new mongoose.Schema({
  _id: { type: String },
  id: { type: String, index: true },
  email: { type: String },
  name: { type: String },
  address: { type: String },
  phone: { type: String },
  pos: { type: Object },
  status: { type: String },
  lifeCycleStatus: { type: String },
  processBy: { type: String },
  type: { type: String },
  createdDate: { type: Date },
  updatedDate: { type: Date },
  description: { type: String },
  demandId: { type: String },
  reason: { type: String },
  notes: { type: Object },
  decisionDate: { type: Date, default: () => Date.now() },
  processedDate: { type: Date },
  assignedDate: { type: Date },
  customerId: { type: String },
  code: { type: String },
  source: { type: String },
  surveyCode: { type: String }
});

QuerySchema.pre('save', function (next) {
  this._id = this.get('id');
  next();
});
