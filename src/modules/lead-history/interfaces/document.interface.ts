import { Document } from 'mongoose';

export interface ILeadHistoryDocument extends Document {
  id: string;
  name: string;
  address: string;
  phone: string;
  pos: Object;
  status: string;
  lifeCycleStatus: string;
  processBy: string;
  type: string;
  createdDate: Date;
  updatedDate: Date;
  decription: string;
  demandId: String;
  reason: String;
  email: string;
  code: string;
  surveyCode: string;
  processedDate: Date;
  assignedDate: Date;
}
