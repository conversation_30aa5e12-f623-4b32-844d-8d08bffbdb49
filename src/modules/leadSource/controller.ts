import {
  Body,
  Controller,
  Get,
  Post,
  Headers,
  Put,
  Delete,
  UseInterceptors,
  UseGuards,
  Query,
} from "@nestjs/common";
import { LeadSourceService } from "./service";
import { PermissionConst } from "../shared/constant/permission.const";
import { IUserResquest } from "../shared/services/user/user-by-id.interface";
import {
  CreateLeadSourceDto,
  DeleteLeadSourceDto,
  LeadSourceDto,
  UpdateLeadSourceDto,
} from "./dto/domain.dto";
import { ApiBearerAuth, ApiUseTags } from "@nestjs/swagger";
import { LoggingInterceptor } from "../../common/interceptors/logging.interceptor";
import { JwtAuthGuard, RoleGuard, Roles, User } from "../../../shared-modules";

@ApiBearerAuth()
@Controller("v1/source")
@ApiUseTags("v1/lead-source")
@UseInterceptors(LoggingInterceptor)
@UseGuards(JwtAuthGuard, RoleGuard)
export class LeadSourceController {
  constructor(private readonly leadSourceService: LeadSourceService) {}

  @Roles(PermissionConst.LEAD_SOURCE_GET_ALL)
  @Get("/list")
  async getAll(@Query() query: any) {
    return await this.leadSourceService.getAll(query);
  }

  @Roles(PermissionConst.LEAD_SOURCE_GET)
  @Get("/get")
  async get(@Query() query: any) {
    return await this.leadSourceService.get(query);
  }

  @Roles(PermissionConst.LEAD_SOURCE_CREATE)
  @Post("/create")
  async createLeadSource(
    @User() user: IUserResquest,
    @Body() dto: CreateLeadSourceDto
  ) {
    return this.leadSourceService.createLeadSource(dto, user?.id);
  }

  @Roles(PermissionConst.LEAD_SOURCE_UPDATE)
  @Put("/update")
  async updateLeadSource(
    @User() user: IUserResquest,
    @Body() dto: UpdateLeadSourceDto
  ) {
    return this.leadSourceService.updateLeadSource(dto, user?.id);
  }

  @Roles(PermissionConst.LEAD_SOURCE_DELETE)
  @Delete("/delete")
  async deleteLeadSource(
    @Body() dto: DeleteLeadSourceDto,
    @User() user: IUserResquest
  ) {
    return this.leadSourceService.deleteLeadSource(dto, user?.id);
  }
}
