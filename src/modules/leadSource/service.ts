import { Injectable } from "@nestjs/common";
import { LeadSourceQueryRepository } from "./repository/query.repository";
import {
  CreateLeadSourceDto,
  DeleteLeadSourceDto,
  UpdateLeadSourceDto,
} from "./dto/domain.dto";
import {
  AwesomeLogger,
  BaseService,
  CmdPatternConst,
  ErrorService,
} from "../../../shared-modules";
import { EmployeeClient } from "../mgs-sender/employee.client";
import { LeadQueryRepository } from "../lead.queryside/repository/query.repository";
import { StsClient } from "../mgs-sender/sts.client";

@Injectable()
export class LeadSourceService extends BaseService {
  private readonly context = LeadSourceService.name;
  private readonly logger = new AwesomeLogger(LeadSourceService.name);
  constructor(
    private readonly queryRepository: LeadSourceQueryRepository,
    private readonly queryLeadRepository: LeadQueryRepository,
    public readonly errorService: ErrorService,
    public readonly employeeClient: EmployeeClient,
    private readonly stsClient: StsClient
  ) {
    super(errorService);
  }

  async getAll(query: any) {
    const dataQuery: any = {};
    const page: number = Number(query.page) || 1;
    const pageSize: number = Number(query.pageSize) || 10;
    // Check search
    if (query.search) {
      dataQuery.$or = [
        { name: { $regex: new RegExp(query.search), $options: "i" } },
      ];
    }
    //filter startDate && endDate
    if (query.startDate && query.endDate) {
      const startDate = new Date(query.startDate);
      const endDate = new Date(query.endDate);

      // Chuyển đổi về thời điểm bắt đầu và kết thúc của ngày
      startDate.setUTCHours(0, 0, 0, 0);
      endDate.setUTCHours(23, 59, 59, 999);

      dataQuery.createdDate = {
        $gte: new Date(startDate),
        $lte: new Date(endDate),
      };
    }
    return await this.queryRepository.findAll(page, pageSize, dataQuery);
  }

  async get(query: any) {
    if (!query.name && !query.id) return '';
    const leadSource = await this.queryRepository.findOne(query);
    if (!leadSource) {
      return this.getResponse('LEADS0002');
    }
    return leadSource;
  }

  async createLeadSource(record: CreateLeadSourceDto, userId: string) {
    this.logger.info(this.context, "createLeadSource");

    if ((await this.queryRepository.countByName(record.name.trim())) >= 1) {
      return this.getResponse('LEADS0001');
    }

    const accInfo = await this.stsClient.sendDataPromise(
      { id: userId },
      CmdPatternConst.STS.ACCOUNT.GET_INFO
    );
    const email = accInfo.listEmailById[userId] ? accInfo.listEmailById[userId] : userId;

    Object.assign(record, {
      name: record.name.trim(),
      createdBy: email,
      updatedBy: email,
      createdDate: Date.now(),
      updatedDate: Date.now(),
    });

    return await this.queryRepository.create(record);
  }

  async updateLeadSource({ id, name }: UpdateLeadSourceDto, userId: string) {
    this.logger.info(this.context, "updateLeadSourceMain");

    if ((await this.queryRepository.countByName(name.trim())) >= 1) {
      return this.getResponse('LEADS0001');
    }

    const leadSource = await this.queryRepository.findOne({ id });
    if (!leadSource) {
      return this.getResponse('LEADS0002');
    }

    const accInfo = await this.stsClient.sendDataPromise(
      { id: userId },
      CmdPatternConst.STS.ACCOUNT.GET_INFO
    );
    const email = accInfo.listEmailById[userId] ? accInfo.listEmailById[userId] : userId;

    Object.assign(leadSource, {
      name: name.trim(),
      createdBy: leadSource.createdBy,
      createdDate: leadSource.createdDate,
      updatedDate: Date.now(),
      updatedBy: email,
    });

    return await this.queryRepository.updateOne(leadSource);
  }

  async deleteLeadSource(dto: DeleteLeadSourceDto, userId: string) {
    this.logger.info(this.context, "deleteConfig");
    var id = dto.id;
    const leadSource = await this.queryRepository.findOne({id});
    if (!leadSource) {
      return this.getResponse('LEADS0002');
    }

    const numberIsUsed = await this.queryLeadRepository.countIsUsedInLead(
      leadSource.name.trim()
    );

    if (numberIsUsed >= 1) {
      return this.getResponse('LEADS0003');
    }

    Object.assign(leadSource, {
      name: leadSource.name,
      createdBy: leadSource.createdBy ?? 0,
      createdDate: leadSource.createdDate,
      updatedDate: Date.now(),
      updatedBy: userId ?? 0,
      softDelete: true,
      softDeleteReason: dto.deleteReason,
    });

    await this.queryRepository.updateOne(leadSource);
    return this.getResponse(0);
  }
}
