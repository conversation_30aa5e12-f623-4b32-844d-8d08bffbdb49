import { LeadSourceQueryRepository } from "./repository/query.repository";

import { QueryDatabaseModule } from "../database/query/query.database.module";
import { QueryProviders } from "./providers/query.cqrs.providers";
import { Module } from "@nestjs/common";
import { AuthModule } from "../auth/auth.module";
import { LeadSourceController } from "./controller";
import { LeadSourceService } from "./service";
import { CqrsModule } from "@nestjs/cqrs";
import { MgsSenderModule } from "../mgs-sender/mgs-sender.module";
import { LeadQueryRepository } from "../lead.queryside/repository/query.repository";
import { EmployeeQuerySideModule } from "../employee/module";
import { LeadQuerySideModule } from "../lead.queryside/module";

@Module({
  imports: [
    QueryDatabaseModule,
    AuthModule,
    CqrsModule,
    MgsSenderModule,
    EmployeeQuerySideModule,
    LeadQuerySideModule,
  ],
  controllers: [LeadSourceController],
  providers: [LeadSourceService, LeadSourceQueryRepository, ...QueryProviders],
  exports: [
    LeadSourceQueryRepository,
    LeadSourceQuerySideModule,
    LeadSourceService,
  ],
})
export class LeadSourceQuerySideModule {}
