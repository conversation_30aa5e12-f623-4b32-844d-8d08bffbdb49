import * as mongoose from "mongoose";
import uuid = require("uuid");

export const QuerySchema = new mongoose.Schema({
  _id: { type: String },
  id: { type: String, default: uuid.v4, index: true },
  name: { type: String },
  createdDate: { type: Date },
  createdBy: { type: String },
  updatedDate: { type: Date },
  updatedBy: { type: String },
  softDelete: { type: Boolean, default: false },
  softDeleteReason: { type: String },
});

QuerySchema.pre("save", function (next) {
  this._id = this.get("id");
  next();
});
