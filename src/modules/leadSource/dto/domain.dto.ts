import { ApiModelPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsNotEmpty, IsString, MaxLength } from "class-validator";
import { trim } from "lodash";

export class LeadSourceDto {
  @IsNotEmpty()
  @ApiModelPropertyOptional()
  id: string;
}

export class CreateLeadSourceDto {
  @ApiModelPropertyOptional()
  @Transform((value) => trim(value))
  @IsNotEmpty()
  @MaxLength(60)
  name: string;
}

export class UpdateLeadSourceDto {
  @ApiModelPropertyOptional()
  @Transform((value) => trim(value))
  @IsNotEmpty()
  id: string;

  @ApiModelPropertyOptional()
  @Transform((value) => trim(value))
  @IsNotEmpty()
  name: string;
}

export class DeleteLeadSourceDto {
  @IsString()
  @IsNotEmpty()
  @ApiModelPropertyOptional()
  id: string;

  @ApiModelPropertyOptional()
  deleteReason: string;
}
