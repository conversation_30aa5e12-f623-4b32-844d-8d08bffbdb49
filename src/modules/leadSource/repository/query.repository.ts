import { Model } from "mongoose";
import { Inject, Injectable } from "@nestjs/common";
import { ILeadSourceDocument } from "../interfaces/document.interface";
import _ = require("lodash");
import { CommonConst } from "../../../modules/shared/constant/common.const";
import { LeadSource } from "src/modules/shared/models/leadSource/model";
import { BaseRepository } from "../../shared/services/baseRepository/repository.base";
import { UpdateLeadSourceDto } from "../dto/domain.dto";
import { ILeadDocument } from "src/modules/lead.queryside/interfaces/document.interface";

@Injectable()
export class LeadSourceQueryRepository extends BaseRepository<
  ILeadSourceDocument,
  LeadSource
> {
  constructor(
    @Inject(CommonConst.QUERY_MODEL_TOKEN)
    private readonly readModel: Model<ILeadSourceDocument>
  ) {
    super();
  }

  async create(readmodel): Promise<ILeadSourceDocument> {
    return await this.readModel
      .create(readmodel)
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }

  async deleteMany(): Promise<ILeadSourceDocument> {
    return await this.readModel
      .deleteMany({})
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }

  async findAll(
    page: number,
    pageSize: number,
    query: any = {}
  ): Promise<ILeadSourceDocument[]> {
    query.softDelete = false;
    const conditions: any[] = [
      {
        $match: query,
      },
      {
        $sort: { updatedDate: -1 },
      },
      {
        $facet: {
          paginatedResults: [
            { $skip: Math.floor(pageSize * page - pageSize) },
            { $limit: pageSize },
          ],
          totalCount: [{ $count: "count" }],
        },
      },
    ];

    return await this.readModel
      .aggregate(conditions)
      .allowDiskUse(true)
      .exec()
      .then((result) => {
        const total = result[0].totalCount[0]
          ? result[0].totalCount[0].count
          : 0;
        return {
          rows: result[0].paginatedResults,
          page,
          pageSize,
          total,
          totalPages: Math.floor((total + pageSize - 1) / pageSize),
        };
      });
  }

  async findOne(query): Promise<ILeadSourceDocument> {
    query.softDelete = false;
    return await this.readModel
      .findOne(query)
      .exec()
      .then((result) => {
        return result;
      });
  }

  async countByName(name: string): Promise<number> {
    return await this.readModel
      .countDocuments({
        name: name,
        softDelete: false,
      })
      .exec();
  }

  async updateOne(model: any) {
    return await this.readModel
      .updateOne(
        { id: model.id },
        {
          $set: model,
        }
      )
      .exec()
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }

  async delete(id: string) {
    return await this.readModel
      .deleteOne({ id })
      .then((response) => {
        return response;
      })
      .catch((error) => {
        return error;
      });
  }
}
