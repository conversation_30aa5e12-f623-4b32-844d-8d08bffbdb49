import { Modu<PERSON> } from "@nestjs/common";
import { QueryDatabaseModule } from "../../database/query/query.database.module";
import { AuthModule } from "../../auth/auth.module";
import { LeadjobExportService } from "./service";
import { LeadjobExportRepository } from "./repository";
import { LoggerModule } from "../../logger/logger.module";
import { MgsSenderModule } from "../../mgs-sender/mgs-sender.module";
import { LeadjobProviders } from "../infra/providers";

@Module({
  imports: [
    QueryDatabaseModule,
    AuthModule,
    MgsSenderModule,
    LoggerModule,
  ],
  providers: [
    ...LeadjobProviders,
    LeadjobExportRepository,
    LeadjobExportService,
  ],
  exports: [LeadjobExportService],
})
export class LeadjobExportModule {}
