import { pick } from "lodash";
import { ExploitCareEnum } from "src/modules/shared/enum/exploit.enum";
import { ILeadjob, IProject, ITakeCare } from "../../interfaces/base.interface";

export class LeadjobPagingResponseDto {
  rows: LeadjobResponseDto[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;

  constructor(init?: Partial<LeadjobPagingResponseDto>) {
    Object.assign(
      this,
      pick(init, ["rows", "total", "page", "pageSize", "totalPages"])
    );
  }
}

export class LeadjobResponseDto {
  customerId: string;
  id: string;
  title: string;
  name: string;
  address: string;
  phone: string;
  status: string;
  processBy: string;
  type: string;
  description: string;
  createdDate: Date;
  updatedDate: Date;
  email: string;
  notes: Object[];
  timestamp: number;
  customer: object;
  property: object;
  processedDate: Date;
  processedHistory: any[];
  code: string;
  assignedDate: Date;
  // new
  note: string;
  importedBy: Object;
  // LeadCare repository feature ====================
  exploitStatus: ExploitCareEnum;
  exploitStatusModifiedBy: string;
  repoId: string;
  takeCare: ITakeCare;
  project: IProject;
  leadCareId: string;
  block: string;
  isMonthly: boolean;
  dateEndWork: Date;
  timeStartWork: Date;
  timeEndWork: Date;
  customData: Object;
  idRepoConfig: string;
  nameRepoConfig: string;
  repoCode: string;
  constructor(init?: Partial<LeadjobResponseDto | ILeadjob>) {
    Object.assign(
      this,
      pick(init, [
        "id",
        "createdBy",
        "modifiedDate",
        "modifiedBy",
        "title",
        // Custom fields
        "customerId",
        "name",
        "address",
        "phone",
        "description",
        "createdDate",
        "updatedDate",
        "email",
        "notes",
        "customer",
        "processedDate",
        "code",
        "assignedDate",
        "importedBy",
        // LeadCare repository feature ====================
        "exploitStatus",
        "takeCare",
        "project",
        "note",
        "leadCareId",
        "block",
        "isMonthly",
        "dateEndWork",
        'timeStartWork',
        'timeEndWork',
        'customData',
        'repoCode',
        'idRepoConfig',
        'nameRepoConfig',
        'implementMaxDate',
        'isFirstWorkId',
        'reason',
      ])
    );
  }
}
