import { CqrsModule } from '@nestjs/cqrs';
import { Module, HttpModule } from '@nestjs/common';
import { CommandHandlers } from './commands/handlers';
import { EventHandlers } from './events';
import { CareSagas } from './sagas/sagas';
import { LeadCareController } from './controller';
import { LeadCareDomainService } from './service';
import { EventStreamRepository } from './repository/event-stream.repository';
import { DomainDatabaseModule } from '../database/domain/domain.database.module';
import { CqrsProviders } from './providers/cqrs.domain.providers';
import { LeadCareQuerySideModule } from '../leadCare.queryside/module';
import { EmployeeQuerySideModule } from '../employee/module';
import { LeadCareHistoryQuerySideModule } from '../leadCare-history/module';
import { LeadCarePublicController } from './public-controller';
import { RawModule } from '../raw/module';
import { CustomerLeadCareController } from './customer-controller';
import { MgsSenderModule } from '../mgs-sender/mgs-sender.module';
import { LoggerModule } from '../logger/logger.module';
import { CodeGenerateModule } from '../code-generate/module';
import { HistoryImportQuerySideModule } from '../history-import/module';
import { LeadRepoCareQueryModule } from '../leadRepoCare.queryside/module';
import { LeadRepoCareDomainModule } from '../leadRepoCare.domain/module';
import { LeadCareDomainServiceExtends } from './service.extend';
import { LeadjobModule } from '../leadJob/module';

@Module({
  imports: [
    CqrsModule,
    DomainDatabaseModule,
    LeadCareQuerySideModule,
    EmployeeQuerySideModule,
    LeadCareHistoryQuerySideModule,
    RawModule,
    MgsSenderModule,
    LoggerModule,
    HttpModule,
    CodeGenerateModule,
    HistoryImportQuerySideModule,
    LeadRepoCareQueryModule,
    LeadRepoCareDomainModule,
    LeadjobModule,
  ],
  controllers: [LeadCareController, LeadCarePublicController, CustomerLeadCareController],
  providers: [
    LeadCareDomainService,
    EventStreamRepository,

    CareSagas,
    ...CqrsProviders,
    ...CommandHandlers,
    ...EventHandlers,
    RawModule,
    LeadCareDomainServiceExtends
  ],
  exports: [
    LeadCareDomainModule,
    LeadCareDomainService,
    EventStreamRepository,
    LeadCareDomainServiceExtends
  ]
})
export class LeadCareDomainModule { }
