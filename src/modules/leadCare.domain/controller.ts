import {
    Controller,
    Post,
    Body,
    Headers,
    UseGuards,
    UseInterceptors,
    Get,
    Param,
    UploadedFiles,
    Put,
    ValidationPipe as ValidationPipeOfficial,
} from '@nestjs/common';
import { RolesGuard } from '../../common/guards/roles.guard';
import { LoggingInterceptor } from '../../common/interceptors/logging.interceptor';
import {
    AssignLeadCareDto,
    RequestDto,
    RejectDto,
    CreateLeadCareDto,
    ImportLeadCareDemandDto,
    ImportLeadCareFromPublicForm,
    ImportLeadCareAsExcelDto,
    UpdateStatusDto,
    UpdateLeadCareDto,
    UpdateStatusAssignDto,
    RejectLeadCareDto,
    HandleTransferRequestDto
} from './dto/leadCare.dto';
import { LeadCareDomainService } from './service';
import { ACGuard, UseRoles } from 'nest-access-control';
import { Action } from '../shared/enum/action.enum';
import { ValidationPipe } from '../../common/pipes/validation.pipe';
import { ApiUseTag<PERSON>, ApiBearerAuth, ApiForbiddenResponse, ApiCreatedResponse } from '@nestjs/swagger';
import { Usr } from '../shared/services/user/decorator/user.decorator';
import { PermissionConst } from '../shared/constant/permission.const';
import { FilesInterceptor } from '@nestjs/platform-express';
import { CommonConst } from '../shared/constant/common.const';
import { Public } from '../../common/decorators/public.decorator';
import { AuthClientKeyGuard } from '../../common/guards/auth.client-key.guard';
import { LeadCareDomainServiceExtends } from './service.extend';
import { CustomAuthGuard } from '../../common/guards/auth.guard';
import {ExploitCareEnum} from "../shared/enum/exploit.enum";

@ApiBearerAuth()
@ApiUseTags('v1/leadCare')
@Controller('v1/leadCare')
@UseInterceptors(LoggingInterceptor)
@UseGuards(CustomAuthGuard)
export class LeadCareController {
    private resSuccess = { success: true };
    private actionName: string = Action.NOTIFY;
    constructor(
        private readonly leadCareService: LeadCareDomainService,
        private readonly leadCareServiceExt: LeadCareDomainServiceExtends
    ) { }

    @Post()
    async createLeadCare(
        @Usr() user,
        @Body(new ValidationPipe()) dto: CreateLeadCareDto, @Headers('timezoneclient') timezoneclient: string, @Headers('act') actionName?: string) {
        this.actionName = (actionName || this.actionName);

        return await this.leadCareService.createLeadCare(dto, this.actionName, timezoneclient, user.id);
    }
    @UseGuards(RolesGuard, ACGuard)
    @UseRoles({
        resource: PermissionConst.IMPORT_LEAD_CARE, // => feature name
        action: 'read',
        possession: 'own',
    })
    @UseInterceptors(FilesInterceptor('files'))
    @Post('/import-demand')
    async createDemandLeadCare(
        @UploadedFiles() files,
        @Usr() user,
        @Body(new ValidationPipe()) dto: ImportLeadCareDemandDto, @Headers('timezoneclient') timezoneclient: string, @Headers('act') actionName?: string) {
        this.actionName = (actionName || this.actionName);
        await this.leadCareService.importDemandTicket(files , dto ,this.actionName, timezoneclient, user.id);
        return true;
    }
    @UseGuards(RolesGuard, ACGuard)
    @UseRoles({
        resource: PermissionConst.IMPORT_LEAD_CARE, // => feature name
        action: 'read',
        possession: 'own',
    })
    @Post('import')
    async importLeadCare(
        @Body(new ValidationPipe()) dtos: CreateLeadCareDto[], @Headers('timezoneclient') timezoneclient: string, @Headers('act') actionName?: string) {
        this.actionName = (actionName || this.actionName);
        for (const dto of dtos) {
            await this.leadCareService.createLeadCare(dto, this.actionName, timezoneclient);
        }
        return true;
    }

    @UseGuards(RolesGuard, ACGuard)
    @UseRoles({
        resource: PermissionConst.PULL_LEAD_CARE, // => feature name
        action: 'read',
        possession: 'own',
    })
    @Post('/pull')
    @ApiCreatedResponse({ description: 'The record has been successfully pulled.' })
    @ApiForbiddenResponse({ description: 'Forbidden.' })
    async pullLeadCare(
        @Usr() user,
        @Headers('timezoneclient') timezoneclient: string,
        @Headers('act') actionName?: string) {
        this.actionName = (actionName || this.actionName);
        const res = await this.leadCareService.pullLeadCare(user, this.actionName, timezoneclient);
        return { success: res };
    }

    @UseGuards(RolesGuard, ACGuard)
    @UseRoles({
        resource: PermissionConst.PULL_LEAD_CARE, // => feature name
        action: 'read',
        possession: 'own',
    })
    @Post('/pullPrimary')
    @ApiCreatedResponse({ description: 'The record has been successfully pulled.' })
    @ApiForbiddenResponse({ description: 'Forbidden.' })
    async pullPrimaryLeadCare(
        @Usr() user,
        @Headers('timezoneclient') timezoneclient: string,
        @Headers('act') actionName?: string) {
        this.actionName = (actionName || this.actionName);
        const res = await this.leadCareService.pullLeadCare(user, this.actionName, timezoneclient, CommonConst.TYPE.PRIMARY);
        return { success: res };
    }

    @UseGuards(RolesGuard, ACGuard)
    @UseRoles({
        resource: PermissionConst.PULL_LEAD_CARE, // => feature name
        action: 'read',
        possession: 'own',
    })
    @Post('/calling')
    @ApiCreatedResponse({ description: 'The record has been successfully updated.' })
    @ApiForbiddenResponse({ description: 'Forbidden.' })
    async callingLeadCare(
        @Usr() user,
        @Body(new ValidationPipe()) dto: RequestDto,
        @Headers('act') actionName?: string) {
        this.actionName = (actionName || this.actionName);
        const res = await this.leadCareService.callingLeadCare(user, dto, this.actionName) || this.resSuccess;
        return res;
    }

    @UseGuards(RolesGuard, ACGuard)
    @UseRoles({
        resource: PermissionConst.REJECT_LEAD_CARE,
        action: 'read',
        possession: 'own',
    })
    @Post('/reject')
    @ApiCreatedResponse({ description: 'The record has been successfully rejected.' })
    @ApiForbiddenResponse({ description: 'Forbidden.' })
    async rejectLeadCare(
        @Usr() user,
        @Body(new ValidationPipe()) dto: RejectDto,
        @Headers('act') actionName?: string) {
        this.actionName = (actionName || this.actionName);
        const res = await this.leadCareService.rejectLeadCare(user, dto, this.actionName);
        return { success: res };
    }

    @UseGuards(RolesGuard, ACGuard)
    @UseRoles({
        resource: PermissionConst.REJECT_LEAD_CARE,
        action: 'read',
        possession: 'own',
    })
    @Post('/reject-all')
    @ApiCreatedResponse({ description: 'The record has been successfully rejected.' })
    @ApiForbiddenResponse({ description: 'Forbidden.' })
    async rejectAllLeadCare(
        @Usr() user,
        @Body(new ValidationPipe()) dto: RejectDto,
        @Headers('act') actionName?: string) {
        this.actionName = (actionName || this.actionName);
        const res = await this.leadCareService.rejectAllLeadCare(user, dto, this.actionName);
        return { success: res };
    }

    @UseGuards(RolesGuard, ACGuard)
    @UseRoles({
        resource: PermissionConst.FAIL_LEAD_CARE,
        action: 'read',
        possession: 'own',
    })
    @Post('/fail')
    @ApiCreatedResponse({ description: 'The record has been successfully failed.' })
    @ApiForbiddenResponse({ description: 'Forbidden.' })
    async failLeadCare(
        @Usr() user,
        @Body(new ValidationPipe()) dto: RequestDto,
        @Headers('act') actionName?: string) {
        this.actionName = (actionName || this.actionName);
        return await this.leadCareService.failLeadCare(user, dto, this.actionName) || this.resSuccess;
    }

    @UseGuards(RolesGuard, ACGuard)
    @UseRoles({
        resource: PermissionConst.ASSIGN_LEAD_CARE,
        action: 'read',
        possession: 'own',
    })
    @Post('/assign')
    @ApiCreatedResponse({ description: 'The record has been successfully assign.' })
    @ApiForbiddenResponse({ description: 'Forbidden.' })
    async assign(
        @Usr() user,
        @Body(new ValidationPipe()) dto: AssignLeadCareDto,
        @Headers('timezoneclient') timezoneclient: string,
        @Headers('act') actionName?: string) {
        this.actionName = (actionName || this.actionName);
        return await this.leadCareService.assignLeadCare(user, dto, this.actionName, timezoneclient) || this.resSuccess;
    }

    @UseGuards(RolesGuard, ACGuard)
    @UseRoles({
        resource: PermissionConst.UNPROCESS_LEAD_CARE,
        action: 'read',
        possession: 'own',
    })
    @Post('/unprocess')
    @ApiCreatedResponse({ description: 'The record has been successfully unprocessing.' })
    @ApiForbiddenResponse({ description: 'Forbidden.' })
    async unprocessLeadCare(
        @Usr() user,
        @Body(new ValidationPipe()) dto: RequestDto,
        @Headers('act') actionName?: string) {
        this.actionName = (actionName || this.actionName);
        return await this.leadCareService.unprocessLeadCare(user, dto, this.actionName) || this.resSuccess;
    }

    @UseGuards(RolesGuard, ACGuard)
    @UseRoles({
        resource: PermissionConst.PROCESS_LEAD_CARE,
        action: 'read',
        possession: 'own',
    })
    @Post('/process')
    @ApiCreatedResponse({ description: 'The record has been successfully processing.' })
    @ApiForbiddenResponse({ description: 'Forbidden.' })
    async processLeadCare(
        @Usr() user,
        @Body(new ValidationPipe()) dto: RequestDto,
        @Headers('act') actionName?: string) {
        this.actionName = (actionName || this.actionName);
        return await this.leadCareService.processLeadCare(user, dto, this.actionName) || this.resSuccess;
    }

    @Get(':id/events')
    @ApiCreatedResponse({ description: 'Get all leadCare events stream.' })
    @ApiForbiddenResponse({ description: 'Forbidden.' })
    async getEvents(
        @Usr() user,
        @Param('id') id: string,
        @Headers('act') actionName?: string) {
        this.actionName = (actionName || this.actionName);
        return await this.leadCareService.getEvents(id);
    }


    @UseGuards(RolesGuard, ACGuard)
    @UseRoles({
        resource: PermissionConst.PENDING_LEAD_CARE,
        action: 'read',
        possession: 'own',
    })
    @Post('/pending')
    @ApiCreatedResponse({ description: 'The record has been successfully pending.' })
    @ApiForbiddenResponse({ description: 'Forbidden.' })
    async pendingLeadCare(
        @Usr() user,
        @Body(new ValidationPipe()) dto: RequestDto,
        @Headers('act') actionName?: string) {
        this.actionName = (actionName || this.actionName);
        return await this.leadCareService.pendingLeadCare(dto, this.actionName, user) || this.resSuccess;
    }

    @Public()
    @Post('/import-from-public-form')
    @UseGuards(AuthClientKeyGuard)
    importFromPublicForm(
        @Body(new ValidationPipeOfficial({ transform: true })) leadCare: ImportLeadCareFromPublicForm,
        @Headers('timezoneclient') timezoneClient: string,
        @Headers('act') actionName?: string,
        @Headers('client-id') clientId?: string,
        @Headers('secret-key') secretKey?: string,
    ) {
        if (actionName) this.actionName = actionName;

        return this.leadCareService.createLeadCare(leadCare, this.actionName, timezoneClient, null, clientId, secretKey);
    }

    @UseGuards(RolesGuard, ACGuard)
    @UseRoles({
      resource: PermissionConst.IMPORT_LEAD_CARE,
      action: 'read',
      possession: 'own',
    })
    @UseInterceptors(FilesInterceptor('files'))
    @Post('/import-from-excel')
    importLeadCareFromExcel(
        @UploadedFiles() files,
        @Usr() user,
        @Body(new ValidationPipe()) options: ImportLeadCareAsExcelDto,
        @Headers('timezoneclient') timezoneclient: string,
        @Headers('act') actionName?: string,
    ) {
      if (actionName) this.actionName = actionName;

      return this.leadCareServiceExt.importLeadCareFromExcel(files, options, this.actionName, timezoneclient, user);
    }

    @UseGuards(RolesGuard, ACGuard)
    @UseRoles({
      resource: PermissionConst.CREATE_LEAD_CARE,
      action: 'read',
      possession: 'own',
    })
    @Put('/update-exploit-status')
    @ApiCreatedResponse({ description: 'The record has been successfully.' })
    @ApiForbiddenResponse({ description: 'Forbidden.' })
    async updateExploitStatus(
      @Usr() user,
      @Body(new ValidationPipe()) dto: UpdateStatusDto,
      @Headers('act') actionName?: string,
    ) {
      this.actionName = actionName || this.actionName;
      return (await this.leadCareService.updateExploitStatus(dto, this.actionName, user.id)) || this.resSuccess;
    }

    @UseGuards(RolesGuard, ACGuard)
    @UseRoles({
      resource: PermissionConst.CREATE_LEAD_CARE,
      action: 'read',
      possession: 'own',
    })
    @Put('/update-leadCare')
    @ApiCreatedResponse({ description: 'The record has been successfully.' })
    @ApiForbiddenResponse({ description: 'Forbidden.' })
    async updateStatus(@Usr() user, @Body(new ValidationPipe()) dto: UpdateLeadCareDto, @Headers('act') actionName?: string) {
      this.actionName = actionName || this.actionName;
      return (await this.leadCareService.updateLeadCare(dto, this.actionName, user.id)) || this.resSuccess;
    }
    
    @UseGuards(RolesGuard, ACGuard)
    @UseRoles({
      resource: PermissionConst.ASSIGN_LEAD_CARE,
      action: 'read',
      possession: 'own',
    })
    @Put('/assign-work')
    @ApiCreatedResponse({ description: 'The record has been successfully.' })
    @ApiForbiddenResponse({ description: 'Forbidden.' })
    async assignWork(
      @Usr() user,
      @Body(new ValidationPipe()) dto: UpdateStatusAssignDto,
      @Headers('act') actionName?: string,
    ) {
      this.actionName = actionName || this.actionName;
      return (await this.leadCareService.updateExploitStatusAssign(dto, this.actionName, user)) || this.resSuccess;
    }

    @UseRoles({
        resource: PermissionConst.UPDATE_LEAD_CARE,
        action: 'read',
        possession: 'own',
    })
    @Put(':id/reject')
    @ApiCreatedResponse({ description: 'The record has been successfully.' })
    @ApiForbiddenResponse({ description: 'Forbidden.' })
    async reject(
      @Usr() user,
      @Param('id') id,
      @Body(new ValidationPipe()) dto: RejectLeadCareDto,
      @Headers('act') actionName?: string,
    ) {
        this.actionName = actionName || this.actionName;
        dto.id = id;
        dto.status = ExploitCareEnum.CANCEL;
        return (await this.leadCareService.updateExploitStatus(dto, this.actionName, user.id)) || this.resSuccess;
    }

    @UseRoles({
        resource: PermissionConst.UPDATE_LEAD_CARE,
        action: 'read',
        possession: 'own',
    })
    @Put(':id/survey')
    @ApiCreatedResponse({ description: 'The record has been successfully.' })
    @ApiForbiddenResponse({ description: 'Forbidden.' })
    async survey(
      @Usr() user,
      @Param('id') id,
      @Body(new ValidationPipe()) dto: RejectLeadCareDto,
      @Headers('act') actionName?: string,
    ) {
        this.actionName = actionName || this.actionName;
        dto.id = id;
        dto.status = ExploitCareEnum.SURVEY;
        return (await this.leadCareService.updateExploitStatus(dto, this.actionName, user.id)) || this.resSuccess;
    }

    @UseRoles({
        resource: PermissionConst.HANDLE_TRANSFER_REQUEST,
        action: 'read',
        possession: 'own',
    })
    @Put('handle-transfer-request')
    @ApiCreatedResponse({ description: 'The record has been successfully.' })
    @ApiForbiddenResponse({ description: 'Forbidden.' })
    @UseGuards(RolesGuard, ACGuard)
    async handleTransferRequest(
      @Usr() user,
      @Body(new ValidationPipe()) dto: HandleTransferRequestDto,
      @Headers('act') actionName?: string,
    ) {
        this.actionName = actionName || this.actionName;
        return (await this.leadCareService.HandleTransferRequest(user, dto, this.actionName)) || this.resSuccess;
    }
}
