
import { Injectable, BadRequestException, NotFoundException, HttpService } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import {
  CreateLeadCareDto, CreateServiceRequestDto,
  ImportLeadCareAsExcelDto,
  ImportLeadCareDemandDto,
  ImportLeadCareFromPublicForm,
  UpdateLeadCareDto,
  UpdateStatusAssignDto,
  UpdateStatusDto,
} from './dto/leadCare.dto';
import {
    IExploitHistory
} from '../shared/services/leadCare/interfaces/leadCare.interface';
import { CreateLeadCareCommand } from './commands/impl/create.cmd';
import { Action } from '../shared/enum/action.enum';
import { ProcessLeadCareCommand } from './commands/impl/process.cmd';
import { CompleteLeadCareCommand } from './commands/impl/complete.cmd';
import { FailLeadCareCommand } from './commands/impl/fail.cmd';
import { ChangeStatusLeadCareCommand } from './commands/impl/changeStatus.cmd';
import { UnprocessLeadCareCommand } from './commands/impl/unprocess.cmd';
import { QueryRepository } from '../leadCare.queryside/repository/query.repository';
import { isNullOrUndefined } from 'util';
import { EmployeeQueryRepository } from '../employee/repository/query.repository';
import { ConfigTimeConst } from '../shared/constant/config-time.const';
import { AssignLeadCareCommand } from './commands/impl/assign.cmd';
import { LifeCycleStatusEnum } from '../shared/enum/life-cycle-status.enum';
import { ReassignLeadCareCommand } from './commands/impl/reassign.cmd';
import { PendingLeadCareCommand } from './commands/impl/pending.cmd';
import { StatusEnum } from '../shared/enum/status.enum';
import { EventStreamRepository } from './repository/event-stream.repository';
import { UpdateLeadCareCommand } from './commands/impl/update.cmd';
import { ExpiredLeadCareCommand } from './commands/impl/expired.cmd';
import { RawQueryRepository } from '../raw/repository/query.repository';
import { MsxLoggerService } from '../logger/logger.service';
import { ErrorConst } from '../shared/constant/error.const';
import { ILeadCareProcessed } from '../shared/services/leadCare/interfaces/leadCare.interface';
import { map } from 'rxjs/operators';
import { OrgchartClient } from '../mgs-sender/orgchart.client';
import { CommonConst } from '../../modules/shared/constant/common.const';
import { CmdPatternConst } from '../../modules/shared/constant/cmd-pattern.const';
import { ConfigService } from '@nestjs/config';
import { CommonUtils } from '../shared/classes/class-utils';
import { CodeGenerateService } from '../code-generate/service';
import { HistoryImportService } from '../history-import/service';
const XLSX = require('xlsx');
import { LeadRepoCareQueryRepository } from '../leadRepoCare.queryside/repositories/query.repository';
import * as _ from 'lodash';
import { ConversationEnum, ExploitCareEnum } from '../shared/enum/exploit.enum';
import { LeadRepoCareEnum} from "../shared/enum/type.enum";
import {PrimaryContractClient} from "../mgs-sender/primary-contract.client";
import { CustomerClosedTicketCareCommand } from './commands/impl/customerClosedTicket.cmd';
import { LeadjobService } from '../leadJob/application/service';
import { EmployeeClient } from '../mgs-sender/employee.client';
import { isEmpty } from 'lodash';
import { NotificationClient } from '../mgs-sender/notification.client';
import {PropertyClient} from "../mgs-sender/property.client";

const uuid = require('uuid');
const timezone = require('moment-timezone');
const request = require('request');

@Injectable()
export class LeadCareDomainService {
    private readonly context = LeadCareDomainService.name;
    private commandId: string;
    constructor(
        private readonly commandBus: CommandBus,
        private readonly queryRepository: QueryRepository,
        private readonly employeeRepository: EmployeeQueryRepository,
        private readonly eventStreamRepository: EventStreamRepository,
        private readonly codeGenerateService: CodeGenerateService,
        private readonly configService: ConfigService,
        private readonly loggerService: MsxLoggerService,
        private readonly orgchartClient: OrgchartClient,
        private readonly historyImportService: HistoryImportService,
        private readonly leadRepoCareRepository: LeadRepoCareQueryRepository,
        private readonly primaryContractClient: PrimaryContractClient,
        private readonly leadJobService: LeadjobService,
        private readonly employeeClient : EmployeeClient, 
        private readonly notificationClient: NotificationClient,
        private readonly propertyClient: PropertyClient,

    ) { }

    async rejectLeadCare(user: any, dto: any, actionName: string) {
        this.loggerService.log(this.context, 'Reject service');
        const leadCare = await this.queryRepository.findLeadCareById(dto.id);
        if (!leadCare) {
            throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.HAVE_NOT_ANY_LEAD) });
        }
        
        // Check ticket
        if (leadCare.lifeCycleStatus !== LifeCycleStatusEnum.PENDING) {
            const history: ILeadCareProcessed[] = leadCare.processedHistory || [];
            history.push({
                id: uuid.v4(),
                processedDate: new Date(),
                processBy: user.id,
                isReject: true,
                isTimeOut: false,
                causeReject: dto.causeReject
            } as ILeadCareProcessed);
            const model: any = {
                id: leadCare.id,
                customerId: leadCare.customerId,
                modifiedBy: user.email,
                // processBy: null,
                // timeOut: null,
                lifeCycleStatus: LifeCycleStatusEnum.REMOVED,
                reason: dto.causeReject[0],
                processedHistory: history
            };
            // Get DVKH POS
            // let pos = null;
            // try {
            //     const request = {
            //         model: null,
            //         action: CommonConst.AGGREGATES.LEAD.REJECTED
            //     };
            //     pos = await this.orgchartClient.sendDataPromise(request, CmdPatternConst.SERVICE.LEAD.LISTENER);
            //     console.log('pos : ', pos);
                
            // } catch (err) {
            //     this.loggerService.log(this.context, '[Error] Cannot find POS DVKH', err);
            // }
            // flow mới update 20/2/2020
            // Yêu cầu bị trả về thì đặt lifeCycleStatus là removed. 
            // và gán vào pos DVKH 
            // không quan tâm tới trả về mấy lần
            // follow mới update 29/06/2020
            // Yêu cầu trả về vẫn đặt ở pos đó, status là removed
            // model.pos = {
            //     id: pos.id,
            //     name: pos.name,
            //     code: pos.code
            // };
            this.commandId = uuid.v4();
            await this.executeCommand(Action.FAIL, actionName, this.commandId, model)
        }
        // this.employeeRepository.penalty({ id: user.id });
        return true;
    }

    async rejectAllLeadCare(user: any, dto: any, actionName: string) {
        this.loggerService.log(this.context, 'Reject service');
        const leadCares = await this.queryRepository.findLeadCaresAssignedEmployee(user.id);
        if (!leadCares || leadCares.length === 0) {
            throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.HAVE_NOT_ANY_LEAD) });
        }
        
        // Check ticket
        for (const leadCare of leadCares) {
            if (leadCare.lifeCycleStatus !== LifeCycleStatusEnum.PENDING) {
                const history: ILeadCareProcessed[] = leadCare.processedHistory || [];
                history.push({
                    id: uuid.v4(),
                    processedDate: new Date(),
                    processBy: user.id,
                    isReject: true,
                    isTimeOut: false,
                    causeReject: dto.causeReject
                } as ILeadCareProcessed);
                this.commandId = uuid.v4();
                const model: any = {
                    id: leadCare.id,
                    customerId: leadCare.customerId,
                    // processBy: null,
                    // timeOut: null,
                    modifiedBy: user.email,
                    lifeCycleStatus: LifeCycleStatusEnum.REMOVED,
                    reason: dto.causeReject[0],
                    processedHistory: history
                };
                // Get DVKH POS
                // let pos = null;
                // try {
                //     const request = {
                //         model: null,
                //         action: CommonConst.AGGREGATES.LEAD.REJECTED
                //     };
                //     pos = await this.orgchartClient.sendDataPromise(request, CmdPatternConst.SERVICE.LEAD.LISTENER);
                // } catch (err) {
                //     this.loggerService.log(this.context, '[Error] Cannot find POS DVKH', err);
                // }
                // flow mới update 20/2/2020
                // Yêu cầu bị trả về thì đặt lifeCycleStatus là removed. 
                // và gán vào pos DVKH 
                // không quan tâm tới trả về mấy lần
                // follow mới update 29/06/2020
                // Yêu cầu trả về vẫn đặt ở pos đó, status là removed
                // model.pos = {
                //     id: pos.id,
                //     name: pos.name,
                //     code: pos.code
                // };
                this.commandId = uuid.v4();
                await this.executeCommand(Action.FAIL, actionName, this.commandId, model);
            }
        }
        // this.employeeRepository.penalty({ id: user.id });
        return true;
    }

    async createLeadCare(dto: CreateLeadCareDto|ImportLeadCareFromPublicForm, actionName: string, timezoneclient?: string, userId?: string, clientId?: string, secretKey?: string) {
        // if (!CommonUtils.stringNotEmpty(dto.recaptcha)) {
        //     throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.INVALID_FIELD, 'recaptcha') });
        // }
        // if (dto.recaptcha) {
        //     let checkRecaptcha = await this.validateRecaptcha(dto.recaptcha);
        //     if (!checkRecaptcha === true) {
        //         throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.INVALID_FIELD, 'recaptcha') });
        //     }
        // }
        if (!isNullOrUndefined(userId)) {
            const employee: any = await this.employeeRepository.findOne({id: userId});
            if (!isNullOrUndefined(employee) && !isNullOrUndefined(employee.pos) && dto instanceof CreateLeadCareDto) {
                dto.pos = {
                    id: employee.pos?.id,
                    name: employee.pos?.name,
                    code: employee.pos?.code,
                    type: employee.pos?.type,
                    parentId: employee.pos?.parentId,
                    address: employee.pos?.personalInfo?.address || ''
                };

                dto.timeOut = this.createTimeout(timezoneclient);
                dto.timezoneclient = timezoneclient;
                dto.processBy = userId;
                dto.lifeCycleStatus = dto.lifeCycleStatus || LifeCycleStatusEnum.ASSIGNED;
                dto.createdBy = userId;
                dto.modifiedBy = userId;
                dto.employee = {
                    id: employee.id,
                    name: employee.name,
                    code: employee.code,
                    email: employee.email
                };

            }
        }

        if (dto instanceof ImportLeadCareFromPublicForm) {
            dto.importedBy = {
                clientId: clientId,
            }
        }
        this.loggerService.log(this.context, 'create service');

        // Check leadCare is hot or not.
        if (dto instanceof ImportLeadCareFromPublicForm && !(dto.isHot || dto.repoConfigCode)) throw new BadRequestException();
        if (dto instanceof ImportLeadCareFromPublicForm && dto.isHot && dto.repoConfigCode) delete dto.repoConfigCode;
        // Check leadCare repository is available.
        if (dto instanceof ImportLeadCareFromPublicForm) {
            const repo = await this.leadRepoCareRepository.findById(dto.repoId);

            if (!repo) throw new BadRequestException();

            if (dto.repoConfigCode && !_.find(repo.configs, (config) => config.code === dto.repoConfigCode)) throw new BadRequestException();
        }

        this.commandId = uuid.v4();
        if (isNullOrUndefined(dto.id) || dto.id.trim().length === 0) {
            const now = timezone().tz(timezoneclient);
            // const rawData = await this.rawRepository.findOne({ id: dto.rawId });

            // if (isNullOrUndefined(rawData)) {
            //     throw new NotFoundException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, 'raw') });
            // }

            // dto.phone = rawData.phone;
            // dto.email = rawData.email;
            // dto.customerId = rawData.customerId;
            // this.rawRepository.delete(rawData);

            const startWorkingTime = now.clone().set({ hour: ConfigTimeConst.START_WORKING_HOURS, minute: 0, second: 0, millisecond: 0 });
            const endWorkingTime = now.clone().set({ hour: ConfigTimeConst.END_WORKING_HOURS, minute: 0, second: 0, millisecond: 0 });

            let t0;
            let t1;
            let t2;
            let t3;
            if(!dto.createdDate){
                dto.createdDate = now.format();
            }

            if (now.isBefore(startWorkingTime)) {
                t0 = startWorkingTime;
            } else if (now.isBefore(endWorkingTime)) {
                t0 = now;
            } else {
                startWorkingTime.add(1, 'days');
                endWorkingTime.add(1, 'days');
                t0 = startWorkingTime;
            }
            dto.t0 = t0.format();

            t1 = t0.add(ConfigTimeConst.TICKET_GREEN_TIME, 'minutes');
            if (t1.isAfter(endWorkingTime)) {
                startWorkingTime.add(1, 'days');
                t1 = startWorkingTime.clone().add(t1.diff(endWorkingTime, 'minutes'), 'minutes');
                endWorkingTime.add(1, 'days');
            }
            dto.t1 = t1.format();

            t2 = t1.add(ConfigTimeConst.TICKET_YELLOW_TIME, 'minutes');
            if (t2.isAfter(endWorkingTime)) {
                startWorkingTime.add(1, 'days');
                t2 = startWorkingTime.clone().add(t2.diff(endWorkingTime, 'minutes'), 'minutes');
                endWorkingTime.add(1, 'days');
            }
            dto.t2 = t2.format();

            t3 = t2.add(ConfigTimeConst.TICKET_RED_TIME, 'minutes');
            if (t3.isAfter(endWorkingTime)) {
                startWorkingTime.add(1, 'days');
                t3 = startWorkingTime.clone().add(t3.diff(endWorkingTime, 'minutes'), 'minutes');
            }
            dto.t3 = t3.format();

            dto.timezoneclient = timezoneclient;

            dto.processedDate = now.format();
            //add code
            dto.code = await this.codeGenerateService.generateCode(CommonConst.LEAD_CARE_PREFIX);
            return await this.executeCommand(Action.CREATE, actionName, this.commandId, dto);
        } else {
            if (dto.type === CommonConst.TYPE.PRIMARY) {
                const leadCare = await this.queryRepository.findOne({id: dto.id});
                const newProcess = {
                    isInNeed: dto.isInNeed,
                    reasonNoNeed: dto.reasonNoNeed,
                    otherReason: dto.otherReason,
                    interestedProduct: dto.interestedProduct,
                    direction: dto.direction,
                    needLoan: dto.needLoan,
                    isAppointment: dto.isAppointment,
                    isVisited: dto.isVisited,
                    note: dto.note,
                    isCalled: dto.isCalled,
                    callId: dto.isCalled ? dto.callId : '',
                    startCall: dto.isCalled ? dto.startCall : null,
                    endCall: dto.isCalled ? dto.endCall : null,
                    answerTime: dto.isCalled ? dto.answerTime : 0,

                    updateDate: new Date(),

                };
                if (leadCare && leadCare.callHistory && leadCare.callHistory.length > 0) {
                    dto.callHistory = leadCare.callHistory;
                    dto.callHistory.push(newProcess);
                } else {
                    dto.callHistory = [newProcess];
                }
            }
        }
        return await this.executeCommand(Action.UPDATE, actionName, this.commandId, dto);
    }

    async validateRecaptcha(recaptcha) {
        return new Promise(async (resolve, reject) => {
            await request(this.configService.get('URL_CAPTCHA') + '?secret='
                + this.configService.get('SECRET_KEY_CAPTCHA') + '&response='
                + recaptcha
                , function (error, response, body) {
                    body = JSON.parse(body);
                    resolve(body.success);
                })
        });
    }

    async callingLeadCare(user: any, dto: any, actionName: string) {
        this.loggerService.log(this.context, 'Update leadCare: start calling');
        const leadCare = await this.queryRepository.findOne({ id: dto.id });
        if (!leadCare) {
            return;
        }
        dto.isCalled = true;
        return await this.executeCommand(Action.UPDATE, actionName, this.commandId, dto);
    }

    async completeLeadCare(dto: any, actionName: string) {
        this.loggerService.log(this.context, 'complete service');
        this.commandId = uuid.v4();
        return await this.executeCommand(Action.COMPLETE, actionName, this.commandId, dto);
    }

    async failLeadCare(user: any, dto: any, actionName: string) {
        this.loggerService.log(this.context, 'Fail service');
        dto.modifiedBy = user.name;
        this.commandId = uuid.v4();
        return await this.executeCommand(Action.FAIL, actionName, this.commandId, dto);
    }

    async processLeadCare(user: any, dto: any, actionName: string) {
        this.loggerService.log(this.context, 'Process service');
        const isValidMarkProcessing = await this.queryRepository.isValidMarkProcessing(user.id, dto.id);
        if (!isNullOrUndefined(isValidMarkProcessing) && isValidMarkProcessing === false) {
            throw new NotFoundException({ errors: ErrorConst.CommonError(ErrorConst.INVALID_MARK_PROCESSING) });
        }

        dto.modifiedBy = user.name;
        this.commandId = uuid.v4();
        return await this.executeCommand(Action.PROCESS, actionName, this.commandId, dto);
    }

    async unprocessLeadCare(user: any, dto: any, actionName: string) {
        this.loggerService.log(this.context, 'Unprocess service');
        this.commandId = uuid.v4();
        dto.modifiedBy = user.name;
        return await this.executeCommand(Action.UNPROCESS, actionName, this.commandId, dto);
    }

    async assignLeadCare(user: any, dto: any, actionName: string, timezoneclient: string) {
        this.loggerService.log(this.context, 'assign service');
        const leadCare = await this.validateLeadCareToAssign(dto.id);
        await this.checkEmployeeExist(dto.assignFor);
        dto.timeOut = await this.calTimeOutToProcess(leadCare, timezoneclient);
        dto.processBy = dto.assignFor;
        dto.lifeCycleStatus = LifeCycleStatusEnum.ASSIGNED;
        dto.status = leadCare.status;
        dto.timezoneclient = timezoneclient;
        dto.modifiedBy = user.name;
        dto.assignedDate = Date.now(),
            this.commandId = uuid.v4();
        return await this.executeCommand(Action.ASSIGN, actionName, this.commandId, dto);
    }

    async customerClosedTicketLeadCare(user: any, dto: any, actionName: string, timezoneclient: string) {        
        this.loggerService.log(this.context, 'customer closed ticket service');
        dto.lifeCycleStatus = LifeCycleStatusEnum.CUSTOMER_CLOSED_TICKET;
        let leadCare = await this.getLeadCare(dto.id);
        // Validate value of cutomerId and current userId
        if(!user ||!leadCare|| user.id !==leadCare.customerId) 
            throw new BadRequestException("Yêu cầu không hợp lệ.");
        dto.exploitHistory= await this.updateExploitHistory(leadCare.exploitHistory,ExploitCareEnum.CUSTOMER_CLOSED_TICKET,'');
        dto.timezoneclient = timezoneclient;
        dto.status=ExploitCareEnum.CUSTOMER_CLOSED_TICKET;
        dto.exploitStatus=ExploitCareEnum.CUSTOMER_CLOSED_TICKET;        
            this.commandId = uuid.v4();
        return await this.executeCommand(Action.CLOSED_TICKET, actionName, this.commandId, dto);        
    }

    async customerCancelTicketLeadCare(user: any, dto: any, actionName: string, timezoneclient: string) {
        this.loggerService.log(this.context, 'customer cancel ticket service');
        dto.lifeCycleStatus = LifeCycleStatusEnum.CUSTOMER_CLOSED_TICKET;
        let leadCare = await this.getLeadCare(dto.id);
        // Validate value of cutomerId and current userId
        if(!user ||!leadCare|| user.id !==leadCare.customerId)
            throw new BadRequestException("Yêu cầu không hợp lệ.");
        dto.exploitHistory= await this.updateExploitHistory(leadCare.exploitHistory,ExploitCareEnum.CANCEL,'');
        dto.timezoneclient = timezoneclient;
        dto.status=ExploitCareEnum.CANCEL;
        dto.exploitStatus=ExploitCareEnum.CANCEL;
        this.commandId = uuid.v4();
        // send notification to client
        this.notificationClient.createNotificationCare(
          "care_serviceRequestCancelByCustomer",
          null,
          leadCare.customerId,
          CommonConst.AGGREGATES.LEAD_CARE.NAME,
          leadCare.id,
          {
              code: leadCare.code
          }
        );
        return await this.executeCommand(Action.CLOSED_TICKET, actionName, this.commandId, dto);
    }

    async systemClosedTicketLeadCare(actionName: string) {
        
        this.loggerService.log(this.context, 'system closed ticket service');
        const leadCares = await this.queryRepository.findMany({
            exploitStatus: ExploitCareEnum.DONE,
            updatedDate: {
              $lt: new Date(),
            }
        }, {}, {}, 1000);
        let result;
        leadCares.forEach(async leadCare => {            
            let currentHistory = leadCare.exploitHistory;
            
            //currentHistory = currentHistory.sort((a,b) => (a.updatedAt > b.updatedAt) ? 1 : ((b.updatedAt > a.updatedAt) ? -1 : 0));
            if(currentHistory[currentHistory.length-1].status===ExploitCareEnum.DONE)
            {
                // get updateAt 
                let updatedAt = currentHistory[currentHistory.length-1].updatedAt;
                if(updatedAt)
                {
                    updatedAt.setDate(updatedAt.getDate() + 3);
                    let checkDate = new Date()>=updatedAt?true:false;
                    if(checkDate)
                    {
                        leadCare.exploitHistory=  this.updateExploitHistory(leadCare.exploitHistory,ExploitCareEnum.SYSTEM_CLOSED_TICKET,'');
                        leadCare.lifeCycleStatus = LifeCycleStatusEnum.SYSTEM_CLOSED_TICKET;
                        leadCare.status=ExploitCareEnum.SYSTEM_CLOSED_TICKET;
                        leadCare.exploitStatus=ExploitCareEnum.SYSTEM_CLOSED_TICKET;
                        this.commandId = uuid.v4();
                        await this.executeCommand(actionName, '', this.commandId, leadCare);
                    }
                }
            }
           
        });        
    }

    private updateExploitHistory(
        history: IExploitHistory[] = [],
        status: ExploitCareEnum,
        takeCareId?: string
    ) {
        const newHistory: IExploitHistory = {
          status, updatedAt: new Date(), updatedBy: takeCareId, takeCareId
        };
        if (history.length > 500) {
          history = history.slice(history.length - 500);
        }
        history.push(newHistory);
        return history;
    }

    async pullLeadCare(user: any, actionName: string, timezoneclient: string, type?: string) {
        this.loggerService.log(this.context, 'pull service');
        const employee = await this.validateEmployeeBeforePull(user.id, type);
        let sharedPool = false;
        if (employee.pos.taxNumber === '3602545493') { // Hard code DXS Tax No to get leadCare from shared pool
            sharedPool = true;
        }
        const leadCareReadyToAssign = await this.queryRepository.findLeadCaresToAssign(employee.id, employee.pos.id, employee.pos.parentId, type, sharedPool);
        if (isNullOrUndefined(leadCareReadyToAssign) || leadCareReadyToAssign.length <= 0 ) {
            throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.HAVE_NOT_ANY_LEAD) });
        }

        const tpull = timezone().tz(timezoneclient);
        const leadCaresPullLatest = [];
        for (const leadCare of leadCareReadyToAssign) {
            leadCaresPullLatest.push(leadCare.id);
            const model = {
                id: leadCare.id,
                // timeOut: await this.calTimeOutToProcess(leadCare, timezoneclient),
                timeOut: this.createTimeout(timezoneclient),
                processBy: employee.id,
                lifeCycleStatus: LifeCycleStatusEnum.ASSIGNED,
                status: leadCare.status,
                timezoneclient,
                modifiedBy: user.name,
                assignedDate: Date.now(),
            };
            // Start countdown
            // const runTime = Number(ConfigTimeConst.TICKET_TIME_OUT) * 60000;
            // setTimeout(() => this.timeoutLeadCare(leadCare.id), runTime);
            this.commandId = uuid.v4();
            await this.executeCommand(Action.ASSIGN, actionName, this.commandId, model);
        }
        this.employeeRepository.update(
            {
                id: user.id,
                timePullLatest: tpull.format('DD-MM-YYYY, h:mm:ss a').toString(),
                leadCaresPullLatest
            }
        );
        return true;
    }

    /**
     * Chuyển ticket về pool chung (ticket quá hạn)
     * @param id
     */
    // async timeoutLeadCare(id: string) {
    //     const lead = await this.queryRepository.findOne({ id });
    //     if (!lead) {
    //         return;
    //     }
    //     const timezoneclient = lead.timezoneclient;
    //     const timeOut = timezone(lead.timeOut).tz(timezoneclient);
    //     const now = timezone().tz(timezoneclient);

    //     // if (lead.lifeCycleStatus === LifeCycleStatusEnum.PROCESSING) {
    //     //     timeOut.add(ConfigTimeConst.TICKET_HOLDING_TIME, 'minutes');
    //     // }

    //     if (timeOut <= now) {
    //         // flow mới update 20/2/2020
    //         // Yêu cầu bị timeout thì đặt lifeCycleStatus là inpool, để NV khác pull ticket về
    //         // không quan tâm tới bị timeout mấy lần
    //         if (lead.lifeCycleStatus !== LifeCycleStatusEnum.PENDING) {
    //             const history: ILeadProcessed[] = lead.processedHistory || [];
    //             history.push({
    //                 id: uuid.v4(),
    //                 processedDate: new Date(),
    //                 processBy: lead.processBy,
    //                 isReject: false,
    //                 isTimeOut: true,
    //             } as ILeadProcessed);
    //             this.commandId = uuid.v4();
    //             const model: any = {
    //                 id: lead.id,
    //                 lifeCycleStatus: LifeCycleStatusEnum.IN_POOL,
    //                 modifiedBy: lead.modifiedBy,
    //                 processBy: null,
    //                 processedHistory: history
    //             };
    //             await this.executeCommand(Action.EXPIRED, 'background action', this.commandId, model);
    //         }
    //     }
        // }

    async pendingLeadCare(dto: any, actionName: string, user?: any) {
        this.loggerService.log(this.context, 'pending service');

        // dto.modifiedBy = user.name
        this.commandId = uuid.v4();
        return await this.executeCommand(Action.PENDING, actionName, this.commandId, dto);
    }

    async getEvents(leadCareId: string) {
        this.loggerService.log(this.context, 'Get all history event');
        return await this.eventStreamRepository.findAllEventStreamById(leadCareId);
    }

    async updateExploitStatusAssign(dto: any, actionName: string, user?:any){
        this.loggerService.log(this.context, 'Update Exploit Status leadCare');
        this.commandId = uuid.v4();
        const usrId = user.id

        const getUser = await this.employeeClient.sendDataPromise(dto.takeCare,CmdPatternConst.SERVICE.EMPLOYEE.EMPLOYEE_GET_BY_ID)
        if(isEmpty(getUser)){
            throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.USER_TAKE_CARE_NOT_FOUND, 'LeadCare') });
        }
        const leadCareInf: any = await this.queryRepository.findLeadCareById(dto.id);
        if (!!leadCareInf){
          const { exploitHistory } = leadCareInf;
          exploitHistory.push({
            status: ExploitCareEnum.ASSIGN,
            updatedAt: new Date(),
            updatedBy: usrId,
            takeCareId: usrId,
          });

          leadCareInf.takeCare = {
              id: dto.takeCare.id,
              name: dto.takeCare.name,
              phone:dto.takeCare.phone,
              email:dto.takeCare.email,
              code: dto.takeCare.code,
              role: getUser.role,
          }
          leadCareInf.exploitHistory = exploitHistory;
          leadCareInf.exploitStatus = ExploitCareEnum.ASSIGN;
          leadCareInf.status = ExploitCareEnum.ASSIGN;
          leadCareInf.assignedDate = new Date();
          leadCareInf.note = dto.note;
          leadCareInf.dateEndWork = dto.dateEndWork;
          if (leadCareInf.leadJob) {
            const leadCareUpdate: any = {
              id: leadCareInf.leadJob.id,
              _id: leadCareInf.leadJob.id,
              takeCare: leadCareInf.takeCare,
              note: leadCareInf.note,
              assignedDate: leadCareInf.assignedDate,
              dateEndWork: leadCareInf.dateEndWork
            }

            await this.leadJobService.update(user, leadCareUpdate);
            // send notification to app bql
            this.notificationClient.createNotificationBQL({
              eventName: "bql_assignWork_job",
              belongTo: dto.takeCare.id,
              entityId: dto.id,
              entityName: "lead-cares",
              extraData: {
                id: dto.id,
                code: leadCareInf.code,
              }
            });
          } else {
            const leadJob = await this.leadJobService.create(user, leadCareInf, "create", false);
            leadCareInf.leadJob = leadJob;
            // send notification to app bql
            this.notificationClient.createNotificationBQL({
              eventName: "bql_assignWork_job",
              belongTo: dto.takeCare.id,
              entityId: dto.id,
              entityName: "lead-cares",
              extraData: {
                id: dto.id,
                code: leadJob.code,
              }
            });

            this.notificationClient.createNotificationBQL({
              eventName: "bql_assignWork",
              belongTo: user.id,
              entityId: dto.id,
              entityName: "lead-cares",
              extraData: {
                id: dto.id,
                code: leadCareInf.code,
              }
            });
          }
         return this.executeCommand(Action.CHANGE_STATUS, actionName, this.commandId, leadCareInf);
    }
        throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, 'LeadCare') });
    }

    async updateLeadCare(dto: UpdateLeadCareDto, actionName: string, usrId: string){
        this.loggerService.log(this.context, 'Update LeadCare info');
        this.commandId = uuid.v4();
        const leadCareInf = await this.queryRepository.findLeadCareById(dto.id);
        Object.assign(leadCareInf, {...dto, modifiedBy: usrId});
        if (!!leadCareInf){
            return this.executeCommand(Action.UPDATE, actionName, this.commandId, leadCareInf);
        }
        throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, 'LeadCare') });
    }


    private async executeCommand(
        action: string,
        actionName: string,
        commandId: string,
        item: any
    ) {

        let commandObject = null;
        switch (action) {
            case Action.CREATE:
                commandObject = new CreateLeadCareCommand(actionName, commandId, item);
                break;
            case Action.CHANGE_STATUS:
                commandObject = new ChangeStatusLeadCareCommand(actionName, commandId, item);
                break;
            case Action.UPDATE:
                commandObject = new UpdateLeadCareCommand(actionName, commandId, item);
                break;
            case Action.REASSIGN:
                commandObject = new ReassignLeadCareCommand(actionName, commandId, item);
                break;
            case Action.COMPLETE:
                commandObject = new CompleteLeadCareCommand(actionName, commandId, item);
                break;
            case Action.FAIL:
                commandObject = new FailLeadCareCommand(actionName, commandId, item);
                break;
            case Action.PROCESS:
                commandObject = new ProcessLeadCareCommand(actionName, commandId, item);
                break;
            case Action.UNPROCESS:
                commandObject = new UnprocessLeadCareCommand(actionName, commandId, item);
                break;
            case Action.ASSIGN:
                commandObject = new AssignLeadCareCommand(actionName, commandId, item);
                break;
            case Action.PENDING:
                commandObject = new PendingLeadCareCommand(actionName, commandId, item);
                break;
            case Action.EXPIRED:
                commandObject = new ExpiredLeadCareCommand(actionName, commandId, item);
                break;
            case Action.CLOSED_TICKET:
                commandObject = new CustomerClosedTicketCareCommand(actionName, commandId, item);
                break;
            default:
                break;
        }

        return await this.commandBus.execute(commandObject)
            .then((response) => {
                return response;
            }).catch((error) => {
                return error;
            });
    }

    private calTimeOutToProcess(leadCare: any, timezoneclient: string) {
        const ta = timezone().tz(timezoneclient);
        const startWorkingTime = ta.clone().set({ hour: ConfigTimeConst.START_WORKING_HOURS, minute: 0, second: 0, millisecond: 0 });
        const endWorkingTime = ta.clone().set({ hour: ConfigTimeConst.END_WORKING_HOURS, minute: 0, second: 0, millisecond: 0 });

        let timeOut;
        const t2 = timezone(leadCare.t2).tz(timezoneclient);
        if (t2 > ta) {
            return leadCare.t3;
        } else {
            timeOut = ta.add(ConfigTimeConst.TICKET_RED_TIME, 'minutes');
            if (timeOut.isAfter(endWorkingTime)) {
                startWorkingTime.add(1, 'days');
                const diff = timeOut.diff(startWorkingTime, 'minutes');
                timeOut = startWorkingTime.add(diff, 'minutes');
            }
        }
        return timeOut.format();
    }

    private createTimeout(timezoneclient: string) {
        const ta = timezone().tz(timezoneclient);
        const timeOut = ta.add(ConfigTimeConst.TICKET_TIME_OUT, 'minutes');
        return timeOut.format();
    }

    private async validateLeadCareToAssign(leadCareId: string) {
        const leadCare = await this.queryRepository.findOne({ id: leadCareId });
        if (isNullOrUndefined(leadCare)) {
            throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, 'ticket') });
        } else if (leadCare.processBy != null) {
            throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.LEAD_ASSIGNED) });
        }
        return leadCare;
    }

    private async getLeadCare(leadCareId: string) {
        const leadCare = await this.queryRepository.findOne({ id: leadCareId });                
        const newHistory: IExploitHistory = {
            status:ExploitCareEnum.CUSTOMER_CLOSED_TICKET, updatedAt: new Date(), updatedBy: "", takeCareId:""
        };        
        if (isNullOrUndefined(leadCare)) {
            throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, 'ticket') });
        }       
        return leadCare;
    }

    private async checkEmployeeExist(employeeId: string) {
        const employee = await this.employeeRepository.findOne({ id: employeeId });
        if (isNullOrUndefined(employee)) {
            throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, 'employee') });
        }
        return employee;
    }

    private async validateEmployeeBeforePull(employeeId: string, type: string) {
        const employee = await this.employeeRepository.findOne({ id: employeeId });
        if (isNullOrUndefined(employee)) {
            throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, 'employee') });
        }

        // Không check isPenalty nữa - O2OWADMIN-619
        // if (employee.isPenalty && employee.isPenalty === true) {
        //     throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.EMPLOYEE_PENALTY) });
        // }

        if (isNullOrUndefined(employee.pos) || isNullOrUndefined(employee.pos.id)) {
            throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.EMPLOYEE_NOT_ASSIGN_POS) });
        }


        const isValidBeforePull = await this.queryRepository.isValidBeforePull(employeeId, type);
        if (isValidBeforePull === false) {
            throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.EMPLOYEE_NOT_PROCESSED_YET) });
        }

        return employee;
    }

    async updateStatusLeadCare() {
        const all = await this.queryRepository.findAll();

        if (all.length === 0) {
            return;
        }

        const yellow = [];
        const red = [];
        // let warning = [];
        await all.forEach(leadCare => {
            const timezoneclient = leadCare.timezoneclient;
            const now = timezone().tz(timezoneclient);
            const t1 = timezone(leadCare.t1).tz(timezoneclient);
            const t2 = timezone(leadCare.t2).tz(timezoneclient);
            if (t1 <= now && now < t2) {
                yellow.push(leadCare.id);
            } else if (t2 <= now) {
                red.push(leadCare.id);
                // if(leadCare.processBy) warning.push(leadCare);
            }
        });

        if (yellow.length > 0) {
            await this.queryRepository.updateStatus({ ids: yellow, status: StatusEnum.YELLOW });
        }

        if (red.length > 0) {
            await this.queryRepository.updateStatus({ ids: red, status: StatusEnum.RED });
        }

        // warning.forEach(item => {
        //     const msg = {
        //         title: `Ticket sắp hết hạn khai thác.`,
        //         content: `Ticket của bạn sắp hết hạn xử lý. Hãy nhanh khai thác nếu không sẽ bị không thể tiếp tục xử lý`,
        //         entityName: 'leadCare',
        //         entityId: item.id,
        //         eventName: 'leadCareWarning'
        //     }
        //     const sender = {};
        //     const receivers = [item.processBy];
        //     this.notifyService.subscribe(CmdPatternConst.SERVICE.NOTIFICATION.NOTIFY, {msg, sender, receivers});
        // });
    }

    async timeout() {
        const leadCareProcess = await this.queryRepository.findLeadCaresAssigned();
        if (leadCareProcess.length === 0) {
            return;
        }
        for (const leadCare of leadCareProcess) {
            const timezoneclient = leadCare.timezoneclient;
            const timeOut = timezone(leadCare.timeOut).tz(timezoneclient);
            const now = timezone().tz(timezoneclient);

            if (timeOut <= now) {
                // flow mới update 20/2/2020
                // Yêu cầu bị timeout thì đặt lifeCycleStatus là inpool, để NV khác pull ticket về
                // không quan tâm tới bị timeout mấy lần
                if (leadCare.lifeCycleStatus !== LifeCycleStatusEnum.PENDING) {
                    const history: ILeadCareProcessed[] = leadCare.processedHistory || [];
                    history.push({
                        id: uuid.v4(),
                        processedDate: new Date(),
                        processBy: leadCare.processBy,
                        isReject: false,
                        isTimeOut: true,
                    } as ILeadCareProcessed);
                    this.commandId = uuid.v4();
                    const model: any = {
                        id: leadCare.id,
                        lifeCycleStatus: LifeCycleStatusEnum.IN_POOL,
                        modifiedBy: leadCare.modifiedBy,
                        processBy: null,
                        processedHistory: history
                    };
                    await this.executeCommand(Action.EXPIRED, 'background action', this.commandId, model);
                }
            }
        }
    }
    async importDemandTicket(files: any , dto: ImportLeadCareDemandDto, actionName: string, timezoneclient?: string, userId?: string){
        const history = [];
        let sanList = [{
            id: 'dxs-shared',
            name: 'Các sàn DXS',
            code: 'DXS-Virtual',
            parentId: '',
            type: 'SAN'
        }];
        if (CommonUtils.stringNotEmpty(dto.exchangeId)) {
            // console.log(11111111111, dto.exchangeId);
            sanList = await this.orgchartClient.sendDataPromise({posId: dto.exchangeId}, CmdPatternConst.LISTENER.GET_POS_BY_QUERY);
        }
        // console.log(JSON.stringify(sanList));
        const workbook = XLSX.read(files[0].buffer, { type: 'buffer' });
        const sheet_name_list = workbook.SheetNames;
        const leadCareList: any = await XLSX.utils.sheet_to_json(workbook.Sheets[sheet_name_list[0]], { range: 1 });
        let employee: any = await this.employeeRepository.findOne({ id: userId });
        if (isNullOrUndefined(employee)) {
            employee = {
                id: userId
            };
        } else {
            employee = {
                id: employee.id,
                name: employee.name,
                code: employee.code,
                email: employee.email
            };
        }

        for (const [i, leadCare] of leadCareList.entries()) {
            leadCare.pos = {
                id: sanList[0].id,
                name: sanList[0].name,
                parentId: sanList[0].parentId,
                code: sanList[0].code,
                type: sanList[0].type
            };
            leadCare.phone = leadCare.phone ? leadCare.phone.toString() : '';
            leadCare.email = leadCare.email ? leadCare.email.toString().toLowerCase() : '';
            leadCare.type = CommonConst.TYPE.PRIMARY;
            leadCare.timestamp = dto.timestamp;
            leadCare.source = dto.resource;
            leadCare.customer = {
                personalInfo: {
                    name: leadCare.name,
                    phone: leadCare.phone,
                    email: leadCare.email,
                    identities: [
                        {
                            type: 'CMND',
                            value: leadCare.identity
                        }
                    ]
                }
            };
            leadCare.property = {
                code: leadCare.propertyCode,
                project: { id: leadCare.projectId, name: leadCare.projectName }
            };
            leadCare.createdDate = +leadCare.createdDate;
            leadCare.importedBy = employee || {};
            const validate = this.validateLeadCare(leadCare);
            if (validate) {
                history.push({
                    line: i + 1,
                    error: validate
                });
            } else {
                await this.createLeadCareImport(leadCare, actionName, timezoneclient);
            }
        }
        const readModel = {
            fileName: files[0].originalname,
            processBy: employee || {},
            success: leadCareList.length - history.length,
            fail: history.length,
            type: CommonConst.TYPE.PRIMARY,
            createdDate: new Date(),
            updatedDate: new Date(),
            description: history
        };
        await this.historyImportService.create(readModel);
    }
    private async createLeadCareImport(leadCare , actionName , timezoneclient): Promise<any> {
        return await this.createLeadCare(leadCare, actionName, timezoneclient);
    }
    private validateLeadCare(leadCare) {
        if(isNullOrUndefined(leadCare.phone) || !leadCare.phone.toString().match(CommonConst.REGEX_VN_PHONE)){
            return 'Số điện thoại sai';
        }
        if(leadCare.email && !leadCare.email.toString().match(CommonConst.REGEX_EMAIL)){
            return 'Email sai';
        }
        return '';
    }

    async importLeadCareFromExcel(files: any, options: ImportLeadCareAsExcelDto, actionName: string, timezoneclient: string, user: any) {
        const workbook = XLSX.readCare(files[0].buffer, { type: 'buffer' });
        const sheets = workbook.SheetNames;
        let leadCares = await XLSX.utils.sheet_to_json(workbook.Sheets[sheets[0]], { range: 1 });

        leadCares = leadCares.map(leadCare => {
            return {
                ...leadCare,
                repoId: options.repoId,
                repoConfigCode: options.repoConfigCode,
                importedBy: {
                    id: user.id,
                    name: user.name,
                },
            };
        });

        for (const leadCare of leadCares) {
            await this.createLeadCareImport(leadCare, actionName, timezoneclient);
        }
    }

    async addAnswerConversation(user, dto) {
        const transfer = await this.queryRepository.findOne({id: dto.id, customerId: user.id})
        if (!transfer) {
            throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, 'Yêu cầu chuyển nhượng') });
        }
        const conversation = {
            user: {
                id: user.id,
                name: user.name
            },
            status: ConversationEnum.ANSWER,
            message: dto.conversation.message,
            files: dto.conversation.files,
            createdDate: new Date()
        }
        let updateQuery = {
            $set : {exploitStatus: ExploitCareEnum.ADD_ANSWER, updatedDate: new Date(), modifiedBy: user.id},
            $push: {conversations: conversation}
        };
        await this.queryRepository.updateByQuery({id: dto.id}, updateQuery);
        return {success: true};
    }

    async HandleTransferRequest(user, dto, actionName) {
        const request = await this.queryRepository.findOne({id: dto.id, repoType: LeadRepoCareEnum.TRANSFER})
        if (!request) {
            throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, 'LeadCare') });
        }
        let updateQuery = {};
        let payload = {};
        switch (dto.type) {
            // Đang xử lý
            case ExploitCareEnum.PROCESSING:
                if (request.exploitStatus === ExploitCareEnum.DONE || request.exploitStatus === ExploitCareEnum.CANCEL) {
                    throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.STATUS_NOT_INVALID) });
                }
                updateQuery = {$set : {exploitStatus: ExploitCareEnum.PROCESSING, note: dto.note, updatedDate: new Date(), modifiedBy: user.id}};
                payload = {
                    id: '',
                    entityName: 'notificationManual',
                    eventName: 'notificationManual',
                    listUserId: [request.customerId],
                    extraData: {
                        title: 'Yêu cầu chuyển nhượng đang được xử lý',
                        content: `Yêu cầu ${request.code} của bạn đang được xử lý`
                    }
                };
                this.notificationClient.createNotificationMultipleCare(payload);
                break;
            // Hoàn thành
            case ExploitCareEnum.DONE:
                if (request.exploitStatus !== ExploitCareEnum.PROCESSING && request.exploitStatus !== ExploitCareEnum.ADD_QUESTION && request.exploitStatus !== ExploitCareEnum.ADD_ANSWER) {
                    throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.STATUS_NOT_INVALID) });
                }
                updateQuery = {$set : {exploitStatus: ExploitCareEnum.DONE, note: dto.note, updatedDate: new Date(), modifiedBy: user.id}};

                payload = {
                    id: '',
                    entityName: 'notificationManual',
                    eventName: 'notificationManual',
                    listUserId: [request.customerId],
                    extraData: {
                        title: 'Yêu cầu chuyển nhượng đã hoàn thành',
                        content: `Yêu cầu ${request.code} của bạn đã hoàn thành`
                    }
                };
                this.notificationClient.createNotificationMultipleCare(payload);
                break;
            // Trả về
            case ExploitCareEnum.CANCEL:
                if (request.exploitStatus !== ExploitCareEnum.PROCESSING && request.exploitStatus !== ExploitCareEnum.ADD_QUESTION && request.exploitStatus !== ExploitCareEnum.ADD_ANSWER) {
                    throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.STATUS_NOT_INVALID) });
                }
                updateQuery = {$set : {exploitStatus: ExploitCareEnum.CANCEL, note: dto.note, reason: dto.reason, updatedDate: new Date(), modifiedBy: user.id}};
                payload = {
                    id: '',
                    entityName: 'notificationManual',
                    eventName: 'notificationManual',
                    listUserId: [request.customerId],
                    extraData: {
                        title: 'Yêu cầu chuyển nhượng đã bị trả về',
                        content: `Yêu cầu ${request.code} của bạn đã bị trả về. Lý do: ${dto.reason}`
                    }
                };
                this.notificationClient.createNotificationMultipleCare(payload);
                break;
            case 'update': {
                if (request.exploitStatus !== ExploitCareEnum.PROCESSING && request.exploitStatus !== ExploitCareEnum.ADD_QUESTION && request.exploitStatus !== ExploitCareEnum.ADD_ANSWER) {
                    throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.STATUS_NOT_INVALID) });
                }
                if (dto.conversation && dto.conversation.message) {
                    const conversation = {
                        user: {
                            id: user.id,
                            name: user.name
                        },
                        status: ConversationEnum.QUESTION,
                        message: dto.conversation.message,
                        files: dto.conversation.files,
                        createdDate: new Date()
                    }
                    updateQuery = {
                        $set : {exploitStatus: ExploitCareEnum.ADD_QUESTION, note: dto.note, updatedDate: new Date(), modifiedBy: user.id},
                        $push: {conversations: conversation}
                    };

                    // Gửi thông báo yêu cầu bổ sung
                    payload = {
                        id: '',
                        entityName: 'transfer_conversation',
                        eventName: 'transfer_conversation',
                        listUserId: [request.customerId],
                        extraData: {
                            id: request.id,
                            title: 'Yêu cầu bổ sung thông tin',
                            content: dto.conversation.message
                        }
                    };
                    this.notificationClient.createNotificationMultipleCare(payload);
                } else {
                    updateQuery = {$set : {note: dto.note, updatedDate: new Date(), modifiedBy: user.id}};
                }
                break;
            }
            default: {
                throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, 'Type') });
            }
        }
        await this.queryRepository.updateByQuery({id: dto.id}, updateQuery);
        return { success: true };
    }

    async updateExploitStatus(dto: any, actionName: string, usrId:any){
        this.loggerService.log(this.context, 'Update Exploit Status leadCare');
        this.commandId = uuid.v4();
        const leadCareInf: any = await this.queryRepository.findLeadCareById(dto.id);
        if (!!leadCareInf){
          const {repoType, exploitHistory, customerId, code, processedHistory} = leadCareInf;
          exploitHistory.push({
            status: dto.status as ExploitCareEnum,
            updatedAt: new Date(),
            updatedBy: usrId,
            takeCareId: usrId,
          });

          const exploitStatus = dto.status
          Object.assign(dto, { exploitHistory});
        if(dto.status === ExploitCareEnum.CANCEL) {
            if(leadCareInf.customData && leadCareInf.customData.contractId) {
              switch (leadCareInf.repoType) {
                case  LeadRepoCareEnum.TRANSFER:
                  this.primaryContractClient.sendData({
                    id: leadCareInf.customData.contractId,
                    status: false
                  }, CmdPatternConst.PRIMARY_CONTRACT.PRIMARY_CONTRACT_UPDATE_TRANSFER_CONFIRM);
                  break;
                case  LeadRepoCareEnum.PROJECT:
                  this.primaryContractClient.sendData({
                    id: leadCareInf.customData.contractId,
                    status: false
                  }, CmdPatternConst.PRIMARY_CONTRACT.PRIMARY_CONTRACT_UPDATE_DEPOSIT_CONFIRM);
                  break;
                default:
                  break;
              }
            }
            if(leadCareInf.customerId) {
                // Thông báo cho khách hàng
                this.notificationClient.createNotificationCare(
                  "care_serviceRequestCancelByBql",
                  null,
                  leadCareInf.customerId,
                  CommonConst.AGGREGATES.LEAD_CARE.NAME,
                  leadCareInf.id,
                  {
                      code: leadCareInf.code,
                      id: leadCareInf.id,
                      causeReject: dto.causeReject
                  }
                );
            }
        } else if (dto.status === ExploitCareEnum.SURVEY) {
            if (!leadCareInf.canSurvey) {
                throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.LEAD_CARE_CAN_NOT_SURVEY) });
            }
            dto.isRequireSurvey = true;
            if(leadCareInf.customerId) {
                // Thông báo cho khách hàng
                this.notificationClient.createNotificationCare(
                  "care_serviceRequestSurveyByBql",
                  null,
                  leadCareInf.customerId,
                  CommonConst.AGGREGATES.LEAD_CARE.NAME,
                  leadCareInf.id,
                  {
                      code: leadCareInf.code,
                      id: leadCareInf.id,
                  }
                );
            }
        }
          return this.executeCommand(Action.CHANGE_STATUS, actionName, this.commandId, {...dto, repoType, usrId, customerId, code, processedHistory ,exploitStatus });
    }
        throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, 'LeadCare') });
    }

    async updateRateInfoLeadCareById(rateData: any) {
        let leadCare: any = await this.queryRepository.findOne({ id: rateData.leadCareId });
        leadCare.rateValue = rateData.rateValue;
        leadCare.rateDescription = rateData.rateDescription;

        this.commandId = uuid.v4();
        return await this.executeCommand(Action.UPDATE, '', this.commandId, leadCare);
    }

    async takeSurvey(id, surveys) {
        let leadCare: any = await this.queryRepository.findOne({ id });
        if (!leadCare || !leadCare.canSurvey) {
            throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, 'LeadCare') });
        }
        leadCare.surveyAnswers = surveys;
        leadCare.submitSurvey = true;

        // Thông báo cho ban quản lý
        const bqls = await this.propertyClient.sendDataPromise(leadCare.project.id,CmdPatternConst.PROJECT.GET_ACCOUNT_BQL_MEMBER)
        for (let bql of bqls) {
            this.notificationClient.createNotificationBQL({
                eventName: "bql_serviceRequestSurveyByCustomer",
                belongTo: bql.id,
                entityId: leadCare.id,
                entityName: CommonConst.AGGREGATES.LEAD_CARE.NAME,
                extraData: {
                    customerName: leadCare.name,
                    id: leadCare.id,
                }
            });
        }

        this.commandId = uuid.v4();
        return await this.executeCommand(Action.UPDATE, '', this.commandId, leadCare);
    }
}
