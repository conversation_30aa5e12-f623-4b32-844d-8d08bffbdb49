import { HttpModule, Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { CodeGenerateModule } from '../code-generate/module';
import { DomainDatabaseModule } from '../database/domain/domain.database.module';
import { LeadRepoCareQueryModule } from '../leadRepoCare.queryside/module';
import { LoggerModule } from '../logger/logger.module';
import { MgsSenderModule } from '../mgs-sender/mgs-sender.module';
import { LeadRepoCareDomainCommandHandlers } from './commands/handlers';
import { LeadRepoCareDomainController } from './controller';
import { LeadRepoCareEventStreamHandler } from './events/domain.eventHandler';
import { LeadRepoCareDomainProvider } from './providers/domain.provider';
import { LeadRepoCareDomainRepository } from './repositories/domain.repository';
import { LeadRepoCareDomainSagas } from './sagas/domain.sagas';
import { LeadRepoCareDomainService } from './service';

@Module({
    imports: [
        CqrsModule,
        DomainDatabaseModule,
        LeadRepoCareQueryModule,
        MgsSenderModule,
        LoggerModule,
        HttpModule,
        LeadRepoCareQueryModule,
        CodeGenerateModule,
    ],
    exports: [LeadRepoCareDomainService],
    controllers: [LeadRepoCareDomainController],
    providers: [
        LeadRepoCareDomainService,
        LeadRepoCareDomainRepository,
        LeadRepoCareDomainSagas,
        ...LeadRepoCareDomainProvider,
        ...LeadRepoCareDomainCommandHandlers,
        LeadRepoCareEventStreamHandler,
    ],
})
export class LeadRepoCareDomainModule {}
