
import { Injectable } from '@nestjs/common';
import { HistoryImportQueryRepository } from './repository/query.repository';

@Injectable()
export class HistoryImportService {

  private commandId: string;
  constructor(
    private readonly queryRepository: HistoryImportQueryRepository,
  ) { }

  async create(readmodel: any) {
    this.queryRepository.create(readmodel);
  }

  async listAll(user, query: any = {}): Promise<any> {
    return await this.queryRepository.findAll(user, query);
  }
}
