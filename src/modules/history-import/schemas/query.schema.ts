import * as mongoose from 'mongoose';
import uuid = require('uuid');

export const QuerySchema = new mongoose.Schema({
    _id: { type: String },
    id: { type: String, default: uuid.v4, index: true },
    fileName: { type: String },
    processBy: { type: Object },
    type: { type: String },
    createdDate: { type: Date },
    updatedDate: { type: Date },
    description: { type: Object },
    success: { type: Number },
    fail: { type: Number },
    failedFileUrl: { type: String },
    failedFileName: { type: String },
    failedFilePath: { type: String },
    status: { type: String },
});

QuerySchema.pre('save', function (next) {
    this._id = this.get('id');
    next();
});
