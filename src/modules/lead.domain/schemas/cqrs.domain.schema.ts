import * as mongoose from 'mongoose';
import { StatusEnum } from '../../shared/enum/status.enum';
import { LifeCycleStatusEnum } from '../../shared/enum/life-cycle-status.enum';
import { ExploitEnum } from '../../shared/enum/exploit.enum';

const uuid = require('uuid');
const beautifyUnique = require('mongoose-beautiful-unique-validation');

const categorySchema = new mongoose.Schema({
    id: { type: String },
    name: { type: String },
});
const addressSchema = new mongoose.Schema({
    nation: { type: String },
    province: { type: String },
    district: { type: String },
    ward: { type: String },
});

const identifySchema = new mongoose.Schema({
    type: { type: String },
    num: { type: String },
    date: { type: String },
    issueBy: { type: String },
});

const surveySchema = new mongoose.Schema({
    name: { type: String },
    value: { type: String },
    code: { type: String },
    type: { type: String },
});

export const ExploitHistorySchema = new mongoose.Schema(
  {
      status: { type: String },
      updatedAt: { type: Date },
      updatedBy: { type: String },
      takeCareId: {type: String},
  },
  { _id: false }
);

export const TakeCareSchema = new mongoose.Schema({
  id: { type: String },
  name: { type: String },
  email: { type: String },
  phone: { type: String },
}, {_id: false});

export const payloadSchema = new mongoose.Schema({
    _id: { type: String, default: uuid.v4 },
    id: { type: String, index: true },
    email: { type: String },
    name: { type: String },
    profileUrl: { type: String },
    address: { type: String },
    phone: { type: String },
    pos: { type: Object },
    type: { type: String },
    status: { type: String, default: StatusEnum.GREEN },
    lifeCycleStatus: { type: String, default: LifeCycleStatusEnum.IN_POOL },
    processBy: { type: String, default: null },
    t0: { type: Date },
    t1: { type: Date },
    t2: { type: Date },
    t3: { type: Date },
    timeOut: { type: Date, default: null },
    timezoneclient: { type: String },
    reason: { type: [String] },
    notes: { type: Object },

    timestamp: { type: Number },
    customerId: { type: String },

    description: { type: String, default: '' },
    active: { type: Boolean, default: true },
    softDelete: { type: Boolean, default: false },
    modifiedBy: { type: String },

    createdDate: { type: Date, default: () => Date.now() },
    updatedDate: { type: Date, default: () => Date.now() },
    eventName: { type: String },
    actionName: { type: String },
    revision: { type: Number, index: true },
    source: { type: String },
    code: { type: String },
    customer: { type: Object },
    employee: { type: Object },
    property: { type: Object },
    processedDate: { type: Date, default: () => Date.now() },
    assignedDate: { type: Date },
    processedHistory: { type: Array },
    isCalled: { type: Boolean, default: false },
    advisingType: { type: String },
    price: { type: Number },
    categoryId: { type: String },
    desirablePrice: { type: String },
    category: { type: categorySchema },
    usedFloorArea: { type: Number },

    updatedName: { type: String },
    updatedPhone: { type: String },
    updatedEmail: { type: String },
    updatedProfileUrl: { type: String },

    isInNeed: { type: String },
    reasonNoNeed: { type: String },
    otherReason: { type: String },
    interestedProduct: { type: Object },
    direction: { type: Object },
    needLoan: { type: Boolean, default: false },
    isAppointment: { type: Boolean, default: false },
    isVisited: { type: Boolean, default: false },
    note: { type: String },
    callHistory: { type: Array },
    importedBy: { type: Object },

    sex: { type: String },
    subPhone: { type: [Array], default: []},
    objAddress: {type: addressSchema, default: {}},
    needPersons: { type: String },
    identification: { type: identifySchema },
    incomePerMonth: { type: Number, default: 0},
    sourceIncome: { type: String },
    dob: { type: String },
    major: { type: String },
    maritalStatus: {type: String },
    surveys: {type: [Object], default: []},
    orgCode: { type: String },
    orgName: { type: String },


    // Lead repository feature ==========================================

    exploitStatus: { type: String, default: ExploitEnum.NEW },
    exploitStatusModifiedBy: { type: Object, default: null },
    exploitHistory: [ExploitHistorySchema],
    // assignedHistory: [AssignHistorySchema],
    // takeCareId: { type: String },
    takeCare: TakeCareSchema,
    assignDuration: { type: Number },

    repoId: { type: String },
    repoConfigCode: { type: String },
    isHot: { type: Boolean, default: false },
    expireTime: {type: Date},
    countAssign: { type: Number, default: 0 },
    notiUser: {type: Object},
});



payloadSchema.pre('save', function (next) {
    this._id = this.get('id');
    next();
});

// Enable beautifying on this schema
payloadSchema.plugin(beautifyUnique);
// ============

export const CqrsDomainSchema = new mongoose.Schema({
    _id: { type: String, },
    id: { type: String, index: true },
    streamId: { type: String, index: true },
    aggregate: String,
    aggregateId: String,
    context: String,
    streamRevision: Number,
    commitId: String,
    commitSequence: Number,
    commitStamp: { type: Date, default: () => Date.now() },
    eventName: { type: String },
    payload: payloadSchema,
    payloads: [payloadSchema],
});

CqrsDomainSchema.pre('save', function (next) {
    this._id = this.get('id');
    const payload = this.get('payload');
    if (payload) {
      payload.processedDate = this.get('commitStamp');
    }
    next();
});
