import { EventPublisher, <PERSON><PERSON>om<PERSON><PERSON><PERSON><PERSON>, CommandHandler } from '@nestjs/cqrs';
import { EventStreamRepository } from '../../repository/event-stream.repository';
import { AggregateModel } from '../../models/aggregate.model';
import { BaseEventStream } from '../../../shared/eventStream/models/base-event-stream.model';
import { isNullOrUndefined } from 'util';
import { PendingLeadCommand } from '../impl/pending.cmd';
import { LeadQueryRepository } from '../../../lead.queryside/repository/query.repository';
import { LifeCycleStatusEnum } from '../../../shared/enum/life-cycle-status.enum';
import { CommonConst } from '../../../../modules/shared/constant/common.const';
import { MsxLoggerService } from '../../../../modules/logger/logger.service';

@CommandHandler(PendingLeadCommand)
export class Pending<PERSON>ommandHandler implements ICommandHandler<PendingLeadCommand> {
    private readonly context = PendingCommandHandler.name;
    private readonly aggregateName: string = CommonConst.AGGREGATE_NAME_LEAD;
    private readonly eventName: string = CommonConst.AGGREGATES.LEAD.PENDING;

    constructor(
        private readonly repository: EventStreamRepository,
        private readonly publisher: EventPublisher,
        private readonly queryRepository: LeadQueryRepository,
        private readonly loggerService: MsxLoggerService,
    ) { }

    // execute Cteate Lead Command
    async execute(command: PendingLeadCommand) {
        this.loggerService.log(this.context, `Async Create ${this.aggregateName} cmd ...`);

        const { messagePattern, id, commandModel } = command;
        const model: any = commandModel;
        const leadInfo = await this.queryRepository.findLeadById(model.id);
        const es = await this.repository.findEventStreamById(model.id);

        if (es && es.payload) {

            const payload: any = es.payload;
            // if existed object and have no new one
            // keep existing object
            if (payload) {

                model.id = es.streamId;
                // model.name = (model.name || payload.name);
                model.description = (model.description || payload.description);
                model.active = (model.active || payload.active);
                model.softDelete = (model.softDelete || payload.softDelete);
                model.modifiedBy = (model.modifiedBy || payload.modifiedBy);
            }
        }

        model.address = leadInfo.address;
        model.phone = leadInfo.phone;
        model.email = leadInfo.email;
        model.pos = leadInfo.pos;
        model.status = leadInfo.status;
        model.lifeCycleStatus = LifeCycleStatusEnum.PENDING;
        model.processBy = leadInfo.processBy;
        model.type = leadInfo.type;
        model.createdDate = leadInfo.createdDate;
        model.timezoneclient = leadInfo.timezoneclient;
        model.name = leadInfo.name;

        const eventStream = new BaseEventStream();
        eventStream.id = id;
        eventStream.aggregateId = id;

        if (isNullOrUndefined(model.id)) model.id = id;
        eventStream.streamId = model.id;

        eventStream.aggregate = this.aggregateName;
        eventStream.eventName = this.eventName;

        model.eventName = this.eventName;
        model.actionName = messagePattern;

        const aggregateModel = new AggregateModel(eventStream);

        // pulisher
        const cmdPublisher = this.publisher.mergeObjectContext(aggregateModel);
        const msg = `${this.aggregateName}`;
        cmdPublisher.addItem(msg, model);
        cmdPublisher.commit();
    }
}
