import { EventPublisher, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CommandHandler } from '@nestjs/cqrs';
import { EventStreamRepository } from '../../repository/event-stream.repository';
import { AggregateModel } from '../../models/aggregate.model';
import { BaseEventStream } from '../../../shared/eventStream/models/base-event-stream.model';
import { isNullOrUndefined } from 'util';
import { ProcessLeadCommand } from '../impl/process.cmd';
import { LeadQueryRepository } from '../../../lead.queryside/repository/query.repository';
import { CommonConst } from '../../../../modules/shared/constant/common.const';
import { MsxLoggerService } from '../../../../modules/logger/logger.service';

@CommandHandler(ProcessLeadCommand)
export class ProcessCommandHandler implements ICommandHandler<ProcessLeadCommand>{
    private readonly context = ProcessCommandHandler.name;
    private readonly aggregateName: string = CommonConst.AGGREGATE_NAME_LEAD;
    private readonly eventName: string = CommonConst.AGGREGATES.LEAD.PROCESSING;

    constructor(
        private readonly repository: EventStreamRepository,
        private readonly publisher: EventPublisher,
        private readonly queryRepository: LeadQueryRepository,
        private readonly loggerService: MsxLoggerService,
    ) { }

    // execute Cteate Lead Command
    async execute(command: ProcessLeadCommand) {
        this.loggerService.log(this.context, `Async Create ${this.aggregateName} cmd ...`);

        const { messagePattern, id, commandModel } = command;
        const model: any = commandModel;
        const es = await this.repository.findEventStreamById(model.id);
        
        if (es && es.payload) {

            const payload: any = es.payload;
            // if existed object and have no new one
            // keep existing object
            if (payload) {
                
                model.id = es.streamId;
                // model.name = (model.name || payload.name);
                model.description = (model.description || payload.description);
                model.active = (model.active || payload.active);
                model.softDelete = (model.softDelete || payload.softDelete);
                model.modifiedBy = (model.modifiedBy || payload.modifiedBy);
            }
        }

        const eventStream = new BaseEventStream();
        eventStream.id = id;
        eventStream.aggregateId = id;

        if (isNullOrUndefined(model.id)) model.id = id;
        eventStream.streamId = model.id;

        eventStream.aggregate = this.aggregateName;
        eventStream.eventName = this.eventName;

        model.eventName = this.eventName;
        model.actionName = messagePattern;

        const aggregateModel = new AggregateModel(eventStream);

        // pulisher
        const cmdPublisher = this.publisher.mergeObjectContext(aggregateModel);
        const msg = `${this.aggregateName}`;
        cmdPublisher.addItem(msg, model);
        cmdPublisher.commit();
    }
}
