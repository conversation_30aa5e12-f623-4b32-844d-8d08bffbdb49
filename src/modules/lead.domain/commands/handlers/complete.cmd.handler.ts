import { EventPublisher, <PERSON><PERSON><PERSON>mand<PERSON>and<PERSON>, CommandHandler } from '@nestjs/cqrs';
import { EventStreamRepository } from '../../repository/event-stream.repository';
import { AggregateModel } from '../../models/aggregate.model';
import { BaseEventStream } from '../../../shared/eventStream/models/base-event-stream.model';
import { isNullOrUndefined } from 'util';
import { CompleteLeadCommand } from '../impl/complete.cmd';
import { LeadQueryRepository } from '../../../lead.queryside/repository/query.repository';
import { LifeCycleStatusEnum } from '../../../shared/enum/life-cycle-status.enum';
import { EmployeeService } from '../../../employee/service';
import { CommonConst } from '../../../../modules/shared/constant/common.const';
import { MsxLoggerService } from '../../../../modules/logger/logger.service';

@CommandHandler(CompleteLeadCommand)
export class CompleteCommandHandler implements ICommandHandler<CompleteLeadCommand>{
    private readonly context = CompleteCommandHandler.name;
    private readonly aggregateName: string = CommonConst.AGGREGATE_NAME_LEAD;
    private readonly eventName: string = CommonConst.AGGREGATES.LEAD.COMPLETED;
    constructor(
        private readonly repository: EventStreamRepository,
        private readonly publisher: EventPublisher,
        private readonly queryRepository: LeadQueryRepository,
        private readonly employeeService: EmployeeService,
        private readonly loggerService: MsxLoggerService,
    ) { }

    // execute Cteate Lead Command
    async execute(command: CompleteLeadCommand) {
        this.loggerService.log(this.context, `Async Complete ${this.aggregateName} cmd ...`);

        const { messagePattern, id, commandModel } = command;
        const model: any = commandModel;
        const leadInfo = await this.queryRepository.findLeadById(model.id);
        const es = await this.repository.findEventStreamById(model.id);
        if (es && es.payload) {

            const payload: any = es.payload;
            // if existed object and have no new one
            // keep existing object
            if (payload) {
                model.id = es.streamId;
                model.name = (model.name || payload.name);
                model.description = (model.description || payload.description);
                model.active = (model.active || payload.active);
                model.softDelete = (model.softDelete || payload.softDelete);
                model.modifiedBy = (model.modifiedBy || payload.modifiedBy);
            }
        }

        model.code = leadInfo.code;
        model.address = leadInfo.address;
        model.phone = leadInfo.phone;
        model.email = leadInfo.email;
        model.pos = leadInfo.pos;
        model.status = leadInfo.status;
        model.source = leadInfo.source;
        model.lifeCycleStatus = LifeCycleStatusEnum.COMPLETED;
        model.processBy = leadInfo.processBy;
        model.type = leadInfo.type;
        model.createdDate = leadInfo.createdDate;
        model.updatedDate = leadInfo.updatedDate;
        model.timezoneclient = leadInfo.timezoneclient;
        model.notes = leadInfo.notes;
        model.name = leadInfo.name;
        model.customerId = leadInfo.customerId;
        model.assignedDate = leadInfo.assignedDate;

        const employee = await this.employeeService.getById(model.processBy);

        if (employee) {
            model.employee = employee;
        }

        // console.log('model => ', model);
        const eventStream = new BaseEventStream();
        eventStream.id = id;
        eventStream.aggregateId = id;

        if (isNullOrUndefined(model.id)) model.id = id;
        eventStream.streamId = model.id;

        eventStream.aggregate = this.aggregateName;
        eventStream.eventName = this.eventName;

        model.eventName = this.eventName;
        model.actionName = messagePattern;

        const aggregateModel = new AggregateModel(eventStream);

        // pulisher
        const cmdPublisher = this.publisher.mergeObjectContext(aggregateModel);
        const msg = `${this.aggregateName}`;
        cmdPublisher.addItem(msg, model);
        cmdPublisher.commit();
    }
}
