import {
  Controller,
  Post,
  Body,
  Headers,
  UseGuards,
  UseInterceptors,
  Get,
  Param,
  UploadedFiles,
  Put,
  ValidationPipe as ValidationPipeOfficial,
} from '@nestjs/common';
import { LoggingInterceptor } from '../../common/interceptors/logging.interceptor';
import { AssignLeadDto, RequestDto, RejectDto, CreateLeadDto, ImportLeadDemandDto, ImportLeadFromPublicForm, ImportLeadAsExcelDto, UpdateStatusDto, UpdateLeadDto, DeliverLeadDto, CreateLeadCommonDto, EditLeadDto, RevokeAssignDto } from './dto/lead.dto';
import { LeadDomainService } from './service';
import { Action } from '../shared/enum/action.enum';
import { ValidationPipe } from '../../common/pipes/validation.pipe';
import { ApiUseTags, ApiBearerAuth, ApiForbiddenResponse, ApiCreatedResponse } from '@nestjs/swagger';
import { PermissionConst } from '../shared/constant/permission.const';
import { FilesInterceptor } from '@nestjs/platform-express';
import { CommonConst } from '../shared/constant/common.const';
import { Public } from '../../common/decorators/public.decorator';
import { AuthClientKeyGuard } from '../../common/guards/auth.client-key.guard';
import { LeadDomainServiceExtends } from './service.extend';
import {
  JwtAuthGuard,
  RoleGuard,
  Roles,
  User
} from "../../../shared-modules";

@ApiBearerAuth()
@ApiUseTags('v1/lead')
@Controller('v1/lead')
@UseInterceptors(LoggingInterceptor)
@UseGuards(JwtAuthGuard)
export class LeadController {
  private resSuccess = { success: true };
  private actionName: string = Action.NOTIFY;

  constructor(
    private readonly leadService: LeadDomainService,
    private readonly leadServiceExt: LeadDomainServiceExtends,
  ) { }

  @UseGuards(RoleGuard)
  @Roles(PermissionConst.CREATE_LEAD)
  @Post('/common')
  createLeadCommon(
    @User() user,
    @Body() dto: CreateLeadCommonDto,
    @Headers('timezoneclient') timezoneclient?: string,
    @Headers('act') actionName?: string,
  ) {
    if (actionName) this.actionName = actionName;
    return this.leadServiceExt.createLeadCommon(dto, this.actionName, timezoneclient, user);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionConst.IMPORT_LEAD)
  @UseInterceptors(FilesInterceptor('files'))
  @Post('/import-from-excel')
  importLeadFromExcel(
    @UploadedFiles() files,
    @User() user,
    @Body() options: ImportLeadAsExcelDto,
    @Headers('timezoneclient') timezoneclient?: string,
    @Headers('act') actionName?: string,
  ) {
    if (actionName) this.actionName = actionName;
    return this.leadServiceExt.importLeadFromExcel(files, options, this.actionName, timezoneclient, user);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionConst.UPDATE_LEAD)
  @Put('/update-exploit-status')
  @ApiCreatedResponse({ description: 'The record has been successfully.' })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  async updateExploitStatus(
    @User() user,
    @Body() dto: UpdateStatusDto,
    @Headers('act') actionName?: string,
  ) {
    this.actionName = actionName || this.actionName;
    return (await this.leadService.updateExploitStatus(dto, this.actionName, user.id)) || this.resSuccess;
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionConst.PROCESS_LEAD)
  @Post('/process')
  @ApiCreatedResponse({ description: 'The record has been successfully processing.' })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  async processLead(
    @User() user,
    @Body() dto: RequestDto,
    @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    return await this.leadService.processLead(user, dto, this.actionName) || this.resSuccess;
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionConst.REJECT_LEAD)
  @Post('/reject')
  @ApiCreatedResponse({ description: 'The record has been successfully rejected.' })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  async rejectLead(
    @User() user,
    @Body() dto: RejectDto,
    @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    const res = await this.leadService.rejectLead(user, dto, this.actionName);
    return { success: res };
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionConst.UPDATE_LEAD)
  @Put()
  async update(@User() user, @Body() dto: EditLeadDto) {
    return await this.leadService.update(dto, user.id);
  }

  @Post()
  async createLead(
    @User() user,
    @Body(new ValidationPipeOfficial({ transform: true })) dto: CreateLeadDto, @Headers('timezoneclient') timezoneclient: string = 'Asia/Ho_Chi_Minh', @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    return await this.leadService.createLead(dto, this.actionName, timezoneclient, user.id);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionConst.IMPORT_LEAD)
  @UseInterceptors(FilesInterceptor('files'))
  @Post('/import-demand')
  async createDemandLead(
    @UploadedFiles() files,
    @User() user,
    @Body(new ValidationPipe()) dto: ImportLeadDemandDto, @Headers('timezoneclient') timezoneclient: string, @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    await this.leadService.importDemandTicket(files, dto, this.actionName, timezoneclient, user.id);
    return true;
  }
  @UseGuards(RoleGuard)
  @Roles(PermissionConst.IMPORT_LEAD)
  @Post('import')
  async importLead(
    @Body(new ValidationPipe()) dtos: CreateLeadDto[], @Headers('timezoneclient') timezoneclient: string, @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    for (const dto of dtos) {
      await this.leadService.createLead(dto, this.actionName, timezoneclient);
    }
    return true;
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionConst.PULL_LEAD)
  @Post('/pull')
  @ApiCreatedResponse({ description: 'The record has been successfully pulled.' })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  async pullLead(
    @User() user,
    @Headers('timezoneclient') timezoneclient: string,
    @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    const res = await this.leadService.pullLead(user, this.actionName, timezoneclient);
    return { success: res };
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionConst.PULL_LEAD)
  @Post('/pullPrimary')
  @ApiCreatedResponse({ description: 'The record has been successfully pulled.' })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  async pullPrimaryLead(
    @User() user,
    @Headers('timezoneclient') timezoneclient: string,
    @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    const res = await this.leadService.pullLead(user, this.actionName, timezoneclient, CommonConst.TYPE.PRIMARY);
    return { success: res };
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionConst.PULL_LEAD)
  @Post('/calling')
  @ApiCreatedResponse({ description: 'The record has been successfully updated.' })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  async callingLead(
    @User() user,
    @Body(new ValidationPipe()) dto: RequestDto,
    @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    const res = await this.leadService.callingLead(user, dto, this.actionName) || this.resSuccess;
    return res;
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionConst.REJECT_LEAD)
  @Post('/reject-all')
  @ApiCreatedResponse({ description: 'The record has been successfully rejected.' })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  async rejectAllLead(
    @User() user,
    @Body(new ValidationPipe()) dto: RejectDto,
    @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    const res = await this.leadService.rejectAllLead(user, dto, this.actionName);
    return { success: res };
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionConst.FAIL_LEAD)
  @Post('/fail')
  @ApiCreatedResponse({ description: 'The record has been successfully failed.' })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  async failLead(
    @User() user,
    @Body(new ValidationPipe()) dto: RequestDto,
    @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    return await this.leadService.failLead(user, dto, this.actionName) || this.resSuccess;
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionConst.ASSIGN_LEAD)
  @Post('/assign')
  @ApiCreatedResponse({ description: 'The record has been successfully assign.' })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  async assign(
    @User() user,
    @Body(new ValidationPipe()) dto: AssignLeadDto,
    @Headers('timezoneclient') timezoneclient: string,
    @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    return await this.leadService.assignLead(user, dto, this.actionName, timezoneclient) || this.resSuccess;
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionConst.UNPROCESS_LEAD)
  @Post('/unprocess')
  @ApiCreatedResponse({ description: 'The record has been successfully unprocessing.' })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  async unprocessLead(
    @User() user,
    @Body(new ValidationPipe()) dto: RequestDto,
    @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    return await this.leadService.unprocessLead(user, dto, this.actionName) || this.resSuccess;
  }

  @Get(':id/events')
  @ApiCreatedResponse({ description: 'Get all lead events stream.' })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  async getEvents(
    @User() user,
    @Param('id') id: string,
    @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    return await this.leadService.getEvents(id);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionConst.PENDING_LEAD)
  @Post('/pending')
  @ApiCreatedResponse({ description: 'The record has been successfully pending.' })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  async pendingLead(
    @User() user,
    @Body(new ValidationPipe()) dto: RequestDto,
    @Headers('act') actionName?: string) {
    this.actionName = (actionName || this.actionName);
    return await this.leadService.pendingLead(dto, this.actionName, user) || this.resSuccess;
  }

  @Public()
  @Post('/import-from-public-form')
  @UseGuards(AuthClientKeyGuard)
  importFromPublicForm(
    @Body(new ValidationPipeOfficial({ transform: true })) lead: ImportLeadFromPublicForm,
    @Headers('timezoneclient') timezoneClient: string,
    @Headers('act') actionName?: string,
    @Headers('client-id') clientId?: string,
    @Headers('secret-key') secretKey?: string,
    @Headers('lead_id') leadId?: string,
    @Headers('x-client-code') notiSystem?: string,
  ) {
    if (actionName) this.actionName = actionName;
    return this.leadService.createLead(lead, this.actionName, timezoneClient, null, clientId, secretKey, notiSystem);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionConst.UPDATE_LEAD)
  @Put('/update-lead')
  @ApiCreatedResponse({ description: 'The record has been successfully.' })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  async updateStatus(@User() user, @Body(new ValidationPipe()) dto: UpdateLeadDto, @Headers('act') actionName?: string) {
    this.actionName = actionName || this.actionName;
    return (await this.leadService.updateLead(dto, this.actionName, user.id)) || this.resSuccess;
  }


  @UseGuards(RoleGuard)
  @Roles(PermissionConst.LEAD_MANUAL_DELIVER)
  @Put('/deliver')
  async deliver(@User() user, @Body(new ValidationPipe()) dto: DeliverLeadDto[], @Headers('act') actionName?: string) {
    this.actionName = actionName || this.actionName;
    return this.leadServiceExt.deliverLeads(dto, user);
  }

  @UseGuards(RoleGuard)
  @Roles(PermissionConst.LEAD_MANUAL_DELIVER)
  @Put('/manual-deliver')
  async manualDeliver(@User() user, @Body(new ValidationPipe()) dto: DeliverLeadDto[], @Headers('act') actionName?: string) {
    this.actionName = actionName || this.actionName;
    return this.leadServiceExt.manualDeliver(dto, user);
  }
  
  @UseGuards(RoleGuard)
  @Roles(PermissionConst.LEAD_MANUAL_DELIVER)
  @Put('/revoke-assign')
  async revokeAssign(@User() user, @Body(new ValidationPipe()) dto: RevokeAssignDto, @Headers('act') actionName?: string) {
    this.actionName = actionName || this.actionName;
    return this.leadServiceExt.revokeAssign(dto, user);
  }
}
