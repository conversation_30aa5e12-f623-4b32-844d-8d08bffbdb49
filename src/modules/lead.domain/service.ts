import { Injectable, BadRequestException, NotFoundException, HttpService } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import {
  CreateLeadDto,
  EditLeadDto,
  ImportLeadAsExcelDto,
  ImportLeadDemandDto,
  ImportLeadFromPublicForm,
  UpdateLeadDto,
  UpdateStatusDto,
} from './dto/lead.dto';
import { CreateLeadCommand } from './commands/impl/create.cmd';
import { Action } from '../shared/enum/action.enum';
import { ProcessLeadCommand } from './commands/impl/process.cmd';
import { CompleteLeadCommand } from './commands/impl/complete.cmd';
import { FailLeadCommand } from './commands/impl/fail.cmd';
import { ChangeStatusLeadCommand } from './commands/impl/changeStatus.cmd';
import { UnprocessLeadCommand } from './commands/impl/unprocess.cmd';
import { LeadQueryRepository } from '../lead.queryside/repository/query.repository';
import { isNullOrUndefined } from 'util';
import { EmployeeQueryRepository } from '../employee/repository/query.repository';
import { ConfigTimeConst } from '../shared/constant/config-time.const';
import { AssignLeadCommand } from './commands/impl/assign.cmd';
import { LifeCycleStatusEnum } from '../shared/enum/life-cycle-status.enum';
import { ReassignLeadCommand } from './commands/impl/reassign.cmd';
import { PendingLeadCommand } from './commands/impl/pending.cmd';
import { StatusEnum } from '../shared/enum/status.enum';
import { EventStreamRepository } from './repository/event-stream.repository';
import { UpdateLeadCommand } from './commands/impl/update.cmd';
import { ExpiredLeadCommand } from './commands/impl/expired.cmd';
import { RawQueryRepository } from '../raw/repository/query.repository';
import { MsxLoggerService } from '../logger/logger.service';
import { ErrorConst } from '../shared/constant/error.const';
import { IExploitHistory, ILeadProcessed, ITakeCare } from '../shared/services/lead/interfaces/lead.interface';
import { map } from 'rxjs/operators';
import { OrgchartClient } from '../mgs-sender/orgchart.client';
import { CommonConst } from '../../modules/shared/constant/common.const';
import { CmdPatternConst } from '../../modules/shared/constant/cmd-pattern.const';
import { ConfigService } from '@nestjs/config';
import { CommonUtils } from '../shared/classes/class-utils';
import { CodeGenerateService } from '../code-generate/service';
import { HistoryImportService } from '../history-import/service';
const XLSX = require('xlsx');
import { LeadRepoQueryRepository } from '../leadRepo.queryside/repositories/query.repository';
import * as _ from 'lodash';
import { ExploitEnum } from '../shared/enum/exploit.enum';
import { AwesomeLogger, BaseService, ErrorService } from '../../../shared-modules';
import { EmployeeClient } from '../mgs-sender/employee.client';
import { CmdPatternConst as cmd2 } from '../../../shared-modules';

const uuid = require('uuid');
const timezone = require('moment-timezone');
const request = require('request');

@Injectable()
export class LeadDomainService extends BaseService {

  private readonly context = LeadDomainService.name;
  private commandId: string;
  private readonly logger = new AwesomeLogger(LeadDomainService.name);

  constructor(
    private readonly commandBus: CommandBus,
    private readonly queryRepository: LeadQueryRepository,
    private readonly employeeRepository: EmployeeQueryRepository,
    private readonly eventStreamRepository: EventStreamRepository,
    private readonly codeGenerateService: CodeGenerateService,
    private readonly configService: ConfigService,
    private readonly orgchartClient: OrgchartClient,
    private readonly employeeClient: EmployeeClient,
    private readonly historyImportService: HistoryImportService,
    private readonly leadRepoRepository: LeadRepoQueryRepository,
    public readonly errorService: ErrorService
  ) {
    super(errorService);
  }

  async rejectLead(user: any, dto: any, actionName: string) {
    this.logger.info(this.context, 'Reject service');
    const lead = await this.queryRepository.findLeadById(dto.id);
    if (!lead) {
      throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.HAVE_NOT_ANY_LEAD) });
    }

    // Check ticket
    if (lead.lifeCycleStatus !== LifeCycleStatusEnum.PENDING) {
      const history: ILeadProcessed[] = lead.processedHistory || [];
      history.push({
        id: uuid.v4(),
        processedDate: new Date(),
        processBy: user.id,
        isReject: true,
        isTimeOut: false,
        causeReject: dto.causeReject
      } as ILeadProcessed);
      const model: any = {
        id: lead.id,
        customerId: lead.customerId,
        modifiedBy: user.email,
        // processBy: null,
        // timeOut: null,
        lifeCycleStatus: LifeCycleStatusEnum.REMOVED,
        reason: dto.causeReject,
        processedHistory: history
      };
      // Get DVKH POS
      // let pos = null;
      // try {
      //     const request = {
      //         model: null,
      //         action: CommonConst.AGGREGATES.LEAD.REJECTED
      //     };
      //     pos = await this.orgchartClient.sendDataPromise(request, CmdPatternConst.SERVICE.LEAD.LISTENER);
      //     console.log('pos : ', pos);

      // } catch (err) {
      //     this.logger.info(this.context, '[Error] Cannot find POS DVKH', err);
      // }
      // flow mới update 20/2/2020
      // Yêu cầu bị trả về thì đặt lifeCycleStatus là removed. 
      // và gán vào pos DVKH 
      // không quan tâm tới trả về mấy lần
      // follow mới update 29/06/2020
      // Yêu cầu trả về vẫn đặt ở pos đó, status là removed
      // model.pos = {
      //     id: pos.id,
      //     name: pos.name,
      //     code: pos.code
      // };
      this.commandId = uuid.v4();
      await this.executeCommand(Action.FAIL, actionName, this.commandId, model)
    }
    // this.employeeRepository.penalty({ id: user.id });
    return true;
  }

  async rejectAllLead(user: any, dto: any, actionName: string) {
    this.logger.info(this.context, 'Reject service');
    const leads = await this.queryRepository.findLeadsAssignedEmployee(user.id);
    if (!leads || leads.length === 0) {
      throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.HAVE_NOT_ANY_LEAD) });
    }

    // Check ticket
    for (const lead of leads) {
      if (lead.lifeCycleStatus !== LifeCycleStatusEnum.PENDING) {
        const history: ILeadProcessed[] = lead.processedHistory || [];
        history.push({
          id: uuid.v4(),
          processedDate: new Date(),
          processBy: user.id,
          isReject: true,
          isTimeOut: false,
          causeReject: dto.causeReject
        } as ILeadProcessed);
        this.commandId = uuid.v4();
        const model: any = {
          id: lead.id,
          customerId: lead.customerId,
          // processBy: null,
          // timeOut: null,
          modifiedBy: user.email,
          lifeCycleStatus: LifeCycleStatusEnum.REMOVED,
          reason: dto.causeReject[0],
          processedHistory: history
        };
        // Get DVKH POS
        // let pos = null;
        // try {
        //     const request = {
        //         model: null,
        //         action: CommonConst.AGGREGATES.LEAD.REJECTED
        //     };
        //     pos = await this.orgchartClient.sendDataPromise(request, CmdPatternConst.SERVICE.LEAD.LISTENER);
        // } catch (err) {
        //     this.logger.info(this.context, '[Error] Cannot find POS DVKH', err);
        // }
        // flow mới update 20/2/2020
        // Yêu cầu bị trả về thì đặt lifeCycleStatus là removed. 
        // và gán vào pos DVKH 
        // không quan tâm tới trả về mấy lần
        // follow mới update 29/06/2020
        // Yêu cầu trả về vẫn đặt ở pos đó, status là removed
        // model.pos = {
        //     id: pos.id,
        //     name: pos.name,
        //     code: pos.code
        // };
        this.commandId = uuid.v4();
        await this.executeCommand(Action.FAIL, actionName, this.commandId, model);
      }
    }
    // this.employeeRepository.penalty({ id: user.id });
    return true;
  }

  async createLead(dto: CreateLeadDto | ImportLeadFromPublicForm, actionName: string, timezoneclient?: string, userId?: string, clientId?: string, secretKey?: string, notiSystem?) {
    // if (!CommonUtils.stringNotEmpty(dto.recaptcha)) {
    //     throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.INVALID_FIELD, 'recaptcha') });
    // }
    // if (dto.recaptcha) {
    //     let checkRecaptcha = await this.validateRecaptcha(dto.recaptcha);
    //     if (!checkRecaptcha === true) {
    //         throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.INVALID_FIELD, 'recaptcha') });
    //     }
    // }

    const additionalData: any = {};
    additionalData.userId = userId;

    if (!isNullOrUndefined(userId)) {
      const employee: any = await this.employeeRepository.findOne({ id: userId });
      if (!isNullOrUndefined(employee) && !isNullOrUndefined(employee.pos) && dto instanceof CreateLeadDto) {
        dto.pos = {
          id: employee.pos?.id,
          name: employee.pos?.name,
          code: employee.pos?.code,
          type: employee.pos?.type,
          parentId: employee.pos?.parentId,
          address: employee.pos?.personalInfo?.address || ''
        };

        dto.timeOut = this.createTimeout(timezoneclient);
        dto.timezoneclient = timezoneclient;
        dto.processBy = userId;
        dto.lifeCycleStatus = dto.lifeCycleStatus || LifeCycleStatusEnum.ASSIGNED;
        dto.createdBy = userId;
        dto.modifiedBy = userId;
        dto.employee = {
          id: employee.id,
          name: employee.name,
          code: employee.code,
          email: employee.email
        };

      }
    }

    this.logger.info(this.context, 'create service');

    // Check lead is hot or not.
    if (dto instanceof ImportLeadFromPublicForm && !(dto.isHot || dto.repoConfigCode)) throw new BadRequestException();
    if (dto instanceof ImportLeadFromPublicForm && dto.isHot && dto.repoConfigCode) delete dto.repoConfigCode;
    // Check lead repository is available.
    if (dto instanceof ImportLeadFromPublicForm) {
      const repo = await this.leadRepoRepository.findById(dto.repoId);

      if (!repo) throw new BadRequestException();

      if (dto.repoConfigCode && !_.find(repo.configs, (config) => config.code === dto.repoConfigCode)) throw new BadRequestException();

      dto.importedBy = {
        clientId,
        id: repo?.modifiedBy,
      };
    }

    this.commandId = uuid.v4();

    if (isNullOrUndefined(dto.id) || dto.id.trim().length === 0) {
      const now = timezone.tz(timezoneclient);
      // const rawData = await this.rawRepository.findOne({ id: dto.rawId });

      // if (isNullOrUndefined(rawData)) {
      //     throw new NotFoundException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, 'raw') });
      // }

      // dto.phone = rawData.phone;
      // dto.email = rawData.email;
      // dto.customerId = rawData.customerId;
      // this.rawRepository.delete(rawData);

      const startWorkingTime = now.clone().set({ hour: ConfigTimeConst.START_WORKING_HOURS, minute: 0, second: 0, millisecond: 0 });
      const endWorkingTime = now.clone().set({ hour: ConfigTimeConst.END_WORKING_HOURS, minute: 0, second: 0, millisecond: 0 });

      let t0;
      let t1;
      let t2;
      let t3;
      if (!dto.createdDate) {
        dto.createdDate = now.format();
      }

      if (now.isBefore(startWorkingTime)) {
        t0 = startWorkingTime;
      } else if (now.isBefore(endWorkingTime)) {
        t0 = now;
      } else {
        startWorkingTime.add(1, 'days');
        endWorkingTime.add(1, 'days');
        t0 = startWorkingTime;
      }
      dto.t0 = t0.format();

      t1 = t0.add(ConfigTimeConst.TICKET_GREEN_TIME, 'minutes');
      if (t1.isAfter(endWorkingTime)) {
        startWorkingTime.add(1, 'days');
        t1 = startWorkingTime.clone().add(t1.diff(endWorkingTime, 'minutes'), 'minutes');
        endWorkingTime.add(1, 'days');
      }
      dto.t1 = t1.format();

      t2 = t1.add(ConfigTimeConst.TICKET_YELLOW_TIME, 'minutes');
      if (t2.isAfter(endWorkingTime)) {
        startWorkingTime.add(1, 'days');
        t2 = startWorkingTime.clone().add(t2.diff(endWorkingTime, 'minutes'), 'minutes');
        endWorkingTime.add(1, 'days');
      }
      dto.t2 = t2.format();

      t3 = t2.add(ConfigTimeConst.TICKET_RED_TIME, 'minutes');
      if (t3.isAfter(endWorkingTime)) {
        startWorkingTime.add(1, 'days');
        t3 = startWorkingTime.clone().add(t3.diff(endWorkingTime, 'minutes'), 'minutes');
      }
      dto.t3 = t3.format();

      dto.timezoneclient = timezoneclient;

      dto.processedDate = now.format();
      //add code
      dto.code = await this.codeGenerateService.generateCode(CommonConst.LEAD_PREFIX);
      const exploitHistory = this.updateExploitHistory(
        dto.exploitHistory,
        ExploitEnum.NEW
      );
      Object.assign(dto, { exploitHistory });
      if (notiSystem) {
        dto.notiUser = { notiSystem }
      }
      return await this.executeCommand(Action.CREATE, actionName, this.commandId, dto);
    } else {
      const lead = await this.queryRepository.findOne({ id: dto.id });
      if (
        dto.type === CommonConst.TYPE.PRIMARY &&
        !(dto as CreateLeadDto).skipCallHistory
      ) {
        const newProcess = {
          isInNeed: dto.isInNeed,
          reasonNoNeed: dto.reasonNoNeed,
          otherReason: dto.otherReason,
          interestedProduct: dto.interestedProduct,
          direction: dto.direction,
          needLoan: dto.needLoan,
          isAppointment: dto.isAppointment,
          isVisited: dto.isVisited,
          note: dto.note,
          isCalled: dto.isCalled,
          callId: dto.isCalled ? dto.callId : '',
          startCall: dto.isCalled ? dto.startCall : null,
          endCall: dto.isCalled ? dto.endCall : null,
          answerTime: dto.isCalled ? dto.answerTime : 0,

          updateDate: new Date(),
        };
        if (lead && lead.callHistory && lead.callHistory.length > 0) {
          dto.callHistory = lead.callHistory;
          dto.callHistory.push(newProcess);
        } else {
          dto.callHistory = [newProcess];
        }
      }

      // Get notification from lead repo config
      if (lead.repoId && lead.repoConfigCode) {
        const repo = await this.leadRepoRepository.findById(lead.repoId);

        let config = null;
        if (lead.isHot) config = repo.configHot;
        if (!lead.isHot) config = repo.configs.find(c => c.code === lead.repoConfigCode);

        additionalData.config = config;
      }
      if (lead.phone) {
        dto.phone = lead.phone;
      }
    }

    if (notiSystem) {
      dto.notiUser = { notiSystem }
    }

    return await this.executeCommand(Action.UPDATE, actionName, this.commandId, dto, additionalData);
  }

  async createLeadAdvising(dto: CreateLeadDto, actionName: string, timezoneclient?: string) {
    // Check recapcha
    if (dto.recaptcha) {
      const checkRecaptcha = await this.validateRecaptcha(dto.recaptcha);
      if (!checkRecaptcha === true) {
        throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.INVALID_FIELD, 'recaptcha') });
      }
    }
    if (!dto.advisingType) {
      throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.INVALID_FIELD, 'advisingType') });
    }
    this.logger.info(this.context, 'create service');
    this.commandId = uuid.v4();
    // Create/Update lead
    dto.type = CommonConst.TYPE.ADVISING;
    if (isNullOrUndefined(dto.id) || dto.id.trim().length === 0) {
      const now = timezone().tz(timezoneclient);
      const startWorkingTime = now.clone().set({ hour: ConfigTimeConst.START_WORKING_HOURS, minute: 0, second: 0, millisecond: 0 });
      const endWorkingTime = now.clone().set({ hour: ConfigTimeConst.END_WORKING_HOURS, minute: 0, second: 0, millisecond: 0 });

      let t0: any, t1: any, t2: any, t3: any;
      dto.createdDate = now.format();

      if (now.isBefore(startWorkingTime)) {
        t0 = startWorkingTime;
      } else if (now.isBefore(endWorkingTime)) {
        t0 = now;
      } else {
        startWorkingTime.add(1, 'days');
        endWorkingTime.add(1, 'days');
        t0 = startWorkingTime;
      }
      dto.t0 = t0.format();
      t1 = t0.add(ConfigTimeConst.TICKET_GREEN_TIME, 'minutes');
      if (t1.isAfter(endWorkingTime)) {
        startWorkingTime.add(1, 'days');
        t1 = startWorkingTime.clone().add(t1.diff(endWorkingTime, 'minutes'), 'minutes');
        endWorkingTime.add(1, 'days');
      }
      dto.t1 = t1.format();
      t2 = t1.add(ConfigTimeConst.TICKET_YELLOW_TIME, 'minutes');
      if (t2.isAfter(endWorkingTime)) {
        startWorkingTime.add(1, 'days');
        t2 = startWorkingTime.clone().add(t2.diff(endWorkingTime, 'minutes'), 'minutes');
        endWorkingTime.add(1, 'days');
      }
      dto.t2 = t2.format();
      t3 = t2.add(ConfigTimeConst.TICKET_RED_TIME, 'minutes');
      if (t3.isAfter(endWorkingTime)) {
        startWorkingTime.add(1, 'days');
        t3 = startWorkingTime.clone().add(t3.diff(endWorkingTime, 'minutes'), 'minutes');
      }
      dto.t3 = t3.format();
      dto.timezoneclient = timezoneclient;
      dto.processedDate = now.format();
      // Add code
      dto.code = await this.codeGenerateService.generateCode(CommonConst.LEAD_ADVISING_PREFIX);
      return await this.executeCommand(Action.CREATE, actionName, this.commandId, dto);
    }
    return await this.executeCommand(Action.UPDATE, actionName, this.commandId, dto);
  }

  async validateRecaptcha(recaptcha) {
    return new Promise(async (resolve, reject) => {
      await request(this.configService.get('URL_CAPTCHA') + '?secret='
        + this.configService.get('SECRET_KEY_CAPTCHA') + '&response='
        + recaptcha
        , function (error, response, body) {
          body = JSON.parse(body);
          resolve(body.success);
        })
    });
  }

  async callingLead(user: any, dto: any, actionName: string) {
    this.logger.info(this.context, 'Update lead: start calling');
    const lead = await this.queryRepository.findOne({ id: dto.id });
    if (!lead) {
      return;
    }
    dto.isCalled = true;
    return await this.executeCommand(Action.UPDATE, actionName, this.commandId, dto);
  }

  async completeLead(dto: any, actionName: string) {
    this.logger.info(this.context, 'complete service');
    this.commandId = uuid.v4();
    return await this.executeCommand(Action.COMPLETE, actionName, this.commandId, dto);
  }

  async failLead(user: any, dto: any, actionName: string) {
    this.logger.info(this.context, 'Fail service');
    dto.modifiedBy = user.name;
    this.commandId = uuid.v4();
    return await this.executeCommand(Action.FAIL, actionName, this.commandId, dto);
  }

  async processLead(user: any, dto: any, actionName: string) {
    this.logger.info(this.context, 'Process service');
    const isValidMarkProcessing = await this.queryRepository.isValidMarkProcessing(user.id, dto.id);
    if (!isNullOrUndefined(isValidMarkProcessing) && isValidMarkProcessing === false) {
      throw new NotFoundException({ errors: ErrorConst.CommonError(ErrorConst.INVALID_MARK_PROCESSING) });
    }

    dto.modifiedBy = user.id;
    this.commandId = uuid.v4();
    return await this.executeCommand(Action.PROCESS, actionName, this.commandId, dto);
  }

  async unprocessLead(user: any, dto: any, actionName: string) {
    this.logger.info(this.context, 'Unprocess service');
    this.commandId = uuid.v4();
    dto.modifiedBy = user.name;
    return await this.executeCommand(Action.UNPROCESS, actionName, this.commandId, dto);
  }

  async assignLead(user: any, dto: any, actionName: string, timezoneclient: string) {
    this.logger.info(this.context, 'assign service');
    const lead = await this.validateLeadToAssign(dto.id);
    await this.checkEmployeeExist(dto.assignFor);
    dto.timeOut = await this.calTimeOutToProcess(lead, timezoneclient);
    dto.processBy = dto.assignFor;
    dto.lifeCycleStatus = LifeCycleStatusEnum.ASSIGNED;
    dto.status = lead.status;
    dto.timezoneclient = timezoneclient;
    dto.modifiedBy = user.name;
    dto.assignedDate = Date.now(),
      this.commandId = uuid.v4();
    return await this.executeCommand(Action.ASSIGN, actionName, this.commandId, dto);
  }

  async pullLead(user: any, actionName: string, timezoneclient: string, type?: string) {
    this.logger.info(this.context, 'pull service');
    const employee = await this.validateEmployeeBeforePull(user.id, type);
    let sharedPool = false;
    if (employee.pos.taxNumber === '3602545493') { // Hard code DXS Tax No to get lead from shared pool
      sharedPool = true;
    }
    const leadReadyToAssign = await this.queryRepository.findLeadsToAssign(employee.id, employee.pos.id, employee.pos.parentId, type, sharedPool);
    if (isNullOrUndefined(leadReadyToAssign) || leadReadyToAssign.length <= 0) {
      throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.HAVE_NOT_ANY_LEAD) });
    }

    const tpull = timezone().tz(timezoneclient);
    const leadsPullLatest = [];
    for (const lead of leadReadyToAssign) {
      leadsPullLatest.push(lead.id);
      const model = {
        id: lead.id,
        // timeOut: await this.calTimeOutToProcess(lead, timezoneclient),
        timeOut: this.createTimeout(timezoneclient),
        // processBy: employee.id,
        processBy: user.id,
        lifeCycleStatus: LifeCycleStatusEnum.ASSIGNED,
        status: lead.status,
        timezoneclient,
        modifiedBy: user.name,
        assignedDate: Date.now(),
      };
      // Start countdown
      // const runTime = Number(ConfigTimeConst.TICKET_TIME_OUT) * 60000;
      // setTimeout(() => this.timeoutLead(lead.id), runTime);
      this.commandId = uuid.v4();
      await this.executeCommand(Action.ASSIGN, actionName, this.commandId, model);
    }
    this.employeeRepository.update(
      {
        id: user.id,
        timePullLatest: tpull.format('DD-MM-YYYY, h:mm:ss a').toString(),
        leadsPullLatest
      }
    );
    return true;
  }

  /**
   * Chuyển ticket về pool chung (ticket quá hạn)
   * @param id
   */
  // async timeoutLead(id: string) {
  //     const lead = await this.queryRepository.findOne({ id });
  //     if (!lead) {
  //         return;
  //     }
  //     const timezoneclient = lead.timezoneclient;
  //     const timeOut = timezone(lead.timeOut).tz(timezoneclient);
  //     const now = timezone().tz(timezoneclient);

  //     // if (lead.lifeCycleStatus === LifeCycleStatusEnum.PROCESSING) {
  //     //     timeOut.add(ConfigTimeConst.TICKET_HOLDING_TIME, 'minutes');
  //     // }

  //     if (timeOut <= now) {
  //         // flow mới update 20/2/2020
  //         // Yêu cầu bị timeout thì đặt lifeCycleStatus là inpool, để NV khác pull ticket về
  //         // không quan tâm tới bị timeout mấy lần
  //         if (lead.lifeCycleStatus !== LifeCycleStatusEnum.PENDING) {
  //             const history: ILeadProcessed[] = lead.processedHistory || [];
  //             history.push({
  //                 id: uuid.v4(),
  //                 processedDate: new Date(),
  //                 processBy: lead.processBy,
  //                 isReject: false,
  //                 isTimeOut: true,
  //             } as ILeadProcessed);
  //             this.commandId = uuid.v4();
  //             const model: any = {
  //                 id: lead.id,
  //                 lifeCycleStatus: LifeCycleStatusEnum.IN_POOL,
  //                 modifiedBy: lead.modifiedBy,
  //                 processBy: null,
  //                 processedHistory: history
  //             };
  //             await this.executeCommand(Action.EXPIRED, 'background action', this.commandId, model);
  //         }
  //     }
  // }

  async pendingLead(dto: any, actionName: string, user?: any) {
    this.logger.info(this.context, 'pending service');

    // dto.modifiedBy = user.name
    this.commandId = uuid.v4();
    return await this.executeCommand(Action.PENDING, actionName, this.commandId, dto);
  }

  async getEvents(leadId: string) {
    this.logger.info(this.context, 'Get all history event');
    return await this.eventStreamRepository.findAllEventStreamById(leadId);
  }

  async updateExploitStatus(dto: UpdateStatusDto, actionName: string, usrId: string) {
    this.logger.info(this.context, 'Update Exploit Status lead');
    this.commandId = uuid.v4();
    const leadInf = await this.queryRepository.findLeadById(dto.id);
    if (!!leadInf) {
      const { exploitHistory } = leadInf;
      exploitHistory.push({
        status: dto.status as ExploitEnum,
        updatedAt: new Date(),
        updatedBy: usrId,
        takeCareId: usrId,
      });

      if (dto.assignDuration !== -1 || dto.status !== ExploitEnum.PROCESSING) {
        delete dto.assignDuration;
      }

      Object.assign(dto, {
        exploitHistory,
        exploitStatus: dto.status,
        updatedDate: new Date(),
        modifiedBy: usrId,
      });
      return this.executeCommand(Action.CHANGE_STATUS, actionName, this.commandId, { ...dto, usrId });
    }
    throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, 'Lead') });
  }

  async update(dto: EditLeadDto, usrId: string) {
    this.logger.info(this.context, 'Edit Lead info');
    this.commandId = uuid.v4();

    const leadInf = await this.queryRepository.findLeadById(dto.id);
    const employees = await this.employeeClient.sendDataPromise({ where: { 'account.id': usrId, active: true }, projection: { _id: 0, id: 1, pos: 1, orgCode: 1 } }, cmd2.EMPLOYEE.LISTENER.GET_BY_QUERY);
    Object.assign(leadInf, { ...dto, updatedDate: new Date(), modifiedBy: usrId });

    if (!!leadInf) {
      this.logger.debug(usrId);
      this.logger.debug(employees?.[0]?.id, leadInf.takeCare?.id);
      if (employees?.[0]?.id == leadInf.takeCare?.id) {
        await this.queryRepository.update(leadInf);
        return this.getResponse(0);
      }
      return this.getResponse('LEADE0003');
    }
    return this.getResponse('LEADE0002');
  }

  async updateLead(dto: UpdateLeadDto, actionName: string, usrId: string) {
    this.logger.info(this.context, 'Update Lead info');
    this.commandId = uuid.v4();
    const leadInf = await this.queryRepository.findLeadById(dto.id);
    Object.assign(leadInf, { ...dto, updatedDate: new Date(), modifiedBy: usrId });
    if (!!leadInf) {
      return this.executeCommand(Action.UPDATE, actionName, this.commandId, leadInf);
    }
    throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, 'Lead') });
  }

  private async executeCommand(
    action: string,
    actionName: string,
    commandId: string,
    item: any,
    additionalData?: any,
  ) {

    let commandObject = null;
    switch (action) {
      case Action.CREATE:
        commandObject = new CreateLeadCommand(actionName, commandId, item);
        break;
      case Action.CHANGE_STATUS:
        commandObject = new ChangeStatusLeadCommand(actionName, commandId, item);
        break;
      case Action.UPDATE:
        commandObject = new UpdateLeadCommand(actionName, commandId, item, additionalData);
        break;
      case Action.REASSIGN:
        commandObject = new ReassignLeadCommand(actionName, commandId, item);
        break;
      case Action.COMPLETE:
        commandObject = new CompleteLeadCommand(actionName, commandId, item);
        break;
      case Action.FAIL:
        commandObject = new FailLeadCommand(actionName, commandId, item);
        break;
      case Action.PROCESS:
        commandObject = new ProcessLeadCommand(actionName, commandId, item);
        break;
      case Action.UNPROCESS:
        commandObject = new UnprocessLeadCommand(actionName, commandId, item);
        break;
      case Action.ASSIGN:
        commandObject = new AssignLeadCommand(actionName, commandId, item);
        break;
      case Action.PENDING:
        commandObject = new PendingLeadCommand(actionName, commandId, item);
        break;
      case Action.EXPIRED:
        commandObject = new ExpiredLeadCommand(actionName, commandId, item);
        break;
      default:
        break;
    }

    return await this.commandBus.execute(commandObject)
      .then((response) => {
        return response;
      }).catch((error) => {
        return error;
      });
  }

  private calTimeOutToProcess(lead: any, timezoneclient: string) {
    const ta = timezone().tz(timezoneclient);
    const startWorkingTime = ta.clone().set({ hour: ConfigTimeConst.START_WORKING_HOURS, minute: 0, second: 0, millisecond: 0 });
    const endWorkingTime = ta.clone().set({ hour: ConfigTimeConst.END_WORKING_HOURS, minute: 0, second: 0, millisecond: 0 });

    let timeOut;
    const t2 = timezone(lead.t2).tz(timezoneclient);
    if (t2 > ta) {
      return lead.t3;
    } else {
      timeOut = ta.add(ConfigTimeConst.TICKET_RED_TIME, 'minutes');
      if (timeOut.isAfter(endWorkingTime)) {
        startWorkingTime.add(1, 'days');
        const diff = timeOut.diff(startWorkingTime, 'minutes');
        timeOut = startWorkingTime.add(diff, 'minutes');
      }
    }
    return timeOut.format();
  }

  private createTimeout(timezoneclient: string) {
    const ta = timezone().tz(timezoneclient);
    const timeOut = ta.add(ConfigTimeConst.TICKET_TIME_OUT, 'minutes');
    return timeOut.format();
  }

  private async validateLeadToAssign(leadId: string) {
    const lead = await this.queryRepository.findOne({ id: leadId });
    if (isNullOrUndefined(lead)) {
      throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, 'ticket') });
    } else if (lead.processBy != null) {
      throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.LEAD_ASSIGNED) });
    }
    return lead;
  }

  private async checkEmployeeExist(employeeId: string) {
    const employee = await this.employeeRepository.findOne({ id: employeeId });
    if (isNullOrUndefined(employee)) {
      throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, 'employee') });
    }
    return employee;
  }

  private async validateEmployeeBeforePull(employeeId: string, type: string) {
    const employee = await this.employeeRepository.findOne({ id: employeeId });
    if (isNullOrUndefined(employee)) {
      throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, 'employee') });
    }

    // Không check isPenalty nữa - O2OWADMIN-619
    // if (employee.isPenalty && employee.isPenalty === true) {
    //     throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.EMPLOYEE_PENALTY) });
    // }

    if (isNullOrUndefined(employee.pos) || isNullOrUndefined(employee.pos.id)) {
      throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.EMPLOYEE_NOT_ASSIGN_POS) });
    }


    const isValidBeforePull = await this.queryRepository.isValidBeforePull(employeeId, type);
    if (isValidBeforePull === false) {
      throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.EMPLOYEE_NOT_PROCESSED_YET) });
    }

    return employee;
  }

  async updateStatusLead() {
    const all = await this.queryRepository.findAll();

    if (all.length === 0) {
      return;
    }

    const yellow = [];
    const red = [];
    // let warning = [];
    await all.forEach(lead => {
      const timezoneclient = lead.timezoneclient;
      const now = timezone().tz(timezoneclient);
      const t1 = timezone(lead.t1).tz(timezoneclient);
      const t2 = timezone(lead.t2).tz(timezoneclient);
      if (t1 <= now && now < t2) {
        yellow.push(lead.id);
      } else if (t2 <= now) {
        red.push(lead.id);
        // if(lead.processBy) warning.push(lead);
      }
    });

    if (yellow.length > 0) {
      await this.queryRepository.updateStatus({ ids: yellow, status: StatusEnum.YELLOW });
    }

    if (red.length > 0) {
      await this.queryRepository.updateStatus({ ids: red, status: StatusEnum.RED });
    }

    // warning.forEach(item => {
    //     const msg = {
    //         title: `Ticket sắp hết hạn khai thác.`,
    //         content: `Ticket của bạn sắp hết hạn xử lý. Hãy nhanh khai thác nếu không sẽ bị không thể tiếp tục xử lý`,
    //         entityName: 'lead',
    //         entityId: item.id,
    //         eventName: 'leadWarning'
    //     }
    //     const sender = {};
    //     const receivers = [item.processBy];
    //     this.notifyService.subscribe(CmdPatternConst.SERVICE.NOTIFICATION.NOTIFY, {msg, sender, receivers});
    // });
  }

  async timeout() {
    const leadProcess = await this.queryRepository.findLeadsAssigned();
    if (leadProcess.length === 0) {
      return;
    }
    for (const lead of leadProcess) {
      const timezoneclient = lead.timezoneclient;
      const timeOut = timezone(lead.timeOut).tz(timezoneclient);
      const now = timezone().tz(timezoneclient);

      if (timeOut <= now) {
        // flow mới update 20/2/2020
        // Yêu cầu bị timeout thì đặt lifeCycleStatus là inpool, để NV khác pull ticket về
        // không quan tâm tới bị timeout mấy lần
        if (lead.lifeCycleStatus !== LifeCycleStatusEnum.PENDING) {
          const history: ILeadProcessed[] = lead.processedHistory || [];
          history.push({
            id: uuid.v4(),
            processedDate: new Date(),
            processBy: lead.processBy,
            isReject: false,
            isTimeOut: true,
          } as ILeadProcessed);
          this.commandId = uuid.v4();
          const model: any = {
            id: lead.id,
            lifeCycleStatus: LifeCycleStatusEnum.IN_POOL,
            modifiedBy: lead.modifiedBy,
            processBy: null,
            processedHistory: history
          };
          await this.executeCommand(Action.EXPIRED, 'background action', this.commandId, model);
        }
      }
    }
  }
  async importDemandTicket(files: any, dto: ImportLeadDemandDto, actionName: string, timezoneclient?: string, userId?: string) {
    const history = [];
    let sanList = [{
      id: 'dxs-shared',
      name: 'Các sàn DXS',
      code: 'DXS-Virtual',
      parentId: '',
      type: 'SAN'
    }];
    if (CommonUtils.stringNotEmpty(dto.exchangeId)) {
      // console.log(***********, dto.exchangeId);
      sanList = await this.orgchartClient.sendDataPromise({ posId: dto.exchangeId }, CmdPatternConst.LISTENER.GET_POS_BY_QUERY);
    }
    // console.log(JSON.stringify(sanList));
    const workbook = XLSX.read(files[0].buffer, { type: 'buffer' });
    const sheet_name_list = workbook.SheetNames;
    const leadList: any = await XLSX.utils.sheet_to_json(workbook.Sheets[sheet_name_list[0]], { range: 1 });
    let employee: any = await this.employeeRepository.findOne({ 'account.id': userId });
    if (isNullOrUndefined(employee)) {
      employee = {
        id: userId
      };
    } else {
      employee = {
        // id: employee.id,
        id: userId,
        name: employee[0].name,
        code: employee[0].code,
        email: employee[0].email
      };
    }

    for (const [i, lead] of leadList.entries()) {
      lead.pos = {
        id: sanList[0].id,
        name: sanList[0].name,
        parentId: sanList[0].parentId,
        code: sanList[0].code,
        type: sanList[0].type
      };
      lead.phone = lead.phone ? lead.phone.toString() : '';
      lead.email = lead.email ? lead.email.toString().toLowerCase() : '';
      lead.type = CommonConst.TYPE.PRIMARY;
      lead.timestamp = dto.timestamp;
      lead.source = dto.resource;
      lead.demandCustomer = {
        personalInfo: {
          name: lead.name,
          phone: lead.phone,
          email: lead.email,
          identities: [
            {
              type: 'CMND',
              value: lead.identity
            }
          ]
        }
      };
      lead.property = {
        code: lead.propertyCode,
        project: { id: lead.projectId, name: lead.projectName }
      };
      lead.createdDate = +lead.createdDate;
      lead.importedBy = employee || {};
      const validate = this.validateLead(lead);
      if (validate) {
        history.push({
          line: i + 1,
          error: validate
        });
      } else {
        await this.createLeadImport(lead, actionName, timezoneclient);
      }
    }
    const readModel = {
      fileName: files[0].originalname,
      processBy: employee || {},
      success: leadList.length - history.length,
      fail: history.length,
      type: CommonConst.TYPE.PRIMARY,
      createdDate: new Date(),
      updatedDate: new Date(),
      description: history
    };
    await this.historyImportService.create(readModel);
  }
  private async createLeadImport(lead, actionName, timezoneclient): Promise<any> {
    return await this.createLead(lead, actionName, timezoneclient);
  }
  private validateLead(lead) {
    if (isNullOrUndefined(lead.phone) || !lead.phone.toString().match(CommonConst.REGEX_VN_PHONE)) {
      return 'Số điện thoại sai';
    }
    if (lead.email && !lead.email.toString().match(CommonConst.REGEX_EMAIL)) {
      return 'Email sai';
    }
    return '';
  }

  async importLeadFromExcel(files: any, options: ImportLeadAsExcelDto, actionName: string, timezoneclient: string, user: any) {
    const workbook = XLSX.read(files[0].buffer, { type: 'buffer' });
    const sheets = workbook.SheetNames;
    let leads = await XLSX.utils.sheet_to_json(workbook.Sheets[sheets[0]], { range: 1 });

    leads = leads.map(lead => {
      return {
        ...lead,
        repoId: options.repoId,
        repoConfigCode: options.repoConfigCode,
        importedBy: {
          id: user.id,
          name: user.name,
        },
      };
    });

    for (const lead of leads) {
      await this.createLeadImport(lead, actionName, timezoneclient);
    }
  }

  private updateExploitHistory(
    history: IExploitHistory[] = [],
    status: ExploitEnum,
    takeCareId?: string,
    takeCareInfo?: ITakeCare,
  ) {
    const newHistory: IExploitHistory = {
      status, updatedAt: new Date(), updatedBy: takeCareId, takeCareId, takeCareInfo
    };
    if (history.length > 500) {
      history = history.slice(history.length - 500);
    }
    history.push(newHistory);

    return history;
  }
}
