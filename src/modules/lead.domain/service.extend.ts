import { BadRequestException, Injectable } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import * as Bluebird from 'bluebird';
import { cloneDeep, isEmpty, uniq } from 'lodash';
import uuid = require('uuid');
import { CodeGenerateService } from '../code-generate/service';
import { EmployeeQueryRepository } from '../employee/repository/query.repository';
import { HistoryImportService } from '../history-import/service';
import { ILeadDocument } from '../lead.queryside/interfaces/document.interface';
import { LeadQueryRepository } from '../lead.queryside/repository/query.repository';
import { LeadRepoDomainService } from '../leadRepo.domain/service';
import { LeadRepoQueryRepository } from '../leadRepo.queryside/repositories/query.repository';
import { Queue } from '../shared/classes/class-queue';
import { CommonConst } from '../shared/constant/common.const';
import { ErrorConst } from '../shared/constant/error.const';
import { Action } from '../shared/enum/action.enum';
import { ExploitEnum } from '../shared/enum/exploit.enum';
import { LifeCycleStatusEnum } from '../shared/enum/life-cycle-status.enum';
import { StatusEnum } from '../shared/enum/status.enum';
import { CommandModel } from '../shared/eventStream/models/command.model';
import {
  LeadRepo,
  LeadRepoConfig,
  LeadRepoConfigHot,
} from '../shared/models/leadRepo/model';
import {
  ILead, IExploitHistory, ITakeCare
} from '../shared/services/lead/interfaces/lead.interface';
import { ImportLeadCommand } from './commands/impl/importLead.cmd';
import { RenewLeadCommand } from './commands/impl/renewLead.cmd';
import { DeliverLeadDto, ImportLeadAsExcelDto, RevokeAssignDto } from './dto/lead.dto';
import * as moment from 'moment';
import * as momentTz from 'moment-timezone';
import { LeadSourceQueryRepository } from '../leadSource/repository/query.repository';
import { PermissionConst } from '../shared/constant/permission.const';
import { DeliverTypeEnum } from '../shared/enum/deliver-type.enum';
import { BaseService, ErrorService, CmdPatternConst, AwesomeLogger } from '../../../shared-modules';
import { EmployeeClient } from '../mgs-sender/employee.client';
import * as xlsx from 'xlsx';
import { Workbook } from 'exceljs';
import * as axios from 'axios';
import * as FormData from 'form-data';
import { ImportStatusEnum } from '../shared/enum/import-status.enum';
import * as path from "path";
import { AssignLeadCommand } from './commands/impl/assign.cmd';
import { OrgchartClient } from '../mgs-sender/orgchart.client';

interface PosQueueData {
  id: string;
  employeeQueue: Queue<Object>;
}

@Injectable()
export class LeadDomainServiceExtends extends BaseService {
  private readonly context = LeadDomainServiceExtends.name;
  private readonly response = { success: true };
  private readonly logger = new AwesomeLogger(LeadDomainServiceExtends.name);
  private readonly errorTemplatePath = path.join(process.cwd(), 'src', 'templates', 'error_template.xlsx');

  constructor(
    private readonly commandBus: CommandBus,
    private readonly leadRepoQueryRepo: LeadRepoQueryRepository,
    private readonly employeeRepo: EmployeeQueryRepository,
    private readonly leadQueryRepo: LeadQueryRepository,
    private readonly genCodeSrv: CodeGenerateService,
    private readonly leadRepoDomainSrv: LeadRepoDomainService, // private readonly notificationClient: NotificationClient
    private readonly historyImportSrc: HistoryImportService,
    private readonly sourceRepository: LeadSourceQueryRepository,
    public readonly errorService: ErrorService,
    private readonly employeeClient: EmployeeClient,
    private readonly orgchartClient: OrgchartClient,
  ) {
    super(errorService);
  }

  async importLeadFromExcel(
    files: any[],
    options: ImportLeadAsExcelDto,
    actionName?: string,
    timezoneClient?: string,
    user?: any,
  ) {
    const { repoConfigCode, repoId, source } = options;

    const fileOriginalName = path.parse(files[0].originalname).name;
    let failedFileName = '';
    let failedFileUrl = '';
    let failedFilePath = '';
    let importStatus = ImportStatusEnum.SUCCESS;

    const workbook = xlsx.read(files[0].buffer, { type: 'buffer' });
    const sheets = workbook.SheetNames;
    const dataJson: any[] = await xlsx.utils.sheet_to_json(
      workbook.Sheets[sheets[0]],
      { header: 1, blankrows: false }
    );

    // check data
    let validateTemplate = true;
    const keys = ['no', 'name', 'phone', 'email', 'profileUrl', 'assignee', 'note'];
    dataJson.slice(0).map((row, idx) => {
      // check template
      if (idx == 1) {
        validateTemplate =
          (row[0] == 'STT') &&
          (row[1] == 'Họ và tên *') &&
          (row[2] == 'Số điện thoại *') &&
          (row[3] == 'Email') &&
          (row[4] == 'Link profile') &&
          (row[5] == 'Nhân viên tư vấn') &&
          (row[6] == 'Ghi chú');
      }
    });
    this.logger.debug('validateTemplate', validateTemplate);
    if (!validateTemplate) {
      await this.importHistory(
        files[0].originalname,
        user,
        0,
        0,
        'File của bạn không đúng với template yêu cầu',
        '',
        '',
        '',
        ImportStatusEnum.ENTIRE_ERROR
      );
      return { success: false };
    }

    const data = dataJson.slice(3).map((row, idx) => {
      this.logger.debug('row', row);
      return {
        [keys[0]]: row[0] || '', // STT
        [keys[1]]: row[1] || '', // Họ và tên
        [keys[2]]: String(row[2] || ''), // Số điện thoại
        [keys[3]]: row[3] || '', // Email
        [keys[4]]: row[4] || '', // Link profile
        [keys[5]]: row[5] || '', // Nhân viên tư vấn
        [keys[6]]: row[6] || '', // Ghi chú
      };
    });

    const { repository, config } = await this.validateRepo(repoId, repoConfigCode);

    const leadSource: any = await this.sourceRepository.findOne({ name: source });
    if (!leadSource) {
      this.sourceRepository.create({ name: source });
    }
    const orgChartGroup = new Map();
    if (config.orgCharts?.length) { // CRM NEW: always 1 orgChart in config
      config.orgCharts.forEach(item => {
        orgChartGroup.set({ id: item.id, name: item.name }, item.staffIds);
      });
    }

    const hotLeads = [];
    const normalLeads = [];
    const failedLeads = [];
    const assignedLeads = [];

    await Bluebird.mapSeries(data, async (item, idx) => {
      const validateItem = await this.validateExcelItem(item, config);
      this.logger.debug('validateItem', validateItem);
      if (!isEmpty(validateItem)) {
        failedLeads.push({
          line: idx + 1,
          data: item,
          error: validateItem
        });
      } else {
        const lead = await this.preProcessLead(
          item,
          repository,
          config,
          user,
          timezoneClient,
          source,
        );

        const { assignee } = item;
        if (assignee) {
          const emp = await this.employeeRepo.findOne({ code: assignee });
          const employee = emp[0] || {};
          const { error, pos } = await this.validateEmployee(employee, orgChartGroup);
          if (error) {
            failedLeads.push({
              line: idx + 1,
              data: item,
              error,
            });
          } else {
            const { history, newHistory } = this.updateExploitHistory(
              lead.exploitHistory,
              ExploitEnum.ASSIGN,
              employee.id,
              {
                id: employee.id,
                name: employee.name,
                phone: employee.phone,
                email: employee.email,
              }
            );
            Object.assign(lead, {
              takeCare: {
                id: employee.id,
                name: employee.name,
                email: employee.email,
                phone: employee.phone,
              },
              assignDuration: config.assignDuration,
              expireTime: this.getExpireTime(
                newHistory?.updatedAt,
                config.assignDuration
              ),
              latestAssignHistory: newHistory,
              exploitStatus: ExploitEnum.ASSIGN,
              exploitHistory: history,
              pos,
              assignedDate: new Date(), // crm new
              surveys: config.surveys
            });
            assignedLeads.push(lead);
          }
        } else {
          Object.assign(lead, {
            takeCare: {},
          });
          if (lead.isHot) {
            hotLeads.push(lead);
          } else {
            normalLeads.push(lead);
          }
        }
      }
    });

    this.logger.debug('data.length', data.length);

    // create error leads file
    if (failedLeads.length > 0) {
      const fileBuffer = await this.failLeadToExcel(user, failedLeads);
      // this.logger.debug('fileName', `ErrorUpload_Lead_${(new Date).getTime()}_lead_error.xlsx`);

      const formData = new FormData();
      const now = moment();
      const date = now.format('YYYY-MM-DD');
      const time = now.format('HHmmss');
      failedFileName = `ErrorUpload_${fileOriginalName}_${date}_${time}.xlsx`;
      formData.append('file', fileBuffer, { filename: failedFileName });
      formData.append('path', 'lead');
      formData.append('useOriginalName', 'true');
      try {
        const response = await axios.default.post(
          process.env.MTD_UPLOAD,
          formData,
          { headers: { ...formData.getHeaders() } });

        this.logger.debug('res from mtd', response.data);
        if (response.data.statusCode == '0') {
          failedFileUrl = response.data.data.Location || '';
          failedFilePath = response.data.data.Key || '';
        }

        if (failedLeads.length == data.length) {
          importStatus = ImportStatusEnum.ENTIRE_ERROR;
        } else {
          importStatus = ImportStatusEnum.PARTIAL_ERROR;
        }
      } catch (error) {
        this.logger.error('error from mtd', error);
      }
    }

    // deliver hot lead first
    let { newConfig, result } = await this.leadDelivery(
      hotLeads,
      repository.configHot as LeadRepoConfig
    );

    // in case no hot lead found, deliver normal lead, only 1 in 2 arrays has data
    // normalLeads = no assignee
    // assignedLeads = have assignee
    let records = [...normalLeads, ...assignedLeads];

    if (result.length) { // check if hot lead has been delivered
      const importCommandId = uuid.v4();
      result = result.map(r => {
        r.notiUser = user;
        return r;
      })
      await this.executeCommand(
        Action.IMPORT_LEAD,
        actionName,
        importCommandId,
        result as any[],
        newConfig.notification || {}
      );

      await this.leadRepoDomainSrv.updateLeadRepoMain(
        {
          id: repository.id,
          configHot: newConfig as LeadRepoConfigHot,
          name: repository.name,
        },
        actionName,
        user?.id,
        timezoneClient,
        true
      );
    }

    if (records.length) { // check if normal leads found
      const importCommandId = uuid.v4();
      records = records.map(r => {
        r.notiUser = user;
        return r;
      });
      await this.executeCommand(
        Action.IMPORT_LEAD,
        actionName,
        importCommandId,
        records,
        config.notification || {}
      );
    }

    await this.importHistory(
      files[0].originalname,
      user,
      [...normalLeads, ...hotLeads, ...assignedLeads].length,
      failedLeads.length,
      failedLeads,
      failedFileUrl,
      failedFileName,
      failedFilePath,
      importStatus
    );

    return { ...this.response, failedFileUrl, failedFileName, failedFilePath };
  }

  async importHistory(fileName, user, successCount, failCount, description, failedFileUrl, failedFileName, failedFilePath, status) {
    const importHistoryRecord = {
      fileName,
      processBy: user,
      success: successCount,
      fail: failCount,
      type: CommonConst.TYPE.PRIMARY,
      createdDate: new Date(),
      updatedDate: new Date(),
      description,
      failedFileUrl,
      failedFileName,
      failedFilePath,
      status
    };

    await this.historyImportSrc.create(importHistoryRecord);
  }

  async deliverNormalLead(recordNumber) {
    this.logger.info(this.context, '==========Start delivering normal leads...==========');
    const where = {
      'configs.exploitTime.from': { $lte: new Date() },
      'configs.exploitTime.to': { $gte: new Date() }
    }
    const repos = await this.leadRepoQueryRepo.findMany({
      where: where as any,
      projection: { configs: 1, name: 1, code: 1, id: 1, timezoneClient: 1 },
    }, true);

    if (!repos?.length) return;
    const bulkRepos = [];

    await Bluebird.map(repos, async (r) => {
      const { configs } = r;
      const activeConfigs = configs.filter(item => item.active);
      if (!activeConfigs?.length) return;
      let shouldUpdate = false;
      await Bluebird.mapSeries(activeConfigs, async (c) => {
        const { exploitTime } = c;
        const nowMm = moment();
        const now = nowMm.toDate();
        const day = now.getDay() === 0 ? 7 : now.getDay() - 1;
        if (
          now < moment(exploitTime.from).utc().toDate() ||
          now > moment(exploitTime.to).utc().toDate()
        ) {
          return;
        }

        const timezoneOffset = momentTz.tz(r.timezoneClient).utcOffset() / 60;

        if (c.isWorkingTime && c.workingTime[day] && c.workingTime[day].startTime && c.workingTime[day].endTime) {
          if (now < new Date(`${nowMm.format('YYYY-MM-DD')} ${c.workingTime[day].startTime} ${timezoneOffset > 0 ? '+' + timezoneOffset : timezoneOffset}`) ||
            now > new Date(`${nowMm.format('YYYY-MM-DD')} ${c.workingTime[day].endTime} ${timezoneOffset > 0 ? '+' + timezoneOffset : timezoneOffset}`)
          ) {
            return;
          }
        }
        const leads = await this.leadQueryRepo.findMany(
          {
            repoId: r.id,
            repoConfigCode: c.code,
            isHot: false,
            exploitStatus: {
              $in: [ExploitEnum.NEW, ExploitEnum.RENEW],
            },
          },
          {
            exploitHistory: 1,
            exploitStatus: 1,
            takeCare: 1,
            id: 1,
            pos: 1,
            expireTime: 1,
            code: 1,
            createdDate: 1,
            name: 1,
            phone: 1,
            email: 1,
            isHot: 1,
            notiUser: 1
          },
          // recordNumber
        );

        const { result, newConfig } = await this.leadDelivery(
          leads as any[],
          c
        );

        if (!result.length) return;
        // exe 100 records
        const executeNum = Math.floor(result.length / 100) + Math.floor(result.length % 100);
        for (let i = 0; i < executeNum; i++) {
          const executeResult = result.slice((i * 100), (i * 100) + 100);
          const importCommandId = uuid.v4();
          await this.executeCommand(
            Action.RENEW_LEAD,
            'Deliver normal lead',
            importCommandId,
            // result as any,
            executeResult.map(e => {
              return {
                id: e.id,
                exploitStatus: e.exploitStatus,
                takeCare: e.takeCare,
                assignDuration: e.assignDuration,
                expireTime: e.expireTime,
                visiblePhone: e.visiblePhone,
                latestAssignHistory: e.latestAssignHistory,
                isHot: e.isHot,
                name: e.name,
                email: e.email,
                code: e.code,
                phone: e.phone,
                pos: e.pos,
                notiUser: e.notiUser,
                assignedDate: e.assignedDate
              } as any;
            }),
            newConfig.notification || {}
          );
        }

        this.updateConfigData(configs, c.code, newConfig);
        if (!shouldUpdate) {
          shouldUpdate = true;
        }

        this.logger.info(this.context, `Delivered ${result.length} leads in ${r.code}-${c.code}`);
      });

      if (shouldUpdate) {
        Object.assign(r, { configs, updatedDate: new Date() });
        bulkRepos.push(r);
      }
    });
    if (bulkRepos.length) {
      this.leadRepoDomainSrv.bulkUpdateLeadConfig(bulkRepos);
    }

    this.logger.info(this.context, `Finish delivering normal leads`);
  }

  async renewNormalLead(recordNumber) {
    this.logger.info(this.context, '==========Start renewing normal leads...==========');
    const assigningLead = await this.leadQueryRepo.findMany(
      {
        // 'takeCare.id': { $exists: true },
        isHot: false,
        expireTime: {
          $lt: new Date(),
        },
        assignDuration: { $ne: -1 },
        exploitStatus: {
          $in: [
            // ExploitEnum.REASSIGN,
            ExploitEnum.ASSIGN,
            ExploitEnum.PROCESSING,
          ],
        },
      },
      { id: 1 },
      // recordNumber
    );

    if (!assigningLead?.length) {
      return;
    }

    assigningLead.forEach((lead) => {
      const latestAssignHistory: IExploitHistory = {
        status: ExploitEnum.RENEW, updatedAt: new Date()
      };

      Object.assign(lead, {
        exploitStatus: ExploitEnum.RENEW,
        latestAssignHistory: latestAssignHistory,
        takeCare: {},
      });
    });
    // exe 100 records
    const executeNum = Math.floor(assigningLead.length / 100) + Math.floor(assigningLead.length % 100);
    for (let i = 0; i < executeNum; i++) {
      const executeResult = assigningLead.slice((i * 100), (i * 100) + 100);
      const importCommandId = uuid.v4();
      await this.executeCommand(
        Action.RENEW_LEAD,
        'Renew normal lead',
        importCommandId,
        executeResult.map(e => ({
          id: e.id,
          exploitStatus: e.exploitStatus,
          takeCare: e.takeCare,
          latestAssignHistory: e.latestAssignHistory
        } as any))
      );
      this.logger.info(this.context, `Renewed ${executeResult.length} normal leads expired !!!`);
    }
    this.logger.info(this.context, 'Finish renewing normal leads');
  }

  async reAssignHotLead(recordNumber) {
    this.logger.info(this.context, '==========Start reassigning hot leads...==========');

    const repo = await this.leadRepoQueryRepo.findMany({
      projection: { id: 1, name: 1, code: 1, configHot: 1 },
    });
    if (!repo?.length) return;
    const bulkRepos = [];
    await Bluebird.map(repo, async (r) => {
      let shouldUpdate = false;
      const now = new Date();
      const day = now.getDay() === 0 ? 7 : now.getDay() - 1;
      if (r.configHot.isWorkingTime && r.configHot.workingTime[day] && r.configHot.workingTime[day].startTime && r.configHot.workingTime[day].endTime) {
        if (now < new Date(`${moment(now).format('YYYY-MM-DD')} ${r.configHot.workingTime[day].startTime}`) ||
          now > new Date(`${moment(now).format('YYYY-MM-DD')} ${r.configHot.workingTime[day].endTime}`)) {
          return;
        }
      }
      const hotleads = await this.leadQueryRepo.findMany(
        {
          $or: [
            {
              isHot: true,
              repoId: r.id,
              expireTime: {
                $lt: new Date(),
              },
              assignDuration: { $ne: -1 },
              exploitStatus: {
                $in: [
                  ExploitEnum.ASSIGN,
                  // ExploitEnum.REASSIGN,
                  ExploitEnum.PROCESSING,
                ],
              },
            },
            {
              isHot: true,
              repoId: r.id,
              exploitStatus: {
                $in: [ExploitEnum.NEW, ExploitEnum.RENEW],
              },
            },
          ],
        },
        {
          // exploitHistory: 1,
          exploitStatus: 1,
          takeCare: 1,
          id: 1,
          pos: 1,
          expireTime: 1,
          code: 1,
          createdDate: 1,
          name: 1,
          phone: 1,
          email: 1,
          isHot: 1,
          countAssign: 1,
          notiUser: 1,
        }, recordNumber
      );

      if (!hotleads?.length) return;

      const { newConfig, result } = await this.leadDelivery(
        hotleads,
        r.configHot as LeadRepoConfig
      );

      if (!result?.length) return;
      // exe 100 records
      const executeNum = Math.floor(result.length / 100) + Math.floor(result.length % 100);
      for (let i = 0; i < executeNum; i++) {
        const executeResult = result.slice((i * 100), (i * 100) + 100);
        const importCommandId = uuid.v4();
        await this.executeCommand(
          Action.RENEW_LEAD,
          'Deliver normal lead',
          importCommandId,
          // result as any,
          executeResult.map(e => {
            return {
              id: e.id,
              exploitStatus: e.exploitStatus,
              takeCare: e.takeCare,
              assignDuration: e.assignDuration,
              visiblePhone: e.visiblePhone,
              expireTime: e.expireTime,
              latestAssignHistory: e.latestAssignHistory,
              isHot: e.isHot,
              name: e.name,
              phone: e.phone,
              email: e.email,
              code: e.code,
              pos: e.pos,
              notiUser: e.notiUser,
              assignedDate: e.assignedDate
            } as any;
          }),
          newConfig.notification || {}
        );
      }
      if (!shouldUpdate) {
        shouldUpdate = true;
      }

      if (shouldUpdate) {
        Object.assign(r, { configHot: newConfig, updatedDate: new Date() });
        bulkRepos.push(r);
      }
      this.logger.info(this.context, `Reassigned ${result.length} hot leads in ${r.code} !!!`);
    });

    if (bulkRepos.length) {
      this.leadRepoDomainSrv.bulkUpdateLeadConfig(bulkRepos);
    }
    this.logger.info(this.context, 'Finish reassigning hot leads');
  }

  async deliverLeads(dto: DeliverLeadDto[], user) {
    this.logger.info(this.context, 'Deliver leads');
    let status = [ExploitEnum.MANUAL_DELIVER];
    const isRevoke = user.roles.includes(PermissionConst.LEAD_REVOKE_DELIVER);
    const emp = await this.employeeRepo.findOne({ 'account.id': user.id });
    const employee = emp[0] || {};

    let query: any = {
      id: {
        $in: dto.map(e => e.id)
      }
    };
    if (isRevoke) {
      status.push(ExploitEnum.NEW, ExploitEnum.RENEW, ExploitEnum.ASSIGN,
        // ExploitEnum.REASSIGN
      );
    } else {
      query["takeCare.id"] = employee.id;
    }
    query.exploitStatus = status;
    const [leads, manager] = await Promise.all([
      this.leadQueryRepo.findMany(query),
      this.employeeRepo.findOne({ id: employee.id })
    ]);

    if (!leads || leads.length !== dto.length) {
      throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, 'id') });
    }

    if (!manager || dto.some(d => !manager[0]?.staffIds.includes(d.assignee))) {
      throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, 'id') });
    }

    const employees = await this.employeeRepo.find({
      id: {
        $in: dto.map(e => e.assignee)
      }
    });

    const leadRepos = await this.leadRepoQueryRepo.find({
      id: {
        $in: uniq(leads.map(e => e.repoId))
      }
    });

    // exe 100 records
    const executeNum = Math.floor(leads.length / 100) + Math.floor(leads.length % 100);
    for (let i = 0; i < executeNum; i++) {
      const executeResult = leads.slice((i * 100), (i * 100) + 100);
      const importCommandId = uuid.v4();
      await this.executeCommand(
        Action.RENEW_LEAD,
        'Manual deliver normal lead',
        importCommandId,
        // result as any,
        executeResult.map(lead => {
          const deliverLead = dto.find(d => lead.id === d.id);
          const employee = employees.find(emp => emp.id === deliverLead?.assignee);
          const repo = leadRepos.find(r => r.id === lead.repoId);
          const config = lead.isHot ? repo.configHot : repo.configs.find(c => c.code === lead.repoConfigCode);

          const { history, newHistory } = this.updateExploitHistory(
            lead.exploitHistory,
            ExploitEnum.ASSIGN,
            employee.id,
            {
              id: employee.id,
              name: employee.name,
              phone: employee.phone,
              email: employee.email,
            },
          );
          return {
            id: lead.id,
            exploitStatus: ExploitEnum.ASSIGN,
            takeCare: {
              id: employee.id,
              name: employee.name,
              email: employee.email,
              phone: employee.phone
            },
            assignDuration: config.assignDuration,
            expireTime: this.getExpireTime(newHistory?.updatedAt, config.assignDuration),
            visiblePhone: lead.visiblePhone,
            latestAssignHistory: newHistory,
            isHot: lead.isHot,
            name: lead.name,
            email: lead.email,
            code: lead.code,
            phone: lead.phone,
            pos: lead.pos,
            assignedDate: new Date()
          } as any;
        }),
        {}
      );
    }

    return { success: true };
  }

  async manualDeliver(dto: DeliverLeadDto[], user: any) {
    this.logger.info(this.context, 'Manual deliver lead');
    let [leads, manager] = await Promise.all([
      this.leadQueryRepo.findMany({
        id: {
          $in: dto.map(e => e.id)
        }
      }),
      this.employeeRepo.findOne({
        'account.id': user.id
      })
    ]);

    console.log('leads', leads);
    console.log('manager', manager);

    manager = manager[0];

    if (!leads || leads.length !== dto.length) {
      return this.getResponse('LEADE0002');
    }

    const employees = await this.employeeRepo.find({
      code: {
        $in: dto.map(e => e.assignee)
      }
    });
    console.log('employees', employees);

    const org = await this.orgchartClient.sendDataPromise({ code: manager['orgCode'] }, CmdPatternConst.ORGCHART.LISTENER.GET_BY_QUERY);

    if (!manager || !manager['isLinemanager'] || employees.some(emp => !org.staffIds.includes(emp.id))) {
      return this.getResponse('LEADE0003');
    }

    const leadRepos = await this.leadRepoQueryRepo.find({
      id: {
        $in: uniq(leads.map(e => e.repoId))
      }
    });

    for (let i = 0; i < leads.length; i++) {
      const importCommandId = uuid.v4();
      const deliverLead = dto.find(d => leads[i].id === d.id);
      const lead = leads[i]
      const employee = employees.find(emp => emp.code === deliverLead?.assignee);
      const repo = leadRepos.find(r => r.id === lead.repoId);
      const config = lead.isHot ? repo.configHot : repo.configs.find(c => c.code === lead.repoConfigCode);

      const { history, newHistory } = this.updateExploitHistory(
        lead.exploitHistory,
        ExploitEnum.ASSIGN,
        employee.id,
        {
          id: employee.id,
          name: employee.name,
          phone: employee.phone,
          email: employee.email,
        },
      );
      const commandModel = {
        id: lead.id,
        exploitHistory: history,
        exploitStatus: ExploitEnum.ASSIGN,
        takeCare: {
          id: employee.id,
          name: employee.name,
          email: employee.email,
          phone: employee.phone
        },
        assignDuration: config.assignDuration,
        expireTime: this.getExpireTime(newHistory?.updatedAt, config.assignDuration),
        visiblePhone: lead.visiblePhone,
        latestAssignHistory: newHistory,
        isHot: lead.isHot,
        name: lead.name,
        email: lead.email,
        code: lead.code,
        phone: lead.phone,
        pos: lead.pos,
        assignedDate: new Date()
      } as any;

      await this.executeCommand(
        Action.ASSIGN,
        'Manual deliver normal lead',
        importCommandId,
        commandModel
      );
    }

    return this.getResponse(0);
  }

  private async leadDelivery(data: ILeadDocument[], config: LeadRepoConfig) {
    const result: ILead[] = [];
    if (!data?.length) {
      return {
        result,
        newConfig: config,
      };
    }
    const initQueueData = await this.initQueueData(config);
    if (initQueueData.size === 0) {
      return {
        result,
        newConfig: config,
      };
    }
    // san1
    // ihouzz
    // san2

    const { orgChartQueue = [], assignDuration, orgCharts } = config;
    // init pos queue
    const posQueue = new Queue<PosQueueData>([]);
    initQueueData.forEach((value, key) => {
      const cloneValue = cloneDeep(value);
      const existedQueue = orgChartQueue.find((item) => item.id === key);
      // ihouzz
      // san2
      // san1
      if (existedQueue && config.deliverType == DeliverTypeEnum.AUTO) { // auto
        posQueue.enqueue({
          id: existedQueue.id,
          employeeQueue: new Queue(existedQueue.employeeQueue || []),
        });
      } else { // manual
        posQueue.enqueue({
          id: key,
          employeeQueue: new Queue(cloneValue),
        });
      }
    });

    // posQueue = san1 ihouzz san2

    const dataQueue = new Queue(data, data.length);

    while (!dataQueue.isEmpty()) {
      const orgChart = this.getOrgChart(posQueue, initQueueData);
      const { employeeQueue, id } = orgChart;
      const lead = dataQueue.dequeue() as ILead;
      const posName = orgCharts.find((item) => item.id === id)?.name;
      const employee = employeeQueue.dequeue() as any;
      if (lead.takeCare?.id !== employee.id) {

        let exploitStatus = ExploitEnum.ASSIGN;
        // lead.exploitStatus === ExploitEnum.NEW
        // ? ExploitEnum.ASSIGN
        // : ExploitEnum.REASSIGN;
        if (config.deliverType == 1) {
          exploitStatus = ExploitEnum.MANUAL_DELIVER;
        }
        const { history, newHistory } = this.updateExploitHistory(
          lead.exploitHistory,
          exploitStatus,
          employee.id,
          {
            id: employee.id,
            name: employee.name,
            phone: employee.phone,
            email: employee.email,
          },
        );

        Object.assign(lead, {
          exploitStatus,
          exploitHistory: history,
          assignDuration,
          takeCare: employee,
          expireTime: this.getExpireTime(
            newHistory?.updatedAt,
            assignDuration
          ),
          latestAssignHistory: newHistory,
          visiblePhone: config.visiblePhone,
          pos: { id, name: posName },
          assignedDate: new Date() // crm new
        });
        result.push(lead);
      }
      posQueue.enqueue(orgChart);
    }

    Object.assign(config, { orgChartQueue: this.parseQueueData(posQueue) });

    return { result, newConfig: config };
  }

  private parseQueueData(posQueue: Queue<PosQueueData>) {
    return posQueue.data.map((item) => ({
      id: item.id,
      employeeQueue: item.employeeQueue.data,
    }));
  }

  private deliveredToAll(posQueue: Queue<PosQueueData>) {
    return !posQueue.data.some((item) => {
      return !item.employeeQueue.isEmpty();
    });
  }

  private async initQueueData(
    config: LeadRepoConfig
  ): Promise<Map<string, any[]>> {
    const initData = new Map<string, any[]>();
    const { orgCharts } = config;

    await Bluebird.mapSeries(orgCharts, async org => {
      const { staffIds } = org;

      if (config.deliverType == DeliverTypeEnum.MANUAL) { // neu phan bo manual, ko tvv, assign cho manager 
        const managers = await this.employeeRepo.findManyWithSort(
          {
            managerAt: org.id,
            active: true
          },
          { id: 1, name: 1, email: 1, phone: 1, code: 1 }
        );
        if (managers?.length > 0) {
          initData.set(
            org.id,
            managers.map((e) => ({
              id: e.id,
              name: e.name,
              email: e.email,
              phone: e.phone,
            }))
          );
        }
      } else if (staffIds?.length > 0) { // neu phan bo auto, ko tvv, assign cho staff trong san
        const employees = await this.employeeRepo.findManyWithSort(
          {
            id: { $in: staffIds },
            active: true
          },
          { id: 1, name: 1, email: 1, phone: 1, code: 1 }
        );
        if (employees?.length > 0) {
          initData.set(
            org.id,
            employees.map((e) => ({
              id: e.id,
              name: e.name,
              email: e.email,
              phone: e.phone,
            }))
          );
        }
      }
    });

    return initData;
  }

  private async validateRepo(repoId: string, repoConfigCode: string) {
    const repository = await this.leadRepoQueryRepo.findById(repoId, true);

    if (!repository) {
      return this.getResponse('LREPOE0003');
    }

    const config = repository.configs.find(
      (item) => item.code === repoConfigCode
    );
    if (!config) {
      return this.getResponse('LREPOE0004');
    }

    return { repository, config };
  }

  private async executeCommand<T>(
    action: string,
    actionName: string,
    commandId: string,
    item: CommandModel | CommandModel[],
    additionalData?: T
  ) {
    switch (action) {
      case Action.IMPORT_LEAD:
        return this.commandBus.execute(
          new ImportLeadCommand(
            actionName,
            commandId,
            item as CommandModel[],
            additionalData
          )
        );
      case Action.RENEW_LEAD:
        return this.commandBus.execute(
          new RenewLeadCommand(
            actionName,
            commandId,
            item as CommandModel[],
            additionalData
          )
        );
      case Action.ASSIGN:
        return this.commandBus.execute(
          new AssignLeadCommand(
            actionName,
            commandId,
            item as CommandModel,
          )
        );
      default:
        break;
    }
  }

  private updateExploitHistory(
    history: IExploitHistory[] = [],
    status: ExploitEnum,
    takeCareId?: string,
    takeCareInfo?: ITakeCare,
  ) {
    const newHistory: IExploitHistory = {
      status, updatedAt: new Date(), updatedBy: takeCareId, takeCareId, takeCareInfo
    };
    if (history.length > 500) {
      history = history.slice(history.length - 500);
    }
    history = history.concat(newHistory);

    return { history, newHistory };
  }

  private validateLead(lead) {
    if (isEmpty(lead.phone) || !lead.phone.toString().match(CommonConst.REGEX_VN_PHONE)) {
      return 'Số điện thoại sai';
    }
    if (lead.email && !lead.email.toString().match(CommonConst.REGEX_EMAIL)) {
      return 'Email sai';
    }
    return '';
  }

  private async validateExcelItem(item, config) {
    this.logger.info('validate excel items', item);
    const { name, phone, email, profileUrl, assignee, note } = item;
    const { deliverType = 1 } = config;
    let error = '';

    // name
    if (isEmpty(name)) {
      error += 'Họ và tên không được bỏ trống\n';
    }
    if (name.length > 60) {
      error += 'Họ và tên vượt quá ký tự tối đa\n';
    }
    if (typeof name != 'string') {
      error += 'Họ và tên sai định dạng\n';
    }

    // phone
    if (isEmpty(phone)) {
      error += 'Số điện thoại không được bỏ trống\n';
    }
    if (phone.length > 15) {
      error += 'Số điện thoại vượt quá ký tự tối đa\n';
    }
    if (!phone.toString().match(CommonConst.REGEX_VN_PHONE)) {
      error += 'Số điện thoại sai định dạng\n';
    }

    // email
    if (email && email.length > 25) {
      error += 'Email vượt quá ký tự tối đa\n';
    }
    if (email && !email.toString().match(CommonConst.REGEX_EMAIL)) {
      error += 'Email sai định dạng\n';
    }

    // profileUrl
    if (profileUrl && profileUrl.length > 50) {
      error += 'Link profile vượt quá ký tự tối đa\n';
    }

    // assignee
    if (assignee && assignee.length > 50) {
      error += 'Nhân viên tư vấn vượt quá ký tự tối đa\n';
    }
    if (assignee && typeof assignee != 'string') {
      error += 'Nhân viên tư vấn sai định dạng\n';
    }
    if (assignee && deliverType == DeliverTypeEnum.MANUAL) {
      const employee = await this.employeeRepo.findOne({ code: assignee });
      // const employee = await this.employeeClient.sendDataPromise({ code: assignee }, CmdPatternConst.EMPLOYEE.LISTENER.GET_BY_QUERY);
      // console.log('employee', employee);
      console.log('config', config);
      if (!employee) {
        error += 'Mã nhân viên tư vấn không tồn tại\n';
      }
      const staffIds = config?.orgCharts[0]?.staffIds;
      if (!staffIds.includes(employee[0]?.id)) {
        error += 'Nhân viên tư vấn không nằm trong cấu hình phân bổ\n';
      }
    }

    // note
    if (note && note.length > 50) {
      error += 'Ghi chú vượt quá ký tự tối đa\n';
    }

    return error;
  }

  private async validateEmployee(employee, orgChartGroup: Map<string, string[]>) {
    const pos: any = {};
    console.log('employee', employee);
    if (!isEmpty(employee)) {
      orgChartGroup.forEach((v, k) => {
        if (v.includes(employee.id)) {
          Object.assign(pos, k);
        }
      });

      if (!isEmpty(pos)) {
        return {
          pos
        };
      }
    }

    return {
      error: `Nhân viên ${employee.code} không tồn tại trên sàn chỉ định`,
    };
  }

  private async preProcessLead(lead, repository: LeadRepo, config: LeadRepoConfig, user, timezoneClient, source) {
    this.logger.info('pre-processing lead');

    // generate lead code
    const code = await this.genCodeSrv.generateLeadCode(user);
    if (isEmpty(code)) {
      return this.getResponse('COME0011');
    }
    this.logger.debug('genCode', code);

    const project = !lead.isHot ? config.project : {};
    const result = {
      ...lead,
      repoId: repository.id,
      repoConfigCode: config.code,
      importedBy: {
        id: user?.id,
        name: user?.name,
      },
      orgCode: user?.orgCode,
      orgName: user?.orgName,
      isHot: lead.isHot ?? false,
      id: uuid.v4(),
      type: CommonConst.TYPE.PRIMARY,
      status: StatusEnum.GREEN,
      lifeCycleStatus: LifeCycleStatusEnum.IN_POOL,
      visiblePhone: config.visiblePhone,
      timezoneClient,
      project,
      code,
      source,
      createdBy: user.id,
      modifiedBy: user.id
    };
    const { history } = this.updateExploitHistory(
      result.exploitHistory,
      ExploitEnum.NEW
    );
    Object.assign(result, {
      _id: result.id,
      exploitStatus: ExploitEnum.NEW,
      exploitHistory: history,
    });

    return result;
  }

  private updateConfigData(src: LeadRepoConfig[], code: string, config: LeadRepoConfig) {
    const index = src.findIndex(item => item.code === code);
    if (!code) {
      src = src.concat(config);
    }
    if (code) {
      src = [...src.slice(0, index), config, ...src.slice(index + 1)];
    }
  }

  private getExpireTime(start: Date, duration: number) {
    return new Date(moment(start).valueOf() + duration * 60000);
  }

  /**
   * this function will make sure employeeQueue not empty
   * @param posQueue get org id, employeeQueue from db
   * @param initData get org id, employeeQueue from config
   * @returns 
   */
  private getOrgChart(posQueue: Queue<PosQueueData>, initData: Map<string, any[]>) {
    const orgChart = posQueue.dequeue() as PosQueueData;
    const { employeeQueue, id } = orgChart;
    if (!employeeQueue.isEmpty()) {
      return orgChart;
    }
    if (employeeQueue.isEmpty()) { // employeeQueue from config is empty
      if (this.deliveredToAll(posQueue)) { // employeeQueue from db not empty, reconfig employeeQueue of orgChart and return
        posQueue.data.forEach((item) => {
          const inQueueData = cloneDeep(initData.get(item.id));
          item.employeeQueue.reset(inQueueData);
        });
        const currentData = cloneDeep(initData.get(id));
        orgChart.employeeQueue.reset(currentData);
        return orgChart;
      } else { // if empty, queue the orgChart to last and shift to the next orgChart in queue
        posQueue.enqueue(orgChart);
        return this.getOrgChart(posQueue, initData);
      }
    }
  }

  async createLeadCommon(
    options: any,
    actionName: string,
    timezoneClient?: string,
    user?: any
  ) {
    const { repoConfigCode, repoId, source } = options;
    const { repository, config } = await this.validateRepo(repoId, repoConfigCode);

    const leadSource: any = await this.sourceRepository.findOne({ name: source });
    if (!leadSource) {
      this.sourceRepository.create({ name: source });
    }
    const orgChartGroup = new Map();
    let posId: any;
    if (config.orgCharts?.length) { // NEW: always 1 orgChart in config
      config.orgCharts.forEach(item => {
        posId = item.id;
        orgChartGroup.set({ id: item.id, name: item.name }, item.staffIds);
      });
    }

    const hotLeads = [];
    const normalLeads = [];
    const assignedLeads = [];
    const validateLead = this.validateLead(options);
    this.logger.debug('validateLead', validateLead);
    if (validateLead) {
      return validateLead;
    }
    const lead = await this.preProcessLead(
      options,
      repository,
      config,
      user,
      timezoneClient,
      source,
    );

    let manager: any;
    if (config.deliverType == DeliverTypeEnum.MANUAL && !options?.assignee) {
      manager = await this.employeeRepo.getManagerByPos(posId);
      this.logger.debug('manager', manager);
    }

    // let assignee = null;
    // this.logger.debug('posId', posId);
    // this.logger.debug('options?.assignee', options?.assignee);
    // if (posId && options?.assignee) {
    //   assignee = options?.assignee;
    // }
    // if (posId && !options?.assignee) {
    //   assignee = manager ? manager[0].code : null;
    // }
    let assignee = options?.assignee || null;

    if (!assignee && config.deliverType === DeliverTypeEnum.MANUAL) {
      const manager = await this.employeeRepo.getManagerByPos(posId);
      assignee = manager?.[0]?.code || null;
    }
    this.logger.debug('assignee', assignee);

    if (assignee) {
      const emp = await this.employeeRepo.findOne({ code: assignee });
      const employee = emp[0];
      this.logger.debug('employee', employee.id);
      this.logger.debug('orgChartGroup', orgChartGroup);
      const { error, pos } = await this.validateEmployee(employee, orgChartGroup);
      this.logger.debug(error);
      if (error) {
        return this.getResponse('LEADE0005');
      }

      let exploitStatus = ExploitEnum.ASSIGN;
      if (config.deliverType == DeliverTypeEnum.MANUAL && !options?.assignee) {
        exploitStatus = ExploitEnum.MANUAL_DELIVER;
      }

      // add exploit history 
      const { history, newHistory } = this.updateExploitHistory(
        lead.exploitHistory,
        exploitStatus,
        employee.id,
        {
          id: employee.id,
          name: employee.name,
          phone: employee.phone,
          email: employee.email,
        }
      );

      // add lead
      Object.assign(lead, {
        takeCare: {
          id: employee.id,
          name: employee.name,
          email: employee.email,
          phone: employee.phone,
        },
        assignDuration: config.assignDuration,
        expireTime: this.getExpireTime(
          newHistory?.updatedAt,
          config.assignDuration
        ),
        latestAssignHistory: newHistory,
        exploitStatus,
        exploitHistory: history,
        pos,
        assignedDate: new Date(),
        surveys: config.surveys
      });
      assignedLeads.push(lead);
    } else {
      Object.assign(lead, {
        takeCare: {},
        surveys: config.surveys
      });
      if (lead.isHot) {
        hotLeads.push(lead);
      } else {
        normalLeads.push(lead);
      }
    }

    console.log('lead', lead);
    console.log('hotLeads', hotLeads);
    console.log('normalLeads', normalLeads);

    const { newConfig, result } = await this.leadDelivery(
      hotLeads,
      repository.configHot as LeadRepoConfig
    );

    const records = [...normalLeads, ...assignedLeads];

    if (result.length) {
      const importCommandId = uuid.v4();
      await this.executeCommand(
        Action.IMPORT_LEAD,
        actionName,
        importCommandId,
        result as any[],
        newConfig.notification || {}
      );

      await this.leadRepoDomainSrv.updateLeadRepoMain(
        {
          id: repository.id,
          configHot: newConfig as LeadRepoConfigHot,
          name: repository.name,
        },
        actionName,
        user?.id,
        timezoneClient,
        true
      );
    }

    if (records.length) {
      const importCommandId = uuid.v4();
      await this.executeCommand(
        Action.IMPORT_LEAD,
        actionName,
        importCommandId,
        records,
        config.notification || {}
      );
    }
    // return this.response;
    return this.getResponse('0')
  }

  private async failLeadToExcel(user: any, failedLeads: any) {
    const workbook = new Workbook();
    await workbook.xlsx.readFile(this.errorTemplatePath);

    const worksheet = workbook.getWorksheet(1); // Get the first worksheet

    const startRow = 7;
    // this.logger.debug('sourceSheet', sourceSheet);
    failedLeads.forEach((item, index) => {
      const data = [
        item.data.no || ' ',
        item.data.name || ' ',
        item.data.phone || ' ',
        item.data.email || ' ',
        item.data.profileUrl || ' ',
        item.data.assignee || ' ',
        item.data.note || ' ',
        item.error
      ];

      const row = worksheet.insertRow(
        startRow + index,
        data
      );

      row.eachCell((cell, number) => {
        cell.alignment = { wrapText: false };
        worksheet.getColumn(cell.col).width = 20;

        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      });
    });

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async revokeAssign(dto: RevokeAssignDto, user: any) {
    const { id } = dto;
    const lead = await this.leadQueryRepo.findOne({ id });
    if (!lead) {
      return this.getResponse('LEADE0002');
    }

    if (lead.exploitStatus === ExploitEnum.ASSIGN || lead.exploitStatus === ExploitEnum.PROCESSING) {
      return this.getResponse('LEADE0004');
    }

    const commandId = uuid.v4();

    const { history, newHistory } = this.updateExploitHistory(
      lead.exploitHistory,
      ExploitEnum.MANUAL_DELIVER,
    );
    const commandModel = {
      id: lead.id,
      exploitStatus: ExploitEnum.MANUAL_DELIVER,
      takeCare: null,
      assignDuration: null,
      expireTime: null,
      latestAssignHistory: newHistory,
    } as any;

    await this.executeCommand(
      Action.ASSIGN,
      'Revoke assign',
      commandId,
      commandModel
    );

    return this.getResponse(0);
  }
}
