import { Injectable } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ICommand, Saga, ofType } from '@nestjs/cqrs';
import { LeadEventStreamCreated } from '../events/event-stream-created.evt';
import { CreateLeadQueryCommand } from '../../lead.queryside/commands/impl/create-query.cmd';
import { DeleteLeadQueryCommand } from '../../lead.queryside/commands/impl/delete-query.cmd';
import { LeadHistoryQueryRepository } from '../../lead-history/repository/query.repository';
import { ProcessLeadQueryCommand } from '../../lead.queryside/commands/impl/process-query.cmd';
import { AssignLeadQueryCommand } from '../../lead.queryside/commands/impl/assign-query.cmd';
import { UnprocessLeadQueryCommand } from '../../lead.queryside/commands/impl/unprocess-query.cmd';
import { PendingLeadQueryCommand } from '../../lead.queryside/commands/impl/pending-query.cmd';
import { ReassignLeadQueryCommand } from '../../lead.queryside/commands/impl/reassign-query.cmd';
import { FailLeadQueryCommand } from '../../lead.queryside/commands/impl/fail-query.cmd';
import { ExpireLeadQueryCommand } from '../../lead.queryside/commands/impl/expire-query.cmd';
import { UpdateLeadQueryCommand } from '../../../modules/lead.queryside/commands/impl/update-query.cmd';
import { EmployeeService } from '../../../modules/employee/service';
import { MsxLoggerService } from '../../../modules/logger/logger.service';
import { CommonConst } from '../../../modules/shared/constant/common.const';
import { CmdPatternConst } from '../../../modules/shared/constant/cmd-pattern.const';
import { NotificationClient } from '../../../modules/mgs-sender/notification.client';
import { MailerClient } from '../../../modules/mgs-sender/mailer.client';
import { EmployeeClient } from '../../../modules/mgs-sender/employee.client';
import { SourceEnum } from '../../../modules/shared/enum/source.enum';
import { ImportLeadQueryCommand } from '../../lead.queryside/commands/impl/importLead-query.cmd';
import { RenewLeadQueryCommand } from '../../lead.queryside/commands/impl/renewLead-query.cmd';
import { groupBy, isEmpty } from 'lodash';
import { NotifierClient } from '../../mgs-sender/notifier.client';
import { NotificationInstance } from '../../shared/models/leadRepo/model';
import * as util from 'util';


@Injectable()
export class Sagas {
    private readonly context = Sagas.name;
    constructor(
        private readonly loggerService: MsxLoggerService,
        private readonly leadHistoryQueryRepository: LeadHistoryQueryRepository,
        private readonly notificationClient: NotificationClient,
        private readonly employeeService: EmployeeService,
        private readonly mailerClient: MailerClient,
        private readonly employeeClient: EmployeeClient,
        private readonly notifierClient: NotifierClient
    ) { }

    @Saga()
    querySide = (events$: Observable<any>): Observable<ICommand> => {
        return events$
            .pipe(
                ofType(LeadEventStreamCreated),
                map((event: any) => {
                    this.loggerService.log(this.context, 'Inside [LeadSagas] Saga');
                    const baseEventStream = event.baseEventStream;
                    this.loggerService.log(this.context, 'lead domain | sagas | baseEventStream.streamId ==>', baseEventStream.streamId);

                    let cmd: ICommand;

                    if (baseEventStream.payload?.source === SourceEnum.YeuCau ||
                        baseEventStream.payload?.source === SourceEnum.DangTin) {
                        const data = {
                            model: baseEventStream.payload,
                            action: baseEventStream.payload.eventName
                        };
                        this.mailerClient.sendData(data, CmdPatternConst.SERVICE.LEAD.LISTENER);
                    }
                    switch (baseEventStream.payload?.eventName) {
                        case CommonConst.AGGREGATES.LEAD.CREATED: {
                            const payload = baseEventStream.payload;
                            cmd = new CreateLeadQueryCommand(
                                baseEventStream.messagePattern,
                                baseEventStream.streamId,
                                baseEventStream.payload);
                            const pos: any = payload.pos;
                            if (payload.pos && pos.id) {
                                this.employeeService.getEmployeesAvailable(pos.id).then(rs => {
                                    if (rs.length > 0) {
                                        const msg = {
                                            title: `Trung tâm có một ticket mới.`,
                                            content: `Trung tâm có một ticket mới.`,
                                            entityName: CommonConst.AGGREGATES.LEAD.NAME,
                                            entityId: payload.id,
                                            eventName: payload.eventName
                                        };
                                        const sender = {};
                                        const receivers = rs;
                                        this.notificationClient.sendData({ msg, sender, receivers });
                                    }
                                });

                                if (payload.customerId) {
                                    const msg = {
                                        title: `Yêu cầu tư vấn của bạn đang được xử lý.`,
                                        content: `Chúng tôi đang nhận được yêu cầu của bạn. Nhân viên của chúng tôi đang xử lý thông tin.`,
                                        entityName: CommonConst.AGGREGATES.LEAD.NAME,
                                        entityId: payload.id,
                                        eventName: CommonConst.AGGREGATES.LEAD.CREATED
                                    };
                                    const sender = {};
                                    const receivers = [payload.customerId];
                                    this.notificationClient.sendData({ msg, sender, receivers });
                                }
                            }
                            break;
                        }
                        case CommonConst.AGGREGATES.LEAD.COMPLETED: {
                            cmd = new DeleteLeadQueryCommand(
                                baseEventStream.messagePattern,
                                baseEventStream.streamId,
                                baseEventStream.payload);
                            // save history
                            this.leadHistoryQueryRepository.create(baseEventStream.payload);
                            // // up point
                            // const content: any = {
                            //     action: baseEventStream.payload.eventName,
                            //     userId: baseEventStream.payload.processBy
                            // };
                            // this.employeeClient.sendData(content, CmdPatternConst.LISTENER.EMPLOYEE_UP_POINT);
                            break;
                        }
                        case CommonConst.AGGREGATES.LEAD.FAILED: {
                            cmd = new FailLeadQueryCommand(
                                baseEventStream.messagePattern,
                                baseEventStream.streamId,
                                baseEventStream.payload);
                            // this.leadHistoryQueryRepository.create(baseEventStream.payload);
                            break;
                        }
                        case CommonConst.AGGREGATES.LEAD.PROCESSING:
                            cmd = new ProcessLeadQueryCommand(
                                baseEventStream.messagePattern,
                                baseEventStream.streamId,
                                baseEventStream.payload);
                            break;
                        case CommonConst.AGGREGATES.LEAD.UNPROCESSING:
                            cmd = new UnprocessLeadQueryCommand(
                                baseEventStream.messagePattern,
                                baseEventStream.streamId,
                                baseEventStream.payload);
                            break;
                        case CommonConst.AGGREGATES.LEAD.ASSIGNED:
                            cmd = new AssignLeadQueryCommand(
                                baseEventStream.messagePattern,
                                baseEventStream.streamId,
                                baseEventStream.payload);
                                const content: any = {
                                    action: baseEventStream.payload.eventName,
                                    userId: baseEventStream.payload.processBy
                                };
                                this.employeeClient.sendData(content, CmdPatternConst.LISTENER.EMPLOYEE_UP_POINT);
                            break;
                        case CommonConst.AGGREGATES.LEAD.PENDING:
                            cmd = new PendingLeadQueryCommand(
                                baseEventStream.messagePattern,
                                baseEventStream.streamId,
                                baseEventStream.payload);
                            break;
                        case CommonConst.AGGREGATES.LEAD.REASSIGNED:
                            cmd = new ReassignLeadQueryCommand(
                                baseEventStream.messagePattern,
                                baseEventStream.streamId,
                                baseEventStream.payload);
                            break;
                        case CommonConst.AGGREGATES.LEAD.CHANGE_STATUS:
                        case CommonConst.AGGREGATES.LEAD.UPDATED:
                            cmd = new UpdateLeadQueryCommand(
                                baseEventStream.messagePattern,
                                baseEventStream.streamId,
                                baseEventStream.payload);
                            break;
                        case CommonConst.AGGREGATES.LEAD.EXPIRED:
                            cmd = new ExpireLeadQueryCommand(
                                baseEventStream.messagePattern,
                                baseEventStream.streamId,
                                baseEventStream.payload);
                            break;
                        default:
                            break;
                    }

                    switch (baseEventStream?.eventName) {
                        case CommonConst.AGGREGATES.LEAD.IMPORT_LEAD:
                            var assigned = baseEventStream.payloads.filter(item => !!item.takeCare?.id).map(i => {
                                return {
                                    _id: i.id,
                                    id: i.id,
                                    code: i.code,
                                    createdDate: i.createdDate,
                                    name: i.name,
                                    phone: i.phone,
                                    email: i.email,
                                    isHot: i.isHot,
                                    assignDuration: i.assignDuration,
                                    exploitStatus: i.exploitStatus,
                                    takeCare: i.takeCare,
                                    // 2 below fields are computed, not for saving to db
                                    visiblePhone: i.visiblePhone,
                                    latestAssignHistory: i.latestAssignHistory,
                                    notiUser: i.notiUser,
                                };
                            });
      
                            if (assigned.length) {
                                const notiUser = assigned[0].notiUser;
                                const { email, web, app, sms } = event?.additionalData || {};
    
                                // mail
                                if (!isEmpty(email) && email.active) {
                                    const data = {
                                        content: email.content || 'No content',
                                        title: email.title || 'No title',
                                        target: assigned.map(
                                            (i) => i.takeCare?.email
                                        ),
                                    };
                                    this.mailerClient.sendData(
                                        data,
                                        CmdPatternConst.MAIL.LEAD_ASSIGNED,
                                        notiUser
                                    );
                                }
    
                                // sms
                                if (!isEmpty(sms) && sms.active) {
                                    this.notificationClient.sendSmsLeadAsssigned(
                                        sms.content,
                                        assigned.map((item) => item.takeCare?.phone)
                                    );
                                }
    
                                // real time lead + noti web
                                const leadGroupedByEmployee = groupBy(
                                    assigned,
                                    item => item.takeCare.id
                                );
    
                                Object.keys(leadGroupedByEmployee).forEach(
                                    (key) => {
                                        const leadAssigned =
                                            leadGroupedByEmployee[key];
                                        if (leadAssigned.length) {
                                            this.notifierClient.sendLeadAssinged(
                                                key,
                                                {
                                                    eventName: 'leadAssigned',
                                                    noti: web?.content
                                                        ? web.content
                                                        : '',
                                                    data: leadAssigned,
                                                }
                                            );
                                        }
                                    }
                                );
                            }
    
                            cmd = new ImportLeadQueryCommand(
                                baseEventStream.returnMessagePattern,
                                baseEventStream.streamId,
                                baseEventStream.payloads
                            );
                            break;
                    
                        case CommonConst.AGGREGATES.LEAD.RENEW_LEAD:
                            var assigned = baseEventStream.payloads.filter(item => !!item.takeCare?.id).map(i => {
                                return {
                                    _id: i.id,
                                    id: i.id,
                                    code: i.code,
                                    createdDate: i.createdDate,
                                    name: i.name,
                                    phone: i.phone,
                                    email: i.email,
                                    isHot: i.isHot,
                                    assignDuration: i.assignDuration,
                                    exploitStatus: i.exploitStatus,
                                    takeCare: i.takeCare,
                                    // 2 below fields are computed, not for saving to db
                                    visiblePhone: i.visiblePhone,
                                    latestAssignHistory: i.latestAssignHistory,
                                    notiUser: i.notiUser,
                                };
                            });
        
                            if (assigned.length) {
                                const notiUser = assigned[0].notiUser;
                                const { email, web, app, sms } = event?.additionalData || {};
    
                                // mail
                                if (!isEmpty(email) && email.active) {
                                    const data = {
                                        content: email.content || 'No content',
                                        title: email.title || 'No title',
                                        target: assigned.map(
                                            (i) => i.takeCare?.email
                                        ),
                                    };
                                    this.mailerClient.sendData(
                                        data,
                                        CmdPatternConst.MAIL.LEAD_ASSIGNED,
                                        notiUser
                                    );
                                }
    
                                // sms
                                if (!isEmpty(sms) && sms.active) {
                                    this.notificationClient.sendSmsLeadAsssigned(
                                        sms.content,
                                        assigned.map((item) => item.takeCare?.phone)
                                    );
                                }
    
                                // real time lead + noti web
                                const leadGroupedByEmployee = groupBy(
                                    assigned,
                                    item => item.takeCare.id
                                );
    
                                Object.keys(leadGroupedByEmployee).forEach(
                                    (key) => {
                                        const leadAssigned =
                                            leadGroupedByEmployee[key];
                                        if (leadAssigned.length) {
                                          if (!isEmpty(web) && web.active) {
                                            this.notifierClient.sendLeadAssinged(
                                              key,
                                              {
                                                  eventName: 'leadAssigned',
                                                  noti: web?.content
                                                      ? web.content
                                                      : '',
                                                  data: leadAssigned,
                                              }
                                          );
                                          }
     
                                            if (!isEmpty(app) && app.active) {
                                              const msg = {
                                                title: app?.content || 'Yêu cầu tư vấn',
                                                content: app?.content || 'Bạn vừa được nhận 1 YC tư vấn mới. Hãy tiến hành xử lý',
                                                entityName: CommonConst.AGGREGATES.LEAD.NAME,
                                                entityId: null,
                                                eventName: CommonConst.AGGREGATES.LEAD.RENEW_LEAD
                                            }
                                            const receivers = leadAssigned.map(i => i.takeCare?.id); 
                                            this.notificationClient.sendData({ msg, sender: {}, receivers });
                                            }
                                        }
                                    }
                                );
                            }
    
                            cmd = new RenewLeadQueryCommand(
                                baseEventStream.returnMessagePattern,
                                baseEventStream.streamId,
                                baseEventStream.payloads
                            );
                            break;
                        case CommonConst.AGGREGATES.LEAD.ASSIGN:
                            cmd = new AssignLeadQueryCommand(
                                baseEventStream.messagePattern,
                                baseEventStream.streamId,
                                baseEventStream.payload);
                            break;
                        default:
                            break;
                    }

                    if (baseEventStream.eventName === CommonConst.AGGREGATES.LEAD.UPDATED) {
                        if (baseEventStream.payload?.hasSendSmsAfterSurvey && event.additionalData?.config?.notification?.smsCus?.content) {
                            this.notificationClient.sendSmsAsUser(event.additionalData.userId, event.additionalData.config.notification.smsCus.content, baseEventStream.payload.updatedPhone || baseEventStream.payload.phone);
                        }
                    }

                    return cmd;
                }));
    }

}
