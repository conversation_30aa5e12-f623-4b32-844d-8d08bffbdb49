import { <PERSON>S<PERSON>, <PERSON><PERSON>te, IsEnum, IsNotEmpty, IsO<PERSON>al, <PERSON>ength, ValidateNested, ValidateIf } from 'class-validator';
import { ClassBased } from '../../shared/classes/class-based';
import {
  ILead,
  IExploitHistory,
  ITakeCare,
  IProject,
} from '../../shared/services/lead/interfaces/lead.interface';
import {
  IsStringNotBlank,
  IsLegalLeadStatus,
  IsLegalSex,
  IsLegalMaritalStatus,
  IsLegalIdentify,
  IsLegalSurvey,
  IsSurveyValuesValid,
} from '../../shared/classes/class-validation';
import { ApiModelPropertyOptional, ApiModelProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ExploitEnum } from '../../shared/enum/exploit.enum';
import { ValueDto } from '../../shared/models/leadRepo/model';
import { SurveyTypeEnum } from '../../shared/enum/survey.enum';

export class ExploitHistory implements IExploitHistory {
  @ApiModelPropertyOptional()
  status: ExploitEnum;

  @ApiModelPropertyOptional()
  updatedAt: Date;

  @ApiModelPropertyOptional()
  takeCareId?: string;

  @ApiModelPropertyOptional()
  updatedBy?: string;
}

export class LeadDto extends ClassBased implements ILead {
  customerId: string;
  @ApiModelPropertyOptional()
  id: string;
  @ApiModelPropertyOptional()
  name: string;
  @ApiModelPropertyOptional()
  address: string;
  @ApiModelPropertyOptional()
  phone: string;
  @ApiModelPropertyOptional()
  pos: Object;
  @ApiModelPropertyOptional()
  status: string;
  @ApiModelPropertyOptional()
  processBy: string;
  @ApiModelPropertyOptional()
  type: string;
  @ApiModelPropertyOptional()
  description: string;
  @ApiModelPropertyOptional()
  createdDate: Date;
  @ApiModelPropertyOptional()
  updatedDate: Date;
  @ApiModelPropertyOptional()
  t0: Date;
  @ApiModelPropertyOptional()
  t1: Date;
  @ApiModelPropertyOptional()
  t2: Date;
  @ApiModelPropertyOptional()
  t3: Date;
  @ApiModelPropertyOptional()
  timeOut: Date;
  @ApiModelPropertyOptional()
  email: string;
  @ApiModelPropertyOptional()
  profileUrl: string;
  @ApiModelPropertyOptional()
  lifeCycleStatus: string;
  timezoneclient: string;
  notes: Object[];
  timestamp: number;
  source: string;
  demandCustomer: object;
  property: object;
  processedDate: Date;
  @ApiModelPropertyOptional()
  processedHistory: any[];
  @ApiModelPropertyOptional()
  code: string;
  @ApiModelPropertyOptional()
  isCalled: boolean;
  @ApiModelPropertyOptional()
  advisingType: string;
  assignedDate: Date;

  // new
  updatedName: string;
  updatedPhone: string;
  updatedEmail: string;
  updatedProfileUrl: string;

  isInNeed: string;
  reasonNoNeed: string;
  otherReason: string;
  interestedProduct: Object[];
  direction: Object[];
  needLoan: boolean;
  isAppointment: boolean;
  isVisited: boolean;
  note: string;

  @ApiModelPropertyOptional()
  hasSendSmsAfterSurvey: boolean;

  callHistory: any[];

  callId: string;
  startCall: Date;
  endCall: Date;
  answerTime: number;

  importedBy: Object;

  // Lead repository feature ====================
  exploitStatus: ExploitEnum;
  exploitHistory: IExploitHistory[];
  latestAssignHistory: IExploitHistory;
  exploitStatusModifiedBy: string;
  repoId: string;
  repoConfigCode: string;
  isHot: boolean;
  assignDuration: number;
  visiblePhone: boolean;
  takeCare: ITakeCare;
  project: IProject;
  expireTime: Date;
  countAssign: number;
}

export class CreateLeadDto extends LeadDto {
  @IsString()
  @IsNotEmpty()
  type: string;

  @IsString()
  // @IsNotEmpty()
  address: string;

  // @IsNotEmpty()
  pos: Object;

  recaptcha: string;

  name: string;

  customerId: string;

  timestamp: number;

  source: string;

  customer: object;
  employee: object;
  property: object;
  processedDate: Date;
  code: string; // auto generate code.
  images: Object;
  usedFloorArea: number;
  skipCallHistory: boolean;
  notiUser?: any;
}

export class CreateLeadCommonDto {
  @IsNotEmpty()
  @IsString()
  source: string;

  @IsNotEmpty()
  @IsString()
  repoId: string;

  @IsNotEmpty()
  @IsString()
  repoConfigCode: string;

  @IsOptional()
  @IsString()
  assignee: string;

  @IsNotEmpty()
  @IsString()
  @MaxLength(60)
  name: string;

  @IsNotEmpty()
  @IsString()
  @MaxLength(15)
  phone: string;

  @IsOptional()
  @IsString()
  @MaxLength(25)
  email: string;

  @IsOptional()
  @IsString()
  @MaxLength(50)
  profileUrl: string;

  @IsOptional()
  @IsString()
  @MaxLength(500)
  note: string;
}

export class CreateLeadAdvisingDto extends LeadDto {
  @IsString()
  @IsNotEmpty()
  type: string;
  address: string;
  pos: Object;
  recaptcha: string;
  name: string;
  customerId: string;
  timestamp: number;
  source: string;
  customer: object;
  employee: object;
  property: object;
  processedDate: Date;
  code: string; //auto generate code.
  images: Object;
  advisingType: string;
  usedFloorArea: number;
  skipCallHistory: boolean;
}
export class ImportLeadDemandDto {
  @IsString()
  @IsNotEmpty({ message: 'Vui lòng chọn công ty' })
  companyId: string;
  @IsString()
  // @IsNotEmpty({message: 'Vui lòng chọn sàn giao dịch'})
  exchangeId: string;
  // @IsString()
  posId: string;
  @IsNotEmpty()
  timestamp: number;
  @IsString()
  @IsNotEmpty({ message: 'Vui lòng chọn nguồn' })
  resource: string;
}

export class AssignLeadDto {

  @ApiModelProperty()
  @IsString()
  @Validate(IsStringNotBlank)
  id: string;

  @ApiModelProperty()
  @IsString()
  @Validate(IsStringNotBlank)
  assignFor: string;
}

export class RequestDto {
  id: string;
  reason: string;
  notes: Object[];
}
export class CallHistoryDto {
  id: string;
  callId: string;
  startCall: Date;
  endCall: Date;
  answerTime: number;
}

export class RejectDto {
  id: string;
  causeReject: string[];
}

export class UpdateStatusDto {
  @ApiModelPropertyOptional()
  @IsNotEmpty()
  @IsString()
  id: string;

  @ApiModelPropertyOptional()
  @IsNotEmpty()
  @IsString()
  @Validate(IsLegalLeadStatus)
  status: string;

  @ApiModelPropertyOptional()
  assignDuration: number;
}
class SurveyObj {
  @ApiModelProperty()
  type: string;
  @ApiModelProperty()
  code: string;
  @ApiModelProperty()
  value: string;
  @ApiModelProperty()
  name: string;
}
class IdentifyObj {
  @ApiModelProperty()
  type: string;
  @ApiModelProperty()
  num: string;
  @ApiModelProperty()
  date: string;
  @ApiModelProperty()
  issueBy: string;
}
class AddrObj {
  @ApiModelProperty()
  nation: string;
  @ApiModelProperty()
  province: string;
  @ApiModelProperty()
  district: string;
  @ApiModelProperty()
  ward: string;
}

class Survey {
  @ApiModelPropertyOptional()
  @IsString()
  type: string;

  @ApiModelPropertyOptional()
  @IsString()
  @MaxLength(50)
  name: string;

  @ApiModelPropertyOptional({ type: [ValueDto] })
  @Validate(IsSurveyValuesValid)
  @ValidateNested({ each: true })
  @Type(() => ValueDto)
  values: ValueDto[];

  @ApiModelPropertyOptional()
  @IsOptional()
  @IsString()
  @MaxLength(250)
  text: string;

  @ApiModelPropertyOptional()
  @IsOptional()
  @IsString()
  @MaxLength(500)
  multilineText: string;
}

export class EditLeadDto {
  @ApiModelPropertyOptional()
  @IsNotEmpty()
  @IsString()
  id: string;

  @ApiModelPropertyOptional()
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiModelPropertyOptional()
  @IsString()
  @IsNotEmpty()
  phone: string;

  @ApiModelPropertyOptional()
  @IsOptional()
  email: string;

  @ApiModelPropertyOptional()
  @IsOptional()
  profileUrl: string;

  @ApiModelPropertyOptional()
  @IsOptional()
  note: string;

  @ApiModelPropertyOptional({ type: [Survey] })
  @IsOptional()
  @ValidateNested({ each: true })
  @IsNotEmpty()
  @Type(() => Survey)
  surveys: Survey[];
}

export class UpdateLeadDto {
  @ApiModelPropertyOptional()
  @IsNotEmpty()
  @IsString()
  id: string;

  @ApiModelPropertyOptional()
  @Validate(IsLegalSex)
  sex: string;

  @ApiModelPropertyOptional()
  dob: string;
  @ApiModelPropertyOptional()
  subPhone: string[];
  @ApiModelPropertyOptional()
  email: string;
  @ApiModelPropertyOptional({ type: AddrObj })
  objAddress: Object;
  @ApiModelPropertyOptional()
  major: string;
  @ApiModelPropertyOptional()
  income: string;
  @ApiModelPropertyOptional()
  sourceIncome: string;
  @ApiModelPropertyOptional()
  assignDuration: string;

  @ApiModelPropertyOptional()
  @Validate(IsLegalMaritalStatus)
  maritalStatus: string;

  @ApiModelPropertyOptional({ type: [SurveyObj] })
  @Validate(IsLegalSurvey)
  surveys: Object[];

  @ApiModelPropertyOptional({ type: IdentifyObj })
  @Validate(IsLegalIdentify)
  identification: Object;
  @ApiModelPropertyOptional({ type: Object })
  customer: object;
}

export class ImportLeadFromPublicForm extends LeadDto {
  @IsNotEmpty()
  @ApiModelPropertyOptional()
  phone: string;

  @IsNotEmpty()
  @ApiModelPropertyOptional()
  name: string;

  @IsNotEmpty()
  @ApiModelPropertyOptional()
  repoId: string;

  @ApiModelPropertyOptional()
  repoConfigCode: string;

  @ApiModelPropertyOptional()
  isHot: boolean;

  notiUser?: any;
}

export class ImportLeadAsExcelDto {
  @ApiModelPropertyOptional()
  @IsNotEmpty()
  @IsString()
  repoId: string;

  @ApiModelPropertyOptional()
  @IsNotEmpty()
  @IsString()
  repoConfigCode: string;

  @ApiModelPropertyOptional()
  @IsNotEmpty()
  @IsString()
  source: string;
}

export class DeliverLeadDto {
  @ApiModelPropertyOptional()
  @IsNotEmpty()
  id: string;

  @ApiModelPropertyOptional()
  @IsNotEmpty()
  assignee: string;
}
export class RevokeAssignDto {
  @ApiModelPropertyOptional()
  @IsNotEmpty()
  id: string;
}

