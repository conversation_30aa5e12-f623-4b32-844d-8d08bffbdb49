import { CqrsModule } from "@nestjs/cqrs";
import { <PERSON><PERSON><PERSON>, HttpModule } from "@nestjs/common";
import { CommandHandlers } from "./commands/handlers";
import { EventHandlers } from "./events";
import { Sagas } from "./sagas/sagas";
import { LeadController } from "./controller";
import { LeadDomainService } from "./service";
import { EventStreamRepository } from "./repository/event-stream.repository";
import { DomainDatabaseModule } from "../database/domain/domain.database.module";
import { CqrsProviders } from "./providers/cqrs.domain.providers";
import { LeadQuerySideModule } from "../lead.queryside/module";
import { EmployeeQuerySideModule } from "../employee/module";
import { LeadHistoryQuerySideModule } from "../lead-history/module";
import { LeadPublicController } from "./public-controller";
import { RawModule } from "../raw/module";
import { CustomerLeadController } from "./customer-controller";
import { MgsSenderModule } from "../mgs-sender/mgs-sender.module";
import { LoggerModule } from "../logger/logger.module";
import { CodeGenerateModule } from "../code-generate/module";
import { HistoryImportQuerySideModule } from "../history-import/module";
import { LeadRepoQueryModule } from "../leadRepo.queryside/module";
import { LeadRepoDomainModule } from "../leadRepo.domain/module";
import { LeadDomainServiceExtends } from "./service.extend";
import { LeadSourceQuerySideModule } from "../leadSource/module";
import { SharedModule } from "../../../shared-modules";

@Module({
  imports: [
    CqrsModule,
    DomainDatabaseModule,
    LeadQuerySideModule,
    EmployeeQuerySideModule,
    LeadHistoryQuerySideModule,
    RawModule,
    MgsSenderModule,
    LoggerModule,
    HttpModule,
    CodeGenerateModule,
    HistoryImportQuerySideModule,
    LeadRepoQueryModule,
    LeadRepoDomainModule,
    LeadSourceQuerySideModule,
    SharedModule,
  ],
  controllers: [LeadController, LeadPublicController, CustomerLeadController],
  providers: [
    LeadDomainService,
    EventStreamRepository,

    Sagas,

    ...CqrsProviders,

    ...CommandHandlers,
    ...EventHandlers,

    RawModule,
    LeadDomainServiceExtends,
  ],
  exports: [
    LeadDomainModule,
    LeadDomainService,
    EventStreamRepository,
    LeadDomainServiceExtends,
  ],
})
export class LeadDomainModule {}
