import { Module, OnModuleInit } from '@nestjs/common';
import { RouterModule, Routes } from 'nest-router';
import { LeadDomainModule } from './modules/lead.domain/module';
import { LeadQuerySideModule } from './modules/lead.queryside/module';
import { AuthModule } from './modules/auth/auth.module';
import { AccessControlModule } from 'nest-access-control';
import { acRoles } from './modules/auth/app.roles';
import { EmployeeQuerySideModule } from './modules/employee/module';
import { LeadHistoryQuerySideModule } from './modules/lead-history/module';
import { LeadCareHistoryQuerySideModule } from './modules/leadCare-history/module';
import { ListenerModule } from './modules/listener/listener.module';
import { RawModule } from './modules/raw/module';
import { NotifierClient } from './modules/mgs-sender/notifier.client';
import { StsClient } from './modules/mgs-sender/sts.client';
import { MgsSenderModule } from './modules/mgs-sender/mgs-sender.module';
import { LoggerModule } from './modules/logger/logger.module';
import { LeadRepoDomainModule } from './modules/leadRepo.domain/module';
import { LeadRepoQueryModule } from './modules/leadRepo.queryside/module';
import { LeadRepoCareDomainModule } from "./modules/leadRepoCare.domain/module";
import { LeadRepoCareQueryModule } from "./modules/leadRepoCare.queryside/module";
import { LeadCareDomainModule } from "./modules/leadCare.domain/module";
import { LeadCareQuerySideModule } from "./modules/leadCare.queryside/module";
import { LeadSourceQuerySideModule } from './modules/leadSource/module';
import { LeadjobModule } from "./modules/leadJob/module";
import { CustomConfigModule } from "../shared-modules";
import { OrgchartModule } from './modules/orgchart/module';

const routes: Routes = [
  { path: '/domain', module: LeadDomainModule },
  { path: '/query', module: LeadQuerySideModule },
  { path: '/domain', module: LeadCareDomainModule },
  { path: '/query', module: LeadCareQuerySideModule },
  { path: '/query', module: EmployeeQuerySideModule },
  { path: '/query', module: LeadHistoryQuerySideModule },
  { path: '/query', module: LeadCareHistoryQuerySideModule },
  { path: '/domain', module: RawModule },
  { path: '/domain', module: LeadRepoDomainModule },
  { path: '/query', module: LeadRepoQueryModule },
  { path: '/domain', module: LeadRepoCareDomainModule },
  { path: '/query', module: LeadRepoCareQueryModule },
  { path: '/query', module: LeadSourceQuerySideModule },
  { path: '/query', module: OrgchartModule },
  { path: '', module: LeadjobModule },
];

@Module({
  imports: [
    RouterModule.forRoutes(routes), // setup the routes
    // AccessControlModule.forRoles(roles),
    AccessControlModule.forRoles(acRoles),
    CustomConfigModule,

    AuthModule,
    ListenerModule,

    LeadDomainModule,
    LeadQuerySideModule,
    LeadCareDomainModule,
    LeadCareQuerySideModule,
    EmployeeQuerySideModule,
    LeadHistoryQuerySideModule,
    LeadCareHistoryQuerySideModule,

    LeadRepoDomainModule,
    LeadRepoQueryModule,

    LeadRepoCareDomainModule,
    LeadRepoCareQueryModule,
    LeadSourceQuerySideModule,

    RawModule,

    MgsSenderModule,
    LoggerModule,
    LeadjobModule,
    OrgchartModule
  ],
})
export class ApplicationModule implements OnModuleInit {

  constructor(
    private readonly stsClient: StsClient,
    private readonly notifierClient: NotifierClient
  ) { }

  onModuleInit() {
    this.notifierClient.setupNotify();
    this.stsClient.setupPermission();
  }
}
