{"name": "msx-lead", "version": "1.0.0", "description": "msx lead", "license": "MIT", "scripts": {"dev": "cross-env NODE_ENV=local nest start --watch", "start:watch": "nodemon -e ts --watch main.js", "start:live": "nodemon -e ts main.js", "build": "tsc -p tsconfig.json", "start": "cross-env NODE_ENV=local nest start", "start:prod": "cross-env NODE_ENV=production node dist/src/main", "start:docker": "cross-env NODE_ENV=docker ts-node src/main", "test": "jest --config=jest.json", "test:watch": "jest --watch --config=jest.json", "test:coverage": "jest --config=jest.json --coverage --coverageDirectory=coverage", "e2e": "jest --config=e2e/jest-e2e.json --forceExit", "e2e:watch": "jest --watch --config=e2e/jest-e2e.json"}, "dependencies": {"@nestjs/axios": "^4.0.0", "@nestjs/common": "6.11.11", "@nestjs/config": "0.5.0", "@nestjs/core": "6.11.11", "@nestjs/cqrs": "6.0.1", "@nestjs/microservices": "6.11.11", "@nestjs/passport": "6.1.0", "@nestjs/platform-express": "6.11.11", "@nestjs/swagger": "3.0.2", "@nestjs/testing": "6.11.11", "@types/express": "4.17.13", "@types/uuid": "3.4.5", "amqp-connection-manager": "3.0.0", "amqplib": "0.5.3", "axios": "^1.7.9", "bluebird": "^3.7.2", "body-parser": "1.19.0", "cache-manager": "2.9.1", "cache-manager-redis-store": "1.5.0", "class-transformer": "0.2.3", "class-validator": "0.9.1", "content-filter": "1.1.2", "cors": "2.8.5", "cross-env": "5.2.0", "dotenv": "8.0.0", "exceljs": "^4.2.1", "frisby": "2.1.2", "helmet": "3.20.0", "jsonwebtoken": "8.5.1", "lodash": "4.17.11", "log4js": "^6.9.1", "moment": "2.24.0", "moment-timezone": "0.5.25", "mongoose": "5.6.2", "mongoose-beautiful-unique-validation": "7.1.1", "nest-access-control": "2.0.1", "nest-router": "1.0.9", "object-mapper": "5.0.0", "passport": "0.4.0", "passport-http-bearer": "1.0.1", "passport-jwt": "4.0.0", "passport-local": "1.0.0", "redis": "2.8.0", "reflect-metadata": "0.1.13", "rxjs": "6.5.2", "swagger-ui-express": "4.0.7", "uuid": "3.3.2", "xlsx": "0.15.5"}, "devDependencies": {"@compodoc/compodoc": "1.1.10", "@types/babel__traverse": "7.18.2", "@types/bson": "1", "@types/jest": "24.0.15", "@types/lodash": "4.14.168", "@types/mongoose": "5.5.7", "@types/node": "12.0.10", "jest": "24.8.0", "mongodb": "3.2.7", "nodemon": "1.19.1", "supertest": "4.0.2", "ts-jest": "24.0.2", "ts-node": "8.3.0", "tslint": "5.18.0", "typescript": "4.2.3"}, "resolutions": {"cheerio": "1.0.0-rc.12", "send": "0.19.0", "@types/mongodb": "3.6.20", "@types/babel__traverse": "7.14.2", "@types/bluebird": "3.5.36", "pretty-format": "26.6.2", "@types/express-serve-static-core": "4.17.30", "@types/express": "4.17.13", "jest-diff": "24.9.0", "redis": "2.8.0", "@types/mime": "2.0.3", "bson": "1.1.6"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}