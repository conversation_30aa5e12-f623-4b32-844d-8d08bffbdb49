FROM registry.fke.fptcloud.com/1a0a2532-3924-4691-a28a-73190afbed2e/ihz/msx/node:v1.0.1

# Create app directory
RUN mkdir -p /usr/src/app
WORKDIR /usr/src/app

# Bundle app source
COPY package.json /usr/src/app/
COPY tsconfig.json /usr/src/app/
COPY ./src /usr/src/app/src
COPY ./shared-modules /usr/src/app/shared-modules

# Install app dependencies
RUN yarn
RUN yarn build
COPY . /usr/src/app/dist
# RUN npm install --no-optional

# COPY ./certs/mycert.pem /usr/src/app/certs/mycert.pem
# COPY ./certs/privateKey.pem /usr/src/app/certs/privateKey.pem

COPY wait-for-it.sh /usr/src/app/
RUN chmod +x ./wait-for-it.sh


EXPOSE 3052
# CMD [ "npm", "start" ]
# CMD [ "npm", "run", "start:prod" ]
CMD [ "yarn", "start:prod" ]
# CMD ["./wait-for-it.sh", "msx-sts:3200", "rabbitmq:15672",  "-t", "120","--", "yarn", "start:prod"]