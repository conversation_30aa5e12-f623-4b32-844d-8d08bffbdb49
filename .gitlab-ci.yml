stages:
  - prebuild
  - build
  - deploy

variables:
  DOCKER_IMAGE: "registry.fke.fptcloud.com/************************************/ihz"
  DOCKER_REGISTRY: "gitlab.fis.vn"
  DOCKER_TAG: $CI_COMMIT_SHORT_SHA
  KUBECONFIG_PATH: "/tmp/kubeconfig"
  GIT_SUBMODULE_STRATEGY: recursive
  DOCKER_HOST: "tcp://************:2375"


docker-login:
  stage: prebuild
  image: 
    name: alpine/git:2.45.2
    entrypoint: [""]
  tags:
    - build
    - crm
  script:
    - apk add --no-cache git
    # - git clone https://hant5:$<EMAIL>/dxg/crm/backend/shared-modules.git
    - git --version
    - git config --global url."https://hant5:$<EMAIL>".insteadOf "https://gitlab.com"
    
    - git submodule sync --recursive
    - git submodule update --init --recursive
  only: 
    - develop

# Define job to build the Docker image
build-push:
  stage: build
  image: public.ecr.aws/docker/library/docker:26  # Use a Docker image with the client installed
  tags:
    - build
    - crm
  # services:
  #   - docker:24.0.5-dind  # Enable Docker-in-Docker
  #$************************************+dev
  variables:
    DOCKER_TLS_CERTDIR: "/certs"
    SERVICE_NAME: "lead"
    # ROBOT: "robot\$"
  script:
    - echo "Logging into Docker fci registry..."
    #Login to registry
    - echo  $FCI_REGISTRY_TOKEN
    - docker login registry.fke.fptcloud.com -u 'robot$************************************+dev' -p "$FCI_REGISTRY_TOKEN" 
    - docker build -t "$DOCKER_IMAGE/$SERVICE_NAME:dev_$DOCKER_TAG" .
    - echo "Pushing Docker image..."
    - echo "$CI_REGISTRY_PASSWORD" | docker login "$DOCKER_REGISTRY" -u "$CI_REGISTRY_USER" --password-stdin 
    - docker push "$DOCKER_IMAGE/$SERVICE_NAME:dev_$DOCKER_TAG"
  only:
    - develop