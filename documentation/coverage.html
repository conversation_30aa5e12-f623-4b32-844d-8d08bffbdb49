<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-lead documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="./images/favicon.ico">
	      <link rel="stylesheet" href="./styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="./" class="navbar-brand">msx-lead documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content coverage">
                   <div class="content-data">














                   


<ol class="breadcrumb">
    <li>Documentation coverage</li>
</ol>

<div>
    <img src="./images/coverage-badge-documentation.svg">
</div>

<table class="table table-bordered coverage" id="coverage-table">
    <thead class="coverage-header">
        <tr>
            <th>File</th>
            <th>Type</th>
            <th>Identifier</th>
            <th style="text-align:right" class="statements" data-sort-default>Statements</th>
        </tr>
    </thead>
    <tbody>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#Public">src/common/decorators/public.decorator.ts</a>
            </td>
            <td>variable</td>
            <td>Public</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#Roles">src/common/decorators/roles.decorator.ts</a>
            </td>
            <td>variable</td>
            <td>Roles</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/HttpExceptionFilter.html">src/common/filters/http-exception.filter.ts</a>
            </td>
            <td>class</td>
            <td>HttpExceptionFilter</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ExceptionFilter.html">src/common/filters/rpc-exception.filter.ts</a>
            </td>
            <td>class</td>
            <td>ExceptionFilter</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./guards/AuthClientKeyGuard.html">src/common/guards/auth.client-key.guard.ts</a>
            </td>
            <td>guard</td>
            <td>AuthClientKeyGuard</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/CustomAuthGuard.html">src/common/guards/auth.guard.ts</a>
            </td>
            <td>injectable</td>
            <td>CustomAuthGuard</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./guards/RolesGuard.html">src/common/guards/roles.guard.ts</a>
            </td>
            <td>guard</td>
            <td>RolesGuard</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/ErrorsInterceptor.html">src/common/interceptors/exception.interceptor.ts</a>
            </td>
            <td>injectable</td>
            <td>ErrorsInterceptor</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LoggingInterceptor.html">src/common/interceptors/logging.interceptor.ts</a>
            </td>
            <td>injectable</td>
            <td>LoggingInterceptor</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/TimeoutInterceptor.html">src/common/interceptors/timeout.interceptor.ts</a>
            </td>
            <td>injectable</td>
            <td>TimeoutInterceptor</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/ParseIntPipe.html">src/common/pipes/parse-int.pipe.ts</a>
            </td>
            <td>injectable</td>
            <td>ParseIntPipe</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/ValidationPipe.html">src/common/pipes/validation.pipe.ts</a>
            </td>
            <td>injectable</td>
            <td>ValidationPipe</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/CryptographerService.html">src/common/services/cryptographer.service.ts</a>
            </td>
            <td>injectable</td>
            <td>CryptographerService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href=".//functions.html#bootstrap">src/main.ts</a>
            </td>
            <td>function</td>
            <td>bootstrap</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#configService">src/main.ts</a>
            </td>
            <td>variable</td>
            <td>configService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#acRoles">src/modules/auth/app.roles.ts</a>
            </td>
            <td>variable</td>
            <td>acRoles</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/AuthService.html">src/modules/auth/auth.service.ts</a>
            </td>
            <td>injectable</td>
            <td>AuthService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/JwtPayload.html">src/modules/auth/interfaces/jwt-payload.interface.ts</a>
            </td>
            <td>interface</td>
            <td>JwtPayload</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/JwtStrategy.html">src/modules/auth/passport/jwt.strategy.ts</a>
            </td>
            <td>injectable</td>
            <td>JwtStrategy</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#callback">src/modules/auth/passport/jwt.strategy.ts</a>
            </td>
            <td>variable</td>
            <td>callback</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ICLeadDocument.html">src/modules/c-lead.queryside/interfaces/document.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ICLeadDocument</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QueryProviders">src/modules/c-lead.queryside/providers/query.cqrs.providers.ts</a>
            </td>
            <td>variable</td>
            <td>QueryProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/CLeadQueryRepository.html">src/modules/c-lead.queryside/repository/query.repository.ts</a>
            </td>
            <td>injectable</td>
            <td>CLeadQueryRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QuerySchema">src/modules/c-lead.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>QuerySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ICLeadCareDocument.html">src/modules/c-leadCare.queryside/interfaces/document.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ICLeadCareDocument</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QueryProviders">src/modules/c-leadCare.queryside/providers/query.cqrs.providers.ts</a>
            </td>
            <td>variable</td>
            <td>QueryProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/CLeadCareQueryRepository.html">src/modules/c-leadCare.queryside/repository/query.repository.ts</a>
            </td>
            <td>injectable</td>
            <td>CLeadCareQueryRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QuerySchema">src/modules/c-leadCare.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>QuerySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ICodeGenerateDocument.html">src/modules/code-generate/interfaces/document.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ICodeGenerateDocument</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QueryProviders">src/modules/code-generate/providers/query.cqrs.providers.ts</a>
            </td>
            <td>variable</td>
            <td>QueryProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/CodeGenerateRepository.html">src/modules/code-generate/repository/query.repository.ts</a>
            </td>
            <td>injectable</td>
            <td>CodeGenerateRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QuerySchema">src/modules/code-generate/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>QuerySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/CodeGenerateService.html">src/modules/code-generate/service.ts</a>
            </td>
            <td>injectable</td>
            <td>CodeGenerateService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/ConfigService.html">src/modules/config/config.service.ts</a>
            </td>
            <td>injectable</td>
            <td>ConfigService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#domainDatabaseProviders">src/modules/database/domain/domain.database.providers.ts</a>
            </td>
            <td>variable</td>
            <td>domainDatabaseProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#instance">src/modules/database/domain/domain.database.providers.ts</a>
            </td>
            <td>variable</td>
            <td>instance</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#Mongoose">src/modules/database/domain/domain.database.providers.ts</a>
            </td>
            <td>variable</td>
            <td>Mongoose</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#instance">src/modules/database/query/query.database.providers.ts</a>
            </td>
            <td>variable</td>
            <td>instance</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#Mongoose">src/modules/database/query/query.database.providers.ts</a>
            </td>
            <td>variable</td>
            <td>Mongoose</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#queryDatabaseProviders">src/modules/database/query/query.database.providers.ts</a>
            </td>
            <td>variable</td>
            <td>queryDatabaseProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/EmployeeController.html">src/modules/employee/controller.ts</a>
            </td>
            <td>controller</td>
            <td>EmployeeController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IEmployeeDocument.html">src/modules/employee/interfaces/document.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IEmployeeDocument</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/15)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QueryProviders">src/modules/employee/providers/query.cqrs.providers.ts</a>
            </td>
            <td>variable</td>
            <td>QueryProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/EmployeeQueryRepository.html">src/modules/employee/repository/query.repository.ts</a>
            </td>
            <td>injectable</td>
            <td>EmployeeQueryRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/17)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QuerySchema">src/modules/employee/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>QuerySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/EmployeeService.html">src/modules/employee/service.ts</a>
            </td>
            <td>injectable</td>
            <td>EmployeeService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/15)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IHistoryImportDocument.html">src/modules/history-import/interfaces/document.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IHistoryImportDocument</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/10)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QueryProviders">src/modules/history-import/providers/query.cqrs.providers.ts</a>
            </td>
            <td>variable</td>
            <td>QueryProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/HistoryImportQueryRepository.html">src/modules/history-import/repository/query.repository.ts</a>
            </td>
            <td>injectable</td>
            <td>HistoryImportQueryRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QuerySchema">src/modules/history-import/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>QuerySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/HistoryImportService.html">src/modules/history-import/service.ts</a>
            </td>
            <td>injectable</td>
            <td>HistoryImportService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/LeadHistoryController.html">src/modules/lead-history/controller.ts</a>
            </td>
            <td>controller</td>
            <td>LeadHistoryController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ILeadHistoryDocument.html">src/modules/lead-history/interfaces/document.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ILeadHistoryDocument</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/20)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QueryProviders">src/modules/lead-history/providers/query.cqrs.providers.ts</a>
            </td>
            <td>variable</td>
            <td>QueryProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LeadHistoryQueryRepository.html">src/modules/lead-history/repository/query.repository.ts</a>
            </td>
            <td>injectable</td>
            <td>LeadHistoryQueryRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/18)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#moment">src/modules/lead-history/repository/query.repository.ts</a>
            </td>
            <td>variable</td>
            <td>moment</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QuerySchema">src/modules/lead-history/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>QuerySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LeadHistoryService.html">src/modules/lead-history/service.ts</a>
            </td>
            <td>injectable</td>
            <td>LeadHistoryService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/9)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AssignCommandHandler.html">src/modules/lead.domain/commands/handlers/assign.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>AssignCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ChangeStatusCommandHandler.html">src/modules/lead.domain/commands/handlers/changeStatus.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>ChangeStatusCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CompleteCommandHandler.html">src/modules/lead.domain/commands/handlers/complete.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CompleteCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateCommandHandler.html">src/modules/lead.domain/commands/handlers/create.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CreateCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ExpiredCommandHandler.html">src/modules/lead.domain/commands/handlers/expired.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>ExpiredCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/FailCommandHandler.html">src/modules/lead.domain/commands/handlers/fail.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>FailCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ImportLeadCommandHandler.html">src/modules/lead.domain/commands/handlers/importLead.handler.ts</a>
            </td>
            <td>class</td>
            <td>ImportLeadCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CommandHandlers">src/modules/lead.domain/commands/handlers/index.ts</a>
            </td>
            <td>variable</td>
            <td>CommandHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/PendingCommandHandler.html">src/modules/lead.domain/commands/handlers/pending.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>PendingCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ProcessCommandHandler.html">src/modules/lead.domain/commands/handlers/process.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>ProcessCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ReassignCommandHandler.html">src/modules/lead.domain/commands/handlers/reassign.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>ReassignCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/RenewLeadCommandHandler.html">src/modules/lead.domain/commands/handlers/renewLead.handler.ts</a>
            </td>
            <td>class</td>
            <td>RenewLeadCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UnprocessCommandHandler.html">src/modules/lead.domain/commands/handlers/unprocess.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>UnprocessCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateCommandHandler.html">src/modules/lead.domain/commands/handlers/update.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>UpdateCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AssignLeadCommand.html">src/modules/lead.domain/commands/impl/assign.cmd.ts</a>
            </td>
            <td>class</td>
            <td>AssignLeadCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ChangeStatusLeadCommand.html">src/modules/lead.domain/commands/impl/changeStatus.cmd.ts</a>
            </td>
            <td>class</td>
            <td>ChangeStatusLeadCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CompleteLeadCommand.html">src/modules/lead.domain/commands/impl/complete.cmd.ts</a>
            </td>
            <td>class</td>
            <td>CompleteLeadCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateLeadCommand.html">src/modules/lead.domain/commands/impl/create.cmd.ts</a>
            </td>
            <td>class</td>
            <td>CreateLeadCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ExpiredLeadCommand.html">src/modules/lead.domain/commands/impl/expired.cmd.ts</a>
            </td>
            <td>class</td>
            <td>ExpiredLeadCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/FailLeadCommand.html">src/modules/lead.domain/commands/impl/fail.cmd.ts</a>
            </td>
            <td>class</td>
            <td>FailLeadCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ImportLeadCommand.html">src/modules/lead.domain/commands/impl/importLead.cmd.ts</a>
            </td>
            <td>class</td>
            <td>ImportLeadCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/PendingLeadCommand.html">src/modules/lead.domain/commands/impl/pending.cmd.ts</a>
            </td>
            <td>class</td>
            <td>PendingLeadCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ProcessLeadCommand.html">src/modules/lead.domain/commands/impl/process.cmd.ts</a>
            </td>
            <td>class</td>
            <td>ProcessLeadCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ReassignLeadCommand.html">src/modules/lead.domain/commands/impl/reassign.cmd.ts</a>
            </td>
            <td>class</td>
            <td>ReassignLeadCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/RenewLeadCommand.html">src/modules/lead.domain/commands/impl/renewLead.cmd.ts</a>
            </td>
            <td>class</td>
            <td>RenewLeadCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UnprocessLeadCommand.html">src/modules/lead.domain/commands/impl/unprocess.cmd.ts</a>
            </td>
            <td>class</td>
            <td>UnprocessLeadCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateLeadCommand.html">src/modules/lead.domain/commands/impl/update.cmd.ts</a>
            </td>
            <td>class</td>
            <td>UpdateLeadCommand</td>
            <td align="right" data-sort="16">
                <span class="coverage-percent">16 %</span>
                <span class="coverage-count">(1/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/LeadController.html">src/modules/lead.domain/controller.ts</a>
            </td>
            <td>controller</td>
            <td>LeadController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/21)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/CustomerLeadController.html">src/modules/lead.domain/customer-controller.ts</a>
            </td>
            <td>controller</td>
            <td>CustomerLeadController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AddrObj.html">src/modules/lead.domain/dto/lead.dto.ts</a>
            </td>
            <td>class</td>
            <td>AddrObj</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AssignLeadDto.html">src/modules/lead.domain/dto/lead.dto.ts</a>
            </td>
            <td>class</td>
            <td>AssignLeadDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CallHistoryDto.html">src/modules/lead.domain/dto/lead.dto.ts</a>
            </td>
            <td>class</td>
            <td>CallHistoryDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateLeadAdvisingDto.html">src/modules/lead.domain/dto/lead.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateLeadAdvisingDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/89)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateLeadDto.html">src/modules/lead.domain/dto/lead.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateLeadDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/89)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeliverLeadDto.html">src/modules/lead.domain/dto/lead.dto.ts</a>
            </td>
            <td>class</td>
            <td>DeliverLeadDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ExploitHistory.html">src/modules/lead.domain/dto/lead.dto.ts</a>
            </td>
            <td>class</td>
            <td>ExploitHistory</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/IdentifyObj.html">src/modules/lead.domain/dto/lead.dto.ts</a>
            </td>
            <td>class</td>
            <td>IdentifyObj</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ImportLeadAsExcelDto.html">src/modules/lead.domain/dto/lead.dto.ts</a>
            </td>
            <td>class</td>
            <td>ImportLeadAsExcelDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ImportLeadDemandDto.html">src/modules/lead.domain/dto/lead.dto.ts</a>
            </td>
            <td>class</td>
            <td>ImportLeadDemandDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ImportLeadFromPublicForm.html">src/modules/lead.domain/dto/lead.dto.ts</a>
            </td>
            <td>class</td>
            <td>ImportLeadFromPublicForm</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/78)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadDto.html">src/modules/lead.domain/dto/lead.dto.ts</a>
            </td>
            <td>class</td>
            <td>LeadDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/69)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/RejectDto.html">src/modules/lead.domain/dto/lead.dto.ts</a>
            </td>
            <td>class</td>
            <td>RejectDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/RequestDto.html">src/modules/lead.domain/dto/lead.dto.ts</a>
            </td>
            <td>class</td>
            <td>RequestDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/SurveyObj.html">src/modules/lead.domain/dto/lead.dto.ts</a>
            </td>
            <td>class</td>
            <td>SurveyObj</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateLeadDto.html">src/modules/lead.domain/dto/lead.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateLeadDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/15)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateStatusDto.html">src/modules/lead.domain/dto/lead.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateStatusDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/EventStreamCreatedEventHandler.html">src/modules/lead.domain/events/event-stream-created.evt.handler.ts</a>
            </td>
            <td>class</td>
            <td>EventStreamCreatedEventHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadEventStreamCreated.html">src/modules/lead.domain/events/event-stream-created.evt.ts</a>
            </td>
            <td>class</td>
            <td>LeadEventStreamCreated</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#EventHandlers">src/modules/lead.domain/events/index.ts</a>
            </td>
            <td>variable</td>
            <td>EventHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AggregateModel.html">src/modules/lead.domain/models/aggregate.model.ts</a>
            </td>
            <td>class</td>
            <td>AggregateModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CqrsProviders">src/modules/lead.domain/providers/cqrs.domain.providers.ts</a>
            </td>
            <td>variable</td>
            <td>CqrsProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/LeadPublicController.html">src/modules/lead.domain/public-controller.ts</a>
            </td>
            <td>controller</td>
            <td>LeadPublicController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/EventStreamRepository.html">src/modules/lead.domain/repository/event-stream.repository.ts</a>
            </td>
            <td>injectable</td>
            <td>EventStreamRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/Sagas.html">src/modules/lead.domain/sagas/sagas.ts</a>
            </td>
            <td>injectable</td>
            <td>Sagas</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#addressSchema">src/modules/lead.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>addressSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#beautifyUnique">src/modules/lead.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>beautifyUnique</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#categorySchema">src/modules/lead.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>categorySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CqrsDomainSchema">src/modules/lead.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>CqrsDomainSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#ExploitHistorySchema">src/modules/lead.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>ExploitHistorySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#identifySchema">src/modules/lead.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>identifySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#payloadSchema">src/modules/lead.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>payloadSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#surveySchema">src/modules/lead.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>surveySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#TakeCareSchema">src/modules/lead.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>TakeCareSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#uuid">src/modules/lead.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>uuid</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LeadDomainServiceExtends.html">src/modules/lead.domain/service.extend.ts</a>
            </td>
            <td>injectable</td>
            <td>LeadDomainServiceExtends</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/23)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/PosQueueData.html">src/modules/lead.domain/service.extend.ts</a>
            </td>
            <td>interface</td>
            <td>PosQueueData</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#XLSX">src/modules/lead.domain/service.extend.ts</a>
            </td>
            <td>variable</td>
            <td>XLSX</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LeadDomainService.html">src/modules/lead.domain/service.ts</a>
            </td>
            <td>injectable</td>
            <td>LeadDomainService</td>
            <td align="right" data-sort="3">
                <span class="coverage-percent">3 %</span>
                <span class="coverage-count">(1/33)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#request">src/modules/lead.domain/service.ts</a>
            </td>
            <td>variable</td>
            <td>request</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#timezone">src/modules/lead.domain/service.ts</a>
            </td>
            <td>variable</td>
            <td>timezone</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#uuid">src/modules/lead.domain/service.ts</a>
            </td>
            <td>variable</td>
            <td>uuid</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#XLSX">src/modules/lead.domain/service.ts</a>
            </td>
            <td>variable</td>
            <td>XLSX</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AssignQueryCommandHandler.html">src/modules/lead.queryside/commands/handlers/assign.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>AssignQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateQueryCommandHandler.html">src/modules/lead.queryside/commands/handlers/create.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CreateQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteQueryCommandHandler.html">src/modules/lead.queryside/commands/handlers/delete.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>DeleteQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ExpireQueryCommandHandler.html">src/modules/lead.queryside/commands/handlers/expire.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>ExpireQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/FailQueryCommandHandler.html">src/modules/lead.queryside/commands/handlers/fail.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>FailQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ImportLeadQueryCommandHandler.html">src/modules/lead.queryside/commands/handlers/importLead.handler.ts</a>
            </td>
            <td>class</td>
            <td>ImportLeadQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CommandHandlers">src/modules/lead.queryside/commands/handlers/index.ts</a>
            </td>
            <td>variable</td>
            <td>CommandHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/PendingQueryCommandHandler.html">src/modules/lead.queryside/commands/handlers/pending.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>PendingQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ProcessQueryCommandHandler.html">src/modules/lead.queryside/commands/handlers/process.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>ProcessQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ReassignQueryCommandHandler.html">src/modules/lead.queryside/commands/handlers/reassign.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>ReassignQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/RenewLeadQueryCommandHandler.html">src/modules/lead.queryside/commands/handlers/renewLead.handler.ts</a>
            </td>
            <td>class</td>
            <td>RenewLeadQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UnprocessQueryCommandHandler.html">src/modules/lead.queryside/commands/handlers/unprocess.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>UnprocessQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateQueryCommandHandler.html">src/modules/lead.queryside/commands/handlers/update.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>UpdateQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AssignLeadQueryCommand.html">src/modules/lead.queryside/commands/impl/assign-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>AssignLeadQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateLeadQueryCommand.html">src/modules/lead.queryside/commands/impl/create-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>CreateLeadQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteLeadQueryCommand.html">src/modules/lead.queryside/commands/impl/delete-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>DeleteLeadQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ExpireLeadQueryCommand.html">src/modules/lead.queryside/commands/impl/expire-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>ExpireLeadQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/FailLeadQueryCommand.html">src/modules/lead.queryside/commands/impl/fail-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>FailLeadQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ImportLeadQueryCommand.html">src/modules/lead.queryside/commands/impl/importLead-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>ImportLeadQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/PendingLeadQueryCommand.html">src/modules/lead.queryside/commands/impl/pending-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>PendingLeadQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ProcessLeadQueryCommand.html">src/modules/lead.queryside/commands/impl/process-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>ProcessLeadQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ReassignLeadQueryCommand.html">src/modules/lead.queryside/commands/impl/reassign-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>ReassignLeadQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/RenewLeadQueryCommand.html">src/modules/lead.queryside/commands/impl/renewLead-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>RenewLeadQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UnprocessLeadQueryCommand.html">src/modules/lead.queryside/commands/impl/unprocess-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>UnprocessLeadQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateLeadQueryCommand.html">src/modules/lead.queryside/commands/impl/update-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>UpdateLeadQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/LeadController.html">src/modules/lead.queryside/controller.ts</a>
            </td>
            <td>controller</td>
            <td>LeadController</td>
            <td align="right" data-sort="11">
                <span class="coverage-percent">11 %</span>
                <span class="coverage-count">(3/27)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/CustomerLeadController.html">src/modules/lead.queryside/customer-controller.ts</a>
            </td>
            <td>controller</td>
            <td>CustomerLeadController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#EventHandlers">src/modules/lead.queryside/events/index.ts</a>
            </td>
            <td>variable</td>
            <td>EventHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadAssignedQueryEvent.html">src/modules/lead.queryside/events/query-assigned.evt.ts</a>
            </td>
            <td>class</td>
            <td>LeadAssignedQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadCreatedQueryEvent.html">src/modules/lead.queryside/events/query-created.evt.ts</a>
            </td>
            <td>class</td>
            <td>LeadCreatedQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadDeletedQueryEvent.html">src/modules/lead.queryside/events/query-deleted.evt.ts</a>
            </td>
            <td>class</td>
            <td>LeadDeletedQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadExpiredQueryEvent.html">src/modules/lead.queryside/events/query-expired.evt.ts</a>
            </td>
            <td>class</td>
            <td>LeadExpiredQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadFailedQueryEvent.html">src/modules/lead.queryside/events/query-failed.evt.ts</a>
            </td>
            <td>class</td>
            <td>LeadFailedQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ImportLeadQueryEvent.html">src/modules/lead.queryside/events/query-importLead.evt.ts</a>
            </td>
            <td>class</td>
            <td>ImportLeadQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ImportLeadQueryEventHandler.html">src/modules/lead.queryside/events/query-importLead.handler.ts</a>
            </td>
            <td>class</td>
            <td>ImportLeadQueryEventHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadPendingQueryEvent.html">src/modules/lead.queryside/events/query-pending.evt.ts</a>
            </td>
            <td>class</td>
            <td>LeadPendingQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadProcessingQueryEvent.html">src/modules/lead.queryside/events/query-processing.evt.ts</a>
            </td>
            <td>class</td>
            <td>LeadProcessingQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadReassignedQueryEvent.html">src/modules/lead.queryside/events/query-reassigned.evt.ts</a>
            </td>
            <td>class</td>
            <td>LeadReassignedQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/RenewLeadQueryEvent.html">src/modules/lead.queryside/events/query-renewLead.evt.ts</a>
            </td>
            <td>class</td>
            <td>RenewLeadQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/RenewLeadQueryEventHandler.html">src/modules/lead.queryside/events/query-renewLead.handler.ts</a>
            </td>
            <td>class</td>
            <td>RenewLeadQueryEventHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadUnprocessingQueryEvent.html">src/modules/lead.queryside/events/query-unprocessing.evt.ts</a>
            </td>
            <td>class</td>
            <td>LeadUnprocessingQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadUpdateQueryEvent.html">src/modules/lead.queryside/events/query-update.evt.ts</a>
            </td>
            <td>class</td>
            <td>LeadUpdateQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/QueryEventHandler.html">src/modules/lead.queryside/events/query.evt.handler.ts</a>
            </td>
            <td>class</td>
            <td>QueryEventHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/14)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ILeadDocument.html">src/modules/lead.queryside/interfaces/document.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ILeadDocument</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/QueryAggregateModel.html">src/modules/lead.queryside/models/query-aggregate.model.ts</a>
            </td>
            <td>class</td>
            <td>QueryAggregateModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/14)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QueryProviders">src/modules/lead.queryside/providers/query.cqrs.providers.ts</a>
            </td>
            <td>variable</td>
            <td>QueryProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/PublicLeadController.html">src/modules/lead.queryside/public.controller.ts</a>
            </td>
            <td>controller</td>
            <td>PublicLeadController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/QueryRepository.html">src/modules/lead.queryside/repository/query.repository.ts</a>
            </td>
            <td>injectable</td>
            <td>QueryRepository</td>
            <td align="right" data-sort="1">
                <span class="coverage-percent">1 %</span>
                <span class="coverage-count">(1/63)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#validReportStatus">src/modules/lead.queryside/repository/query.repository.ts</a>
            </td>
            <td>variable</td>
            <td>validReportStatus</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#addressSchema">src/modules/lead.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>addressSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#categorySchema">src/modules/lead.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>categorySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#ExploitHistorySchema">src/modules/lead.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>ExploitHistorySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#identifySchema">src/modules/lead.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>identifySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QuerySchema">src/modules/lead.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>QuerySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#surveySchema">src/modules/lead.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>surveySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#TakeCareSchema">src/modules/lead.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>TakeCareSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LeadService.html">src/modules/lead.queryside/service.ts</a>
            </td>
            <td>injectable</td>
            <td>LeadService</td>
            <td align="right" data-sort="4">
                <span class="coverage-percent">4 %</span>
                <span class="coverage-count">(2/41)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#moment">src/modules/lead.queryside/service.ts</a>
            </td>
            <td>variable</td>
            <td>moment</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#momentTz">src/modules/lead.queryside/service.ts</a>
            </td>
            <td>variable</td>
            <td>momentTz</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#timezone">src/modules/lead.queryside/service.ts</a>
            </td>
            <td>variable</td>
            <td>timezone</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#validListStatus">src/modules/lead.queryside/service.ts</a>
            </td>
            <td>variable</td>
            <td>validListStatus</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#validReportStatus">src/modules/lead.queryside/service.ts</a>
            </td>
            <td>variable</td>
            <td>validReportStatus</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/LeadCareHistoryController.html">src/modules/leadCare-history/controller.ts</a>
            </td>
            <td>controller</td>
            <td>LeadCareHistoryController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ILeadCareHistoryDocument.html">src/modules/leadCare-history/interfaces/document.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ILeadCareHistoryDocument</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/20)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QueryProviders">src/modules/leadCare-history/providers/query.cqrs.providers.ts</a>
            </td>
            <td>variable</td>
            <td>QueryProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LeadCareHistoryQueryRepository.html">src/modules/leadCare-history/repository/query.repository.ts</a>
            </td>
            <td>injectable</td>
            <td>LeadCareHistoryQueryRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/18)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#moment">src/modules/leadCare-history/repository/query.repository.ts</a>
            </td>
            <td>variable</td>
            <td>moment</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QuerySchema">src/modules/leadCare-history/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>QuerySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LeadCareHistoryService.html">src/modules/leadCare-history/service.ts</a>
            </td>
            <td>injectable</td>
            <td>LeadCareHistoryService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/9)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CareAssignCommandHandler.html">src/modules/leadCare.domain/commands/handlers/assign.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CareAssignCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CareChangeStatusCommandHandler.html">src/modules/leadCare.domain/commands/handlers/changeStatus.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CareChangeStatusCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CareCompleteCommandHandler.html">src/modules/leadCare.domain/commands/handlers/complete.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CareCompleteCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CareCreateCommandHandler.html">src/modules/leadCare.domain/commands/handlers/create.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CareCreateCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CareCustomerCloseTicketCommandHandler.html">src/modules/leadCare.domain/commands/handlers/customerClosedTicket.cmd.handlers.ts</a>
            </td>
            <td>class</td>
            <td>CareCustomerCloseTicketCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CareExpiredCommandHandler.html">src/modules/leadCare.domain/commands/handlers/expired.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CareExpiredCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CareFailCommandHandler.html">src/modules/leadCare.domain/commands/handlers/fail.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CareFailCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CareImportLeadCareCommandHandler.html">src/modules/leadCare.domain/commands/handlers/importLead.handler.ts</a>
            </td>
            <td>class</td>
            <td>CareImportLeadCareCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CommandHandlers">src/modules/leadCare.domain/commands/handlers/index.ts</a>
            </td>
            <td>variable</td>
            <td>CommandHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CarePendingCommandHandler.html">src/modules/leadCare.domain/commands/handlers/pending.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CarePendingCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CareProcessCommandHandler.html">src/modules/leadCare.domain/commands/handlers/process.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CareProcessCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CareReassignCommandHandler.html">src/modules/leadCare.domain/commands/handlers/reassign.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CareReassignCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CareUnprocessCommandHandler.html">src/modules/leadCare.domain/commands/handlers/unprocess.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CareUnprocessCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CareUpdateCommandHandler.html">src/modules/leadCare.domain/commands/handlers/update.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CareUpdateCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AssignLeadCareCommand.html">src/modules/leadCare.domain/commands/impl/assign.cmd.ts</a>
            </td>
            <td>class</td>
            <td>AssignLeadCareCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ChangeStatusLeadCareCommand.html">src/modules/leadCare.domain/commands/impl/changeStatus.cmd.ts</a>
            </td>
            <td>class</td>
            <td>ChangeStatusLeadCareCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CompleteLeadCareCommand.html">src/modules/leadCare.domain/commands/impl/complete.cmd.ts</a>
            </td>
            <td>class</td>
            <td>CompleteLeadCareCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateLeadCareCommand.html">src/modules/leadCare.domain/commands/impl/create.cmd.ts</a>
            </td>
            <td>class</td>
            <td>CreateLeadCareCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CustomerClosedTicketCareCommand.html">src/modules/leadCare.domain/commands/impl/customerClosedTicket.cmd.ts</a>
            </td>
            <td>class</td>
            <td>CustomerClosedTicketCareCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ExpiredLeadCareCommand.html">src/modules/leadCare.domain/commands/impl/expired.cmd.ts</a>
            </td>
            <td>class</td>
            <td>ExpiredLeadCareCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/FailLeadCareCommand.html">src/modules/leadCare.domain/commands/impl/fail.cmd.ts</a>
            </td>
            <td>class</td>
            <td>FailLeadCareCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ImportLeadCareCommand.html">src/modules/leadCare.domain/commands/impl/importLead.cmd.ts</a>
            </td>
            <td>class</td>
            <td>ImportLeadCareCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/PendingLeadCareCommand.html">src/modules/leadCare.domain/commands/impl/pending.cmd.ts</a>
            </td>
            <td>class</td>
            <td>PendingLeadCareCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ProcessLeadCareCommand.html">src/modules/leadCare.domain/commands/impl/process.cmd.ts</a>
            </td>
            <td>class</td>
            <td>ProcessLeadCareCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ReassignLeadCareCommand.html">src/modules/leadCare.domain/commands/impl/reassign.cmd.ts</a>
            </td>
            <td>class</td>
            <td>ReassignLeadCareCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UnprocessLeadCareCommand.html">src/modules/leadCare.domain/commands/impl/unprocess.cmd.ts</a>
            </td>
            <td>class</td>
            <td>UnprocessLeadCareCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateLeadCareCommand.html">src/modules/leadCare.domain/commands/impl/update.cmd.ts</a>
            </td>
            <td>class</td>
            <td>UpdateLeadCareCommand</td>
            <td align="right" data-sort="20">
                <span class="coverage-percent">20 %</span>
                <span class="coverage-count">(1/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/LeadCareController.html">src/modules/leadCare.domain/controller.ts</a>
            </td>
            <td>controller</td>
            <td>LeadCareController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/23)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/CustomerLeadCareController.html">src/modules/leadCare.domain/customer-controller.ts</a>
            </td>
            <td>controller</td>
            <td>CustomerLeadCareController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AddrObj.html">src/modules/leadCare.domain/dto/leadCare.dto.ts</a>
            </td>
            <td>class</td>
            <td>AddrObj</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AssignLeadCareDto.html">src/modules/leadCare.domain/dto/leadCare.dto.ts</a>
            </td>
            <td>class</td>
            <td>AssignLeadCareDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CallHistoryDto.html">src/modules/leadCare.domain/dto/leadCare.dto.ts</a>
            </td>
            <td>class</td>
            <td>CallHistoryDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ConversationDto.html">src/modules/leadCare.domain/dto/leadCare.dto.ts</a>
            </td>
            <td>class</td>
            <td>ConversationDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateLeadCareAdvisingDto.html">src/modules/leadCare.domain/dto/leadCare.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateLeadCareAdvisingDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/81)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateLeadCareDto.html">src/modules/leadCare.domain/dto/leadCare.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateLeadCareDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/80)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateServiceRequestDto.html">src/modules/leadCare.domain/dto/leadCare.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateServiceRequestDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateServiceRequestPublicDto.html">src/modules/leadCare.domain/dto/leadCare.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateServiceRequestPublicDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/10)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ExploitHistory.html">src/modules/leadCare.domain/dto/leadCare.dto.ts</a>
            </td>
            <td>class</td>
            <td>ExploitHistory</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/HandleTransferRequestDto.html">src/modules/leadCare.domain/dto/leadCare.dto.ts</a>
            </td>
            <td>class</td>
            <td>HandleTransferRequestDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/IdentifyObj.html">src/modules/leadCare.domain/dto/leadCare.dto.ts</a>
            </td>
            <td>class</td>
            <td>IdentifyObj</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ImportLeadCareAsExcelDto.html">src/modules/leadCare.domain/dto/leadCare.dto.ts</a>
            </td>
            <td>class</td>
            <td>ImportLeadCareAsExcelDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ImportLeadCareDemandDto.html">src/modules/leadCare.domain/dto/leadCare.dto.ts</a>
            </td>
            <td>class</td>
            <td>ImportLeadCareDemandDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ImportLeadCareFromPublicForm.html">src/modules/leadCare.domain/dto/leadCare.dto.ts</a>
            </td>
            <td>class</td>
            <td>ImportLeadCareFromPublicForm</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/70)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadCareDto.html">src/modules/leadCare.domain/dto/leadCare.dto.ts</a>
            </td>
            <td>class</td>
            <td>LeadCareDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/62)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/RejectDto.html">src/modules/leadCare.domain/dto/leadCare.dto.ts</a>
            </td>
            <td>class</td>
            <td>RejectDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/RejectLeadCareDto.html">src/modules/leadCare.domain/dto/leadCare.dto.ts</a>
            </td>
            <td>class</td>
            <td>RejectLeadCareDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/RequestDto.html">src/modules/leadCare.domain/dto/leadCare.dto.ts</a>
            </td>
            <td>class</td>
            <td>RequestDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/SurveyObj.html">src/modules/leadCare.domain/dto/leadCare.dto.ts</a>
            </td>
            <td>class</td>
            <td>SurveyObj</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/TakeSurveyLeadCareDto.html">src/modules/leadCare.domain/dto/leadCare.dto.ts</a>
            </td>
            <td>class</td>
            <td>TakeSurveyLeadCareDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateLeadCareDto.html">src/modules/leadCare.domain/dto/leadCare.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateLeadCareDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/14)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateStatusAssignDto.html">src/modules/leadCare.domain/dto/leadCare.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateStatusAssignDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateStatusDto.html">src/modules/leadCare.domain/dto/leadCare.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateStatusDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CareEventStreamCreatedEventHandler.html">src/modules/leadCare.domain/events/event-stream-created.evt.handler.ts</a>
            </td>
            <td>class</td>
            <td>CareEventStreamCreatedEventHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadCareEventStreamCreated.html">src/modules/leadCare.domain/events/event-stream-created.evt.ts</a>
            </td>
            <td>class</td>
            <td>LeadCareEventStreamCreated</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#EventHandlers">src/modules/leadCare.domain/events/index.ts</a>
            </td>
            <td>variable</td>
            <td>EventHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AggregateModel.html">src/modules/leadCare.domain/models/aggregate.model.ts</a>
            </td>
            <td>class</td>
            <td>AggregateModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CqrsProviders">src/modules/leadCare.domain/providers/cqrs.domain.providers.ts</a>
            </td>
            <td>variable</td>
            <td>CqrsProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/LeadCarePublicController.html">src/modules/leadCare.domain/public-controller.ts</a>
            </td>
            <td>controller</td>
            <td>LeadCarePublicController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/EventStreamRepository.html">src/modules/leadCare.domain/repository/event-stream.repository.ts</a>
            </td>
            <td>injectable</td>
            <td>EventStreamRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/CareSagas.html">src/modules/leadCare.domain/sagas/sagas.ts</a>
            </td>
            <td>injectable</td>
            <td>CareSagas</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#addressSchema">src/modules/leadCare.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>addressSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#beautifyUnique">src/modules/leadCare.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>beautifyUnique</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#categorySchema">src/modules/leadCare.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>categorySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CqrsDomainSchema">src/modules/leadCare.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>CqrsDomainSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#ExploitHistorySchema">src/modules/leadCare.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>ExploitHistorySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#identifySchema">src/modules/leadCare.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>identifySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#payloadSchema">src/modules/leadCare.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>payloadSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#surveySchema">src/modules/leadCare.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>surveySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#TakeCareSchema">src/modules/leadCare.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>TakeCareSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#uuid">src/modules/leadCare.domain/schemas/cqrs.domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>uuid</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LeadCareDomainServiceExtends.html">src/modules/leadCare.domain/service.extend.ts</a>
            </td>
            <td>injectable</td>
            <td>LeadCareDomainServiceExtends</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/26)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/PosQueueData.html">src/modules/leadCare.domain/service.extend.ts</a>
            </td>
            <td>interface</td>
            <td>PosQueueData</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#XLSX">src/modules/leadCare.domain/service.extend.ts</a>
            </td>
            <td>variable</td>
            <td>XLSX</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LeadCareDomainService.html">src/modules/leadCare.domain/service.ts</a>
            </td>
            <td>injectable</td>
            <td>LeadCareDomainService</td>
            <td align="right" data-sort="2">
                <span class="coverage-percent">2 %</span>
                <span class="coverage-count">(1/41)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#request">src/modules/leadCare.domain/service.ts</a>
            </td>
            <td>variable</td>
            <td>request</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#timezone">src/modules/leadCare.domain/service.ts</a>
            </td>
            <td>variable</td>
            <td>timezone</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#uuid">src/modules/leadCare.domain/service.ts</a>
            </td>
            <td>variable</td>
            <td>uuid</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#XLSX">src/modules/leadCare.domain/service.ts</a>
            </td>
            <td>variable</td>
            <td>XLSX</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CareAssignQueryCommandHandler.html">src/modules/leadCare.queryside/commands/handlers/assign.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CareAssignQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CareCreateQueryCommandHandler.html">src/modules/leadCare.queryside/commands/handlers/create.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CareCreateQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CustomerClosedTicketCareLeadQueryCommandHandler.html">src/modules/leadCare.queryside/commands/handlers/customerClosedTiket.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CustomerClosedTicketCareLeadQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CareDeleteQueryCommandHandler.html">src/modules/leadCare.queryside/commands/handlers/delete.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CareDeleteQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CareExpireQueryCommandHandler.html">src/modules/leadCare.queryside/commands/handlers/expire.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CareExpireQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CareFailQueryCommandHandler.html">src/modules/leadCare.queryside/commands/handlers/fail.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CareFailQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CareImportLeadCareQueryCommandHandler.html">src/modules/leadCare.queryside/commands/handlers/importLead.handler.ts</a>
            </td>
            <td>class</td>
            <td>CareImportLeadCareQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CommandHandlers">src/modules/leadCare.queryside/commands/handlers/index.ts</a>
            </td>
            <td>variable</td>
            <td>CommandHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CarePendingQueryCommandHandler.html">src/modules/leadCare.queryside/commands/handlers/pending.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CarePendingQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CareProcessQueryCommandHandler.html">src/modules/leadCare.queryside/commands/handlers/process.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CareProcessQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CareReassignQueryCommandHandler.html">src/modules/leadCare.queryside/commands/handlers/reassign.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CareReassignQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CareUnprocessQueryCommandHandler.html">src/modules/leadCare.queryside/commands/handlers/unprocess.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CareUnprocessQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CareUpdateQueryCommandHandler.html">src/modules/leadCare.queryside/commands/handlers/update.cmd.handler.ts</a>
            </td>
            <td>class</td>
            <td>CareUpdateQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/AssignLeadCareQueryCommand.html">src/modules/leadCare.queryside/commands/impl/assign-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>AssignLeadCareQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateLeadCareQueryCommand.html">src/modules/leadCare.queryside/commands/impl/create-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>CreateLeadCareQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CustomerClosedTicketLeadCareQueryCommand.html">src/modules/leadCare.queryside/commands/impl/customerClosedTiket-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>CustomerClosedTicketLeadCareQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DeleteLeadCareQueryCommand.html">src/modules/leadCare.queryside/commands/impl/delete-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>DeleteLeadCareQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ExpireLeadCareQueryCommand.html">src/modules/leadCare.queryside/commands/impl/expire-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>ExpireLeadCareQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/FailLeadCareQueryCommand.html">src/modules/leadCare.queryside/commands/impl/fail-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>FailLeadCareQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ImportLeadCareQueryCommand.html">src/modules/leadCare.queryside/commands/impl/importLead-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>ImportLeadCareQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/PendingLeadCareQueryCommand.html">src/modules/leadCare.queryside/commands/impl/pending-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>PendingLeadCareQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ProcessLeadCareQueryCommand.html">src/modules/leadCare.queryside/commands/impl/process-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>ProcessLeadCareQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ReassignLeadCareQueryCommand.html">src/modules/leadCare.queryside/commands/impl/reassign-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>ReassignLeadCareQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UnprocessLeadCareQueryCommand.html">src/modules/leadCare.queryside/commands/impl/unprocess-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>UnprocessLeadCareQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateLeadCareQueryCommand.html">src/modules/leadCare.queryside/commands/impl/update-query.cmd.ts</a>
            </td>
            <td>class</td>
            <td>UpdateLeadCareQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/LeadCareController.html">src/modules/leadCare.queryside/controller.ts</a>
            </td>
            <td>controller</td>
            <td>LeadCareController</td>
            <td align="right" data-sort="10">
                <span class="coverage-percent">10 %</span>
                <span class="coverage-count">(3/29)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/CustomerLeadCareController.html">src/modules/leadCare.queryside/customer-controller.ts</a>
            </td>
            <td>controller</td>
            <td>CustomerLeadCareController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#EventHandlers">src/modules/leadCare.queryside/events/index.ts</a>
            </td>
            <td>variable</td>
            <td>EventHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadCareAssignedQueryEvent.html">src/modules/leadCare.queryside/events/query-assigned.evt.ts</a>
            </td>
            <td>class</td>
            <td>LeadCareAssignedQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadCareCreatedQueryEvent.html">src/modules/leadCare.queryside/events/query-created.evt.ts</a>
            </td>
            <td>class</td>
            <td>LeadCareCreatedQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CustomerClosedTicketQueryEvent.html">src/modules/leadCare.queryside/events/query-customerClosed.evt.ts</a>
            </td>
            <td>class</td>
            <td>CustomerClosedTicketQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadCareDeletedQueryEvent.html">src/modules/leadCare.queryside/events/query-deleted.evt.ts</a>
            </td>
            <td>class</td>
            <td>LeadCareDeletedQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadCareExpiredQueryEvent.html">src/modules/leadCare.queryside/events/query-expired.evt.ts</a>
            </td>
            <td>class</td>
            <td>LeadCareExpiredQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadCareFailedQueryEvent.html">src/modules/leadCare.queryside/events/query-failed.evt.ts</a>
            </td>
            <td>class</td>
            <td>LeadCareFailedQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ImportLeadCareQueryEvent.html">src/modules/leadCare.queryside/events/query-importLead.evt.ts</a>
            </td>
            <td>class</td>
            <td>ImportLeadCareQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ImportLeadCareQueryEventHandler.html">src/modules/leadCare.queryside/events/query-importLead.handler.ts</a>
            </td>
            <td>class</td>
            <td>ImportLeadCareQueryEventHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadCarePendingQueryEvent.html">src/modules/leadCare.queryside/events/query-pending.evt.ts</a>
            </td>
            <td>class</td>
            <td>LeadCarePendingQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadCareProcessingQueryEvent.html">src/modules/leadCare.queryside/events/query-processing.evt.ts</a>
            </td>
            <td>class</td>
            <td>LeadCareProcessingQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadCareReassignedQueryEvent.html">src/modules/leadCare.queryside/events/query-reassigned.evt.ts</a>
            </td>
            <td>class</td>
            <td>LeadCareReassignedQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadCareUnprocessingQueryEvent.html">src/modules/leadCare.queryside/events/query-unprocessing.evt.ts</a>
            </td>
            <td>class</td>
            <td>LeadCareUnprocessingQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadCareUpdateQueryEvent.html">src/modules/leadCare.queryside/events/query-update.evt.ts</a>
            </td>
            <td>class</td>
            <td>LeadCareUpdateQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CareQueryEventHandler.html">src/modules/leadCare.queryside/events/query.evt.handler.ts</a>
            </td>
            <td>class</td>
            <td>CareQueryEventHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/15)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ILeadCareDocument.html">src/modules/leadCare.queryside/interfaces/document.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ILeadCareDocument</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/QueryAggregateModel.html">src/modules/leadCare.queryside/models/query-aggregate.model.ts</a>
            </td>
            <td>class</td>
            <td>QueryAggregateModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/14)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QueryProviders">src/modules/leadCare.queryside/providers/query.cqrs.providers.ts</a>
            </td>
            <td>variable</td>
            <td>QueryProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/PublicLeadCareController.html">src/modules/leadCare.queryside/public.controller.ts</a>
            </td>
            <td>controller</td>
            <td>PublicLeadCareController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/QueryRepository.html">src/modules/leadCare.queryside/repository/query.repository.ts</a>
            </td>
            <td>injectable</td>
            <td>QueryRepository</td>
            <td align="right" data-sort="1">
                <span class="coverage-percent">1 %</span>
                <span class="coverage-count">(1/66)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#clc">src/modules/leadCare.queryside/repository/query.repository.ts</a>
            </td>
            <td>variable</td>
            <td>clc</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#addressSchema">src/modules/leadCare.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>addressSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#categorySchema">src/modules/leadCare.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>categorySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#ExploitHistorySchema">src/modules/leadCare.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>ExploitHistorySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#identifySchema">src/modules/leadCare.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>identifySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QuerySchema">src/modules/leadCare.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>QuerySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#surveySchema">src/modules/leadCare.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>surveySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#TakeCareSchema">src/modules/leadCare.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>TakeCareSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LeadCareService.html">src/modules/leadCare.queryside/service.ts</a>
            </td>
            <td>injectable</td>
            <td>LeadCareService</td>
            <td align="right" data-sort="5">
                <span class="coverage-percent">5 %</span>
                <span class="coverage-count">(2/40)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#moment">src/modules/leadCare.queryside/service.ts</a>
            </td>
            <td>variable</td>
            <td>moment</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#timezone">src/modules/leadCare.queryside/service.ts</a>
            </td>
            <td>variable</td>
            <td>timezone</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LeadjobAuthService.html">src/modules/leadJob/application/service.auth.ts</a>
            </td>
            <td>injectable</td>
            <td>LeadjobAuthService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#uuid">src/modules/leadJob/application/service.auth.ts</a>
            </td>
            <td>variable</td>
            <td>uuid</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LeadjobService.html">src/modules/leadJob/application/service.ts</a>
            </td>
            <td>injectable</td>
            <td>LeadjobService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/17)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#momentTz">src/modules/leadJob/application/service.ts</a>
            </td>
            <td>variable</td>
            <td>momentTz</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#uuid">src/modules/leadJob/application/service.ts</a>
            </td>
            <td>variable</td>
            <td>uuid</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LeadjobExportRepository.html">src/modules/leadJob/export/repository.ts</a>
            </td>
            <td>injectable</td>
            <td>LeadjobExportRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LeadjobExportService.html">src/modules/leadJob/export/service.ts</a>
            </td>
            <td>injectable</td>
            <td>LeadjobExportService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#LeadjobProviders">src/modules/leadJob/infra/providers.ts</a>
            </td>
            <td>variable</td>
            <td>LeadjobProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LeadjobRepository.html">src/modules/leadJob/infra/repository.ts</a>
            </td>
            <td>injectable</td>
            <td>LeadjobRepository</td>
            <td align="right" data-sort="78">
                <span class="coverage-percent">78 %</span>
                <span class="coverage-count">(11/14)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#clc">src/modules/leadJob/infra/repository.ts</a>
            </td>
            <td>variable</td>
            <td>clc</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#beautifyUnique">src/modules/leadJob/infra/schema.ts</a>
            </td>
            <td>variable</td>
            <td>beautifyUnique</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#ExploitHistorySchema">src/modules/leadJob/infra/schema.ts</a>
            </td>
            <td>variable</td>
            <td>ExploitHistorySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#LeadjobSchema">src/modules/leadJob/infra/schema.ts</a>
            </td>
            <td>variable</td>
            <td>LeadjobSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#TakeCareSchema">src/modules/leadJob/infra/schema.ts</a>
            </td>
            <td>variable</td>
            <td>TakeCareSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IExploitHistory.html">src/modules/leadJob/interfaces/base.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IExploitHistory</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ILeadjob.html">src/modules/leadJob/interfaces/base.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ILeadjob</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/32)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IProject.html">src/modules/leadJob/interfaces/base.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IProject</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ITakeCare.html">src/modules/leadJob/interfaces/base.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ITakeCare</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ILeadjobDocument.html">src/modules/leadJob/interfaces/document.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ILeadjobDocument</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/LeadjobAdminController.html">src/modules/leadJob/web/admin-controller.ts</a>
            </td>
            <td>controller</td>
            <td>LeadjobAdminController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/13)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/LeadjobAuthController.html">src/modules/leadJob/web/auth-controller.ts</a>
            </td>
            <td>controller</td>
            <td>LeadjobAuthController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/BQLCreateLeadjobRequestDto.html">src/modules/leadJob/web/dto/request.dto.ts</a>
            </td>
            <td>class</td>
            <td>BQLCreateLeadjobRequestDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/54)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateLeadjobRequestDto.html">src/modules/leadJob/web/dto/request.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateLeadjobRequestDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/59)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ExploitHistory.html">src/modules/leadJob/web/dto/request.dto.ts</a>
            </td>
            <td>class</td>
            <td>ExploitHistory</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ImportLeadCareFromPublicForm.html">src/modules/leadJob/web/dto/request.dto.ts</a>
            </td>
            <td>class</td>
            <td>ImportLeadCareFromPublicForm</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/47)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadjobRequestDto.html">src/modules/leadJob/web/dto/request.dto.ts</a>
            </td>
            <td>class</td>
            <td>LeadjobRequestDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/39)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateLeadjobRequestDto.html">src/modules/leadJob/web/dto/request.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateLeadjobRequestDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/43)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadjobPagingResponseDto.html">src/modules/leadJob/web/dto/response.dto.ts</a>
            </td>
            <td>class</td>
            <td>LeadjobPagingResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadjobResponseDto.html">src/modules/leadJob/web/dto/response.dto.ts</a>
            </td>
            <td>class</td>
            <td>LeadjobResponseDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/40)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/LeadjobPublicController.html">src/modules/leadJob/web/public-controller.ts</a>
            </td>
            <td>controller</td>
            <td>LeadjobPublicController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ActiveConfigLeadRepoCommandHandler.html">src/modules/leadRepo.domain/commands/handlers/activeConfig.handler.ts</a>
            </td>
            <td>class</td>
            <td>ActiveConfigLeadRepoCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/BulkUpdateLeadRepoCommandHandler.html">src/modules/leadRepo.domain/commands/handlers/bulkUpdate.handler.ts</a>
            </td>
            <td>class</td>
            <td>BulkUpdateLeadRepoCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateLeadRepoCommandHandler.html">src/modules/leadRepo.domain/commands/handlers/create.handler.ts</a>
            </td>
            <td>class</td>
            <td>CreateLeadRepoCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#LeadRepoDomainCommandHandlers">src/modules/leadRepo.domain/commands/handlers/index.ts</a>
            </td>
            <td>variable</td>
            <td>LeadRepoDomainCommandHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateLeadRepoCommandHandler.html">src/modules/leadRepo.domain/commands/handlers/update.handler.ts</a>
            </td>
            <td>class</td>
            <td>UpdateLeadRepoCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateConfigLeadRepoCommandHandler.html">src/modules/leadRepo.domain/commands/handlers/updateConfig.handler.ts</a>
            </td>
            <td>class</td>
            <td>UpdateConfigLeadRepoCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ActiveConfigLeadRepoCommand.html">src/modules/leadRepo.domain/commands/impl/activeConfig.ts</a>
            </td>
            <td>class</td>
            <td>ActiveConfigLeadRepoCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/BulkUpdateLeadRepoCommand.html">src/modules/leadRepo.domain/commands/impl/bulkUpdate.cmd.ts</a>
            </td>
            <td>class</td>
            <td>BulkUpdateLeadRepoCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateLeadRepoCommand.html">src/modules/leadRepo.domain/commands/impl/create.cmd.ts</a>
            </td>
            <td>class</td>
            <td>CreateLeadRepoCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateLeadRepoCommand.html">src/modules/leadRepo.domain/commands/impl/update.cmd.ts</a>
            </td>
            <td>class</td>
            <td>UpdateLeadRepoCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateConfigLeadRepoCommand.html">src/modules/leadRepo.domain/commands/impl/updateConfig.ts</a>
            </td>
            <td>class</td>
            <td>UpdateConfigLeadRepoCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/LeadRepoDomainController.html">src/modules/leadRepo.domain/controller.ts</a>
            </td>
            <td>controller</td>
            <td>LeadRepoDomainController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ActiveLeadRepoConfigDto.html">src/modules/leadRepo.domain/dto/domain.dto.ts</a>
            </td>
            <td>class</td>
            <td>ActiveLeadRepoConfigDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateLeadRepoDto.html">src/modules/leadRepo.domain/dto/domain.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateLeadRepoDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateLeadRepoConfig.html">src/modules/leadRepo.domain/dto/domain.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateLeadRepoConfig</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/18)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateLeadRepoConfigDto.html">src/modules/leadRepo.domain/dto/domain.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateLeadRepoConfigDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateLeadRepoMainDto.html">src/modules/leadRepo.domain/dto/domain.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateLeadRepoMainDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/13)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadRepoEventStream.html">src/modules/leadRepo.domain/events/domain.event.ts</a>
            </td>
            <td>class</td>
            <td>LeadRepoEventStream</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadRepoEventStreamHandler.html">src/modules/leadRepo.domain/events/domain.eventHandler.ts</a>
            </td>
            <td>class</td>
            <td>LeadRepoEventStreamHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ILeadRepoEventStreamDocument.html">src/modules/leadRepo.domain/interfaces/document.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ILeadRepoEventStreamDocument</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadRepoDomainModel.html">src/modules/leadRepo.domain/models/domain.model.ts</a>
            </td>
            <td>class</td>
            <td>LeadRepoDomainModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#LeadRepoDomainProvider">src/modules/leadRepo.domain/providers/domain.provider.ts</a>
            </td>
            <td>variable</td>
            <td>LeadRepoDomainProvider</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LeadRepoDomainRepository.html">src/modules/leadRepo.domain/repositories/domain.repository.ts</a>
            </td>
            <td>injectable</td>
            <td>LeadRepoDomainRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/16)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LeadRepoDomainSagas.html">src/modules/leadRepo.domain/sagas/domain.sagas.ts</a>
            </td>
            <td>injectable</td>
            <td>LeadRepoDomainSagas</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#beautifyUnique">src/modules/leadRepo.domain/schemas/domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>beautifyUnique</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#DomainSchema">src/modules/leadRepo.domain/schemas/domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>DomainSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#PayloadSchema">src/modules/leadRepo.domain/schemas/domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>PayloadSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LeadRepoDomainService.html">src/modules/leadRepo.domain/service.ts</a>
            </td>
            <td>injectable</td>
            <td>LeadRepoDomainService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/19)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/BulkUpdateLeadRepoQueryCommandHandler.html">src/modules/leadRepo.queryside/command/handlers/bulkUpdate.handler.ts</a>
            </td>
            <td>class</td>
            <td>BulkUpdateLeadRepoQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateLeadRepoQueryCommandHandler.html">src/modules/leadRepo.queryside/command/handlers/create.handler.ts</a>
            </td>
            <td>class</td>
            <td>CreateLeadRepoQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#LeadRepoQueryCommandHandlers">src/modules/leadRepo.queryside/command/handlers/index.ts</a>
            </td>
            <td>variable</td>
            <td>LeadRepoQueryCommandHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateLeadRepoQueryCommandHandler.html">src/modules/leadRepo.queryside/command/handlers/update.handler.ts</a>
            </td>
            <td>class</td>
            <td>UpdateLeadRepoQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/BulkUpdateLeadRepoQueryCommand.html">src/modules/leadRepo.queryside/command/impl/bulkUpdate.cmd.ts</a>
            </td>
            <td>class</td>
            <td>BulkUpdateLeadRepoQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateLeadRepoQueryCommand.html">src/modules/leadRepo.queryside/command/impl/create.cmd.ts</a>
            </td>
            <td>class</td>
            <td>CreateLeadRepoQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateLeadRepoQueryCommand.html">src/modules/leadRepo.queryside/command/impl/update.cmd.ts</a>
            </td>
            <td>class</td>
            <td>UpdateLeadRepoQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/LeadRepoController.html">src/modules/leadRepo.queryside/controller.ts</a>
            </td>
            <td>controller</td>
            <td>LeadRepoController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IPagingQuery.html">src/modules/leadRepo.queryside/controller.ts</a>
            </td>
            <td>interface</td>
            <td>IPagingQuery</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/BulkUpdateLeadRepoQueryEvent.html">src/modules/leadRepo.queryside/events/evt/bulkUpdate.evt.ts</a>
            </td>
            <td>class</td>
            <td>BulkUpdateLeadRepoQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateLeadRepoQueryEvent.html">src/modules/leadRepo.queryside/events/evt/create.evt.ts</a>
            </td>
            <td>class</td>
            <td>CreateLeadRepoQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateLeadRepoQueryEvent.html">src/modules/leadRepo.queryside/events/evt/update.evt.ts</a>
            </td>
            <td>class</td>
            <td>UpdateLeadRepoQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/BulkUpdateLeadRepoQueryEventHandler.html">src/modules/leadRepo.queryside/events/handlers/bulkUpdate.handler.ts</a>
            </td>
            <td>class</td>
            <td>BulkUpdateLeadRepoQueryEventHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateLeadRepoQueryEventHandler.html">src/modules/leadRepo.queryside/events/handlers/create.handler.ts</a>
            </td>
            <td>class</td>
            <td>CreateLeadRepoQueryEventHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#LeadRepoQueryEventHandlers">src/modules/leadRepo.queryside/events/handlers/index.ts</a>
            </td>
            <td>variable</td>
            <td>LeadRepoQueryEventHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateLeadRepoQueryEventHandler.html">src/modules/leadRepo.queryside/events/handlers/update.handler.ts</a>
            </td>
            <td>class</td>
            <td>UpdateLeadRepoQueryEventHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ILeadRepoQueryDocument.html">src/modules/leadRepo.queryside/interfaces/document.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ILeadRepoQueryDocument</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadRepoQueryModel.html">src/modules/leadRepo.queryside/models/query.model.ts</a>
            </td>
            <td>class</td>
            <td>LeadRepoQueryModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#LeadRepoQueryProviders">src/modules/leadRepo.queryside/providers/query.provider.ts</a>
            </td>
            <td>variable</td>
            <td>LeadRepoQueryProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LeadRepoQueryRepository.html">src/modules/leadRepo.queryside/repositories/query.repository.ts</a>
            </td>
            <td>injectable</td>
            <td>LeadRepoQueryRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/16)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#DateRangeSchema">src/modules/leadRepo.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>DateRangeSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#LeadRepoConfigHotSchema">src/modules/leadRepo.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>LeadRepoConfigHotSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#LeadRepoConfigSchema">src/modules/leadRepo.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>LeadRepoConfigSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#LeadRepoSchema">src/modules/leadRepo.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>LeadRepoSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#NotificationInstanceSchema">src/modules/leadRepo.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>NotificationInstanceSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#NotificationSchema">src/modules/leadRepo.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>NotificationSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QuerySchema">src/modules/leadRepo.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>QuerySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#SurveySchema">src/modules/leadRepo.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>SurveySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LeadRepoQueryService.html">src/modules/leadRepo.queryside/service.ts</a>
            </td>
            <td>injectable</td>
            <td>LeadRepoQueryService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ActiveConfigLeadRepoCareCommandHandler.html">src/modules/leadRepoCare.domain/commands/handlers/activeConfig.handler.ts</a>
            </td>
            <td>class</td>
            <td>ActiveConfigLeadRepoCareCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateLeadRepoCareCommandHandler.html">src/modules/leadRepoCare.domain/commands/handlers/create.handler.ts</a>
            </td>
            <td>class</td>
            <td>CreateLeadRepoCareCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#LeadRepoCareDomainCommandHandlers">src/modules/leadRepoCare.domain/commands/handlers/index.ts</a>
            </td>
            <td>variable</td>
            <td>LeadRepoCareDomainCommandHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateLeadRepoCareCommandHandler.html">src/modules/leadRepoCare.domain/commands/handlers/update.handler.ts</a>
            </td>
            <td>class</td>
            <td>UpdateLeadRepoCareCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateConfigLeadRepoCareCommandHandler.html">src/modules/leadRepoCare.domain/commands/handlers/updateConfig.handler.ts</a>
            </td>
            <td>class</td>
            <td>UpdateConfigLeadRepoCareCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ActiveConfigLeadRepoCareCommand.html">src/modules/leadRepoCare.domain/commands/impl/activeConfig.ts</a>
            </td>
            <td>class</td>
            <td>ActiveConfigLeadRepoCareCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateLeadRepoCareCommand.html">src/modules/leadRepoCare.domain/commands/impl/create.cmd.ts</a>
            </td>
            <td>class</td>
            <td>CreateLeadRepoCareCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateLeadRepoCareCommand.html">src/modules/leadRepoCare.domain/commands/impl/update.cmd.ts</a>
            </td>
            <td>class</td>
            <td>UpdateLeadRepoCareCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateConfigLeadRepoCareCommand.html">src/modules/leadRepoCare.domain/commands/impl/updateConfig.ts</a>
            </td>
            <td>class</td>
            <td>UpdateConfigLeadRepoCareCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/LeadRepoCareDomainController.html">src/modules/leadRepoCare.domain/controller.ts</a>
            </td>
            <td>controller</td>
            <td>LeadRepoCareDomainController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ActiveLeadRepoCareConfigDto.html">src/modules/leadRepoCare.domain/dto/domain.dto.ts</a>
            </td>
            <td>class</td>
            <td>ActiveLeadRepoCareConfigDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateLeadRepoCareDto.html">src/modules/leadRepoCare.domain/dto/domain.dto.ts</a>
            </td>
            <td>class</td>
            <td>CreateLeadRepoCareDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateLeadRepoCareConfig.html">src/modules/leadRepoCare.domain/dto/domain.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateLeadRepoCareConfig</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/16)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateLeadRepoCareConfigDto.html">src/modules/leadRepoCare.domain/dto/domain.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateLeadRepoCareConfigDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateLeadRepoCareMainDto.html">src/modules/leadRepoCare.domain/dto/domain.dto.ts</a>
            </td>
            <td>class</td>
            <td>UpdateLeadRepoCareMainDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/13)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadRepoCareEventStream.html">src/modules/leadRepoCare.domain/events/domain.event.ts</a>
            </td>
            <td>class</td>
            <td>LeadRepoCareEventStream</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadRepoCareEventStreamHandler.html">src/modules/leadRepoCare.domain/events/domain.eventHandler.ts</a>
            </td>
            <td>class</td>
            <td>LeadRepoCareEventStreamHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ILeadRepoCareEventStreamDocument.html">src/modules/leadRepoCare.domain/interfaces/document.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ILeadRepoCareEventStreamDocument</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadRepoCareDomainModel.html">src/modules/leadRepoCare.domain/models/domain.model.ts</a>
            </td>
            <td>class</td>
            <td>LeadRepoCareDomainModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#LeadRepoCareDomainProvider">src/modules/leadRepoCare.domain/providers/domain.provider.ts</a>
            </td>
            <td>variable</td>
            <td>LeadRepoCareDomainProvider</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LeadRepoCareDomainRepository.html">src/modules/leadRepoCare.domain/repositories/domain.repository.ts</a>
            </td>
            <td>injectable</td>
            <td>LeadRepoCareDomainRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/16)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LeadRepoCareDomainSagas.html">src/modules/leadRepoCare.domain/sagas/domain.sagas.ts</a>
            </td>
            <td>injectable</td>
            <td>LeadRepoCareDomainSagas</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#beautifyUnique">src/modules/leadRepoCare.domain/schemas/domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>beautifyUnique</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#DomainSchema">src/modules/leadRepoCare.domain/schemas/domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>DomainSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#PayloadSchema">src/modules/leadRepoCare.domain/schemas/domain.schema.ts</a>
            </td>
            <td>variable</td>
            <td>PayloadSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LeadRepoCareDomainService.html">src/modules/leadRepoCare.domain/service.ts</a>
            </td>
            <td>injectable</td>
            <td>LeadRepoCareDomainService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/17)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateLeadRepoCareQueryCommandHandler.html">src/modules/leadRepoCare.queryside/command/handlers/create.handler.ts</a>
            </td>
            <td>class</td>
            <td>CreateLeadRepoCareQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#LeadRepoCareQueryCommandHandlers">src/modules/leadRepoCare.queryside/command/handlers/index.ts</a>
            </td>
            <td>variable</td>
            <td>LeadRepoCareQueryCommandHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateLeadRepoCareQueryCommandHandler.html">src/modules/leadRepoCare.queryside/command/handlers/update.handler.ts</a>
            </td>
            <td>class</td>
            <td>UpdateLeadRepoCareQueryCommandHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateLeadRepoCareQueryCommand.html">src/modules/leadRepoCare.queryside/command/impl/create.cmd.ts</a>
            </td>
            <td>class</td>
            <td>CreateLeadRepoCareQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateLeadRepoCareQueryCommand.html">src/modules/leadRepoCare.queryside/command/impl/update.cmd.ts</a>
            </td>
            <td>class</td>
            <td>UpdateLeadRepoCareQueryCommand</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/LeadRepoCareController.html">src/modules/leadRepoCare.queryside/controller.ts</a>
            </td>
            <td>controller</td>
            <td>LeadRepoCareController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IPagingQuery.html">src/modules/leadRepoCare.queryside/controller.ts</a>
            </td>
            <td>interface</td>
            <td>IPagingQuery</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateLeadRepoCareQueryEvent.html">src/modules/leadRepoCare.queryside/events/evt/create.evt.ts</a>
            </td>
            <td>class</td>
            <td>CreateLeadRepoCareQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateLeadRepoCareQueryEvent.html">src/modules/leadRepoCare.queryside/events/evt/update.evt.ts</a>
            </td>
            <td>class</td>
            <td>UpdateLeadRepoCareQueryEvent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CreateLeadRepoCareQueryEventHandler.html">src/modules/leadRepoCare.queryside/events/handlers/create.handler.ts</a>
            </td>
            <td>class</td>
            <td>CreateLeadRepoCareQueryEventHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#LeadRepoCareQueryEventHandlers">src/modules/leadRepoCare.queryside/events/handlers/index.ts</a>
            </td>
            <td>variable</td>
            <td>LeadRepoCareQueryEventHandlers</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UpdateLeadRepoCareQueryEventHandler.html">src/modules/leadRepoCare.queryside/events/handlers/update.handler.ts</a>
            </td>
            <td>class</td>
            <td>UpdateLeadRepoCareQueryEventHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ILeadRepoCareQueryDocument.html">src/modules/leadRepoCare.queryside/interfaces/document.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ILeadRepoCareQueryDocument</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadRepoCareQueryModel.html">src/modules/leadRepoCare.queryside/models/query.model.ts</a>
            </td>
            <td>class</td>
            <td>LeadRepoCareQueryModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#LeadRepoCareQueryProviders">src/modules/leadRepoCare.queryside/providers/query.provider.ts</a>
            </td>
            <td>variable</td>
            <td>LeadRepoCareQueryProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LeadRepoCareQueryRepository.html">src/modules/leadRepoCare.queryside/repositories/query.repository.ts</a>
            </td>
            <td>injectable</td>
            <td>LeadRepoCareQueryRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/17)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#DateRangeSchema">src/modules/leadRepoCare.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>DateRangeSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#LeadRepoCareConfigHotSchema">src/modules/leadRepoCare.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>LeadRepoCareConfigHotSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#LeadRepoCareConfigSchema">src/modules/leadRepoCare.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>LeadRepoCareConfigSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#LeadRepoCareSchema">src/modules/leadRepoCare.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>LeadRepoCareSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#NotificationInstanceSchema">src/modules/leadRepoCare.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>NotificationInstanceSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#NotificationSchema">src/modules/leadRepoCare.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>NotificationSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QuerySchema">src/modules/leadRepoCare.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>QuerySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#SurveySchema">src/modules/leadRepoCare.queryside/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>SurveySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LeadRepoCareQueryService.html">src/modules/leadRepoCare.queryside/service.ts</a>
            </td>
            <td>injectable</td>
            <td>LeadRepoCareQueryService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/LeadSourceController.html">src/modules/leadSource/controller.ts</a>
            </td>
            <td>controller</td>
            <td>LeadSourceController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ILeadSourceDocument.html">src/modules/leadSource/interfaces/document.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ILeadSourceDocument</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QueryProviders">src/modules/leadSource/providers/query.cqrs.providers.ts</a>
            </td>
            <td>variable</td>
            <td>QueryProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LeadSourceQueryRepository.html">src/modules/leadSource/repository/query.repository.ts</a>
            </td>
            <td>injectable</td>
            <td>LeadSourceQueryRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#moment">src/modules/leadSource/repository/query.repository.ts</a>
            </td>
            <td>variable</td>
            <td>moment</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QuerySchema">src/modules/leadSource/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>QuerySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LeadSourceService.html">src/modules/leadSource/service.ts</a>
            </td>
            <td>injectable</td>
            <td>LeadSourceService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/ListenerController.html">src/modules/listener/listener.controller.ts</a>
            </td>
            <td>controller</td>
            <td>ListenerController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/21)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#configService">src/modules/listener/listener.controller.ts</a>
            </td>
            <td>variable</td>
            <td>configService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#recordNumber">src/modules/listener/listener.controller.ts</a>
            </td>
            <td>variable</td>
            <td>recordNumber</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/MsxLoggerService.html">src/modules/logger/logger.service.ts</a>
            </td>
            <td>injectable</td>
            <td>MsxLoggerService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/CareClient.html">src/modules/mgs-sender/care.client.ts</a>
            </td>
            <td>injectable</td>
            <td>CareClient</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/DemandClient.html">src/modules/mgs-sender/demand.client.ts</a>
            </td>
            <td>injectable</td>
            <td>DemandClient</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/EmployeeClient.html">src/modules/mgs-sender/employee.client.ts</a>
            </td>
            <td>injectable</td>
            <td>EmployeeClient</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/LoggerClient.html">src/modules/mgs-sender/logger.client.ts</a>
            </td>
            <td>injectable</td>
            <td>LoggerClient</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/MailerClient.html">src/modules/mgs-sender/mailer.client.ts</a>
            </td>
            <td>injectable</td>
            <td>MailerClient</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#RABBITMQ_URL">src/modules/mgs-sender/mgs-sender.module.ts</a>
            </td>
            <td>variable</td>
            <td>RABBITMQ_URL</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/MsgSenderService.html">src/modules/mgs-sender/mgs.sender.service.ts</a>
            </td>
            <td>injectable</td>
            <td>MsgSenderService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/NotificationClient.html">src/modules/mgs-sender/notification.client.ts</a>
            </td>
            <td>injectable</td>
            <td>NotificationClient</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/13)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/NotifierClient.html">src/modules/mgs-sender/notifier.client.ts</a>
            </td>
            <td>injectable</td>
            <td>NotifierClient</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/INotifierData.html">src/modules/mgs-sender/notifier.client.ts</a>
            </td>
            <td>interface</td>
            <td>INotifierData</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/OrgchartClient.html">src/modules/mgs-sender/orgchart.client.ts</a>
            </td>
            <td>injectable</td>
            <td>OrgchartClient</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/PrimaryContractClient.html">src/modules/mgs-sender/primary-contract.client.ts</a>
            </td>
            <td>injectable</td>
            <td>PrimaryContractClient</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/PropertyClient.html">src/modules/mgs-sender/property.client.ts</a>
            </td>
            <td>injectable</td>
            <td>PropertyClient</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/RatingClient.html">src/modules/mgs-sender/rating.client.ts</a>
            </td>
            <td>injectable</td>
            <td>RatingClient</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/StsClient.html">src/modules/mgs-sender/sts.client.ts</a>
            </td>
            <td>injectable</td>
            <td>StsClient</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./controllers/RawController.html">src/modules/raw/controller.ts</a>
            </td>
            <td>controller</td>
            <td>RawController</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/RawDto.html">src/modules/raw/dto/raw.dto.ts</a>
            </td>
            <td>class</td>
            <td>RawDto</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IRawDocument.html">src/modules/raw/interfaces/document.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IRawDocument</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QueryProviders">src/modules/raw/providers/query.cqrs.providers.ts</a>
            </td>
            <td>variable</td>
            <td>QueryProviders</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/RawQueryRepository.html">src/modules/raw/repository/query.repository.ts</a>
            </td>
            <td>injectable</td>
            <td>RawQueryRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#QuerySchema">src/modules/raw/schemas/query.schema.ts</a>
            </td>
            <td>variable</td>
            <td>QuerySchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/RawService.html">src/modules/raw/service.ts</a>
            </td>
            <td>injectable</td>
            <td>RawService</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ClassBased.html">src/modules/shared/classes/class-based.ts</a>
            </td>
            <td>class</td>
            <td>ClassBased</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/Queue.html">src/modules/shared/classes/class-queue.ts</a>
            </td>
            <td>class</td>
            <td>Queue</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/10)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonUtils.html">src/modules/shared/classes/class-utils.ts</a>
            </td>
            <td>class</td>
            <td>CommonUtils</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/9)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/IsLegalIdentify.html">src/modules/shared/classes/class-validation.ts</a>
            </td>
            <td>class</td>
            <td>IsLegalIdentify</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/IsLegalLeadStatus.html">src/modules/shared/classes/class-validation.ts</a>
            </td>
            <td>class</td>
            <td>IsLegalLeadStatus</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/IsLegalMaritalStatus.html">src/modules/shared/classes/class-validation.ts</a>
            </td>
            <td>class</td>
            <td>IsLegalMaritalStatus</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/IsLegalSex.html">src/modules/shared/classes/class-validation.ts</a>
            </td>
            <td>class</td>
            <td>IsLegalSex</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/IsLegalSurvey.html">src/modules/shared/classes/class-validation.ts</a>
            </td>
            <td>class</td>
            <td>IsLegalSurvey</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/IsStringNotBlank.html">src/modules/shared/classes/class-validation.ts</a>
            </td>
            <td>class</td>
            <td>IsStringNotBlank</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CmdPatternConst.html">src/modules/shared/constant/cmd-pattern.const.ts</a>
            </td>
            <td>class</td>
            <td>CmdPatternConst</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/22)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommonConst.html">src/modules/shared/constant/common.const.ts</a>
            </td>
            <td>class</td>
            <td>CommonConst</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/43)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ConfigTimeConst.html">src/modules/shared/constant/config-time.const.ts</a>
            </td>
            <td>class</td>
            <td>ConfigTimeConst</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/9)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ErrorConst.html">src/modules/shared/constant/error.const.ts</a>
            </td>
            <td>class</td>
            <td>ErrorConst</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/25)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/PermissionConst.html">src/modules/shared/constant/permission.const.ts</a>
            </td>
            <td>class</td>
            <td>PermissionConst</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/83)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/QueueConst.html">src/modules/shared/constant/queue.const.ts</a>
            </td>
            <td>class</td>
            <td>QueueConst</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/15)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/UrlConst.html">src/modules/shared/constant/url.const.ts</a>
            </td>
            <td>class</td>
            <td>UrlConst</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/BaseEventHandler.html">src/modules/shared/eventStream/events/base-event-handler.model.ts</a>
            </td>
            <td>class</td>
            <td>BaseEventHandler</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IEventStreamDocument.html">src/modules/shared/eventStream/interfaces/event-stream-document.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IEventStreamDocument</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/11)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IPayloadBased.html">src/modules/shared/eventStream/interfaces/payload-based.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IPayloadBased</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/BaseEventStream.html">src/modules/shared/eventStream/models/base-event-stream.model.ts</a>
            </td>
            <td>class</td>
            <td>BaseEventStream</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/15)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommandModel.html">src/modules/shared/eventStream/models/command.model.ts</a>
            </td>
            <td>class</td>
            <td>CommandModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/11)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/Payload.html">src/modules/shared/eventStream/models/payload.model.ts</a>
            </td>
            <td>class</td>
            <td>Payload</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/10)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IBaseInterface.html">src/modules/shared/interfaces/base.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IBaseInterface</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/FindManyFilter.html">src/modules/shared/interfaces/baseFilter.interface.ts</a>
            </td>
            <td>interface</td>
            <td>FindManyFilter</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/FindOneFilter.html">src/modules/shared/interfaces/baseFilter.interface.ts</a>
            </td>
            <td>interface</td>
            <td>FindOneFilter</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IDateRangeFilter.html">src/modules/shared/interfaces/baseFilter.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IDateRangeFilter</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/OperatorFilter.html">src/modules/shared/interfaces/baseFilter.interface.ts</a>
            </td>
            <td>interface</td>
            <td>OperatorFilter</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/SearchRegex.html">src/modules/shared/interfaces/baseFilter.interface.ts</a>
            </td>
            <td>interface</td>
            <td>SearchRegex</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DataMessagePattern.html">src/modules/shared/interfaces/messaging-pattern.interface.ts</a>
            </td>
            <td>class</td>
            <td>DataMessagePattern</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/ErrorMessagePattern.html">src/modules/shared/interfaces/messaging-pattern.interface.ts</a>
            </td>
            <td>class</td>
            <td>ErrorMessagePattern</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/InfoMessagePattern.html">src/modules/shared/interfaces/messaging-pattern.interface.ts</a>
            </td>
            <td>class</td>
            <td>InfoMessagePattern</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadAssignedMessagePattern.html">src/modules/shared/interfaces/messaging-pattern.interface.ts</a>
            </td>
            <td>class</td>
            <td>LeadAssignedMessagePattern</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadCareAssignedMessagePattern.html">src/modules/shared/interfaces/messaging-pattern.interface.ts</a>
            </td>
            <td>class</td>
            <td>LeadCareAssignedMessagePattern</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IMessagingPattern.html">src/modules/shared/interfaces/messaging-pattern.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IMessagingPattern</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ISummaryObject.html">src/modules/shared/interfaces/object-summary.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ISummaryObject</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/QueryLeadInterface.html">src/modules/shared/interfaces/queryLead.interface.ts</a>
            </td>
            <td>interface</td>
            <td>QueryLeadInterface</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ReportExploitationFilter.html">src/modules/shared/interfaces/queryLead.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ReportExploitationFilter</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/QueryLeadCareInterface.html">src/modules/shared/interfaces/queryLeadCare.interface.ts</a>
            </td>
            <td>interface</td>
            <td>QueryLeadCareInterface</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IResultPaging.html">src/modules/shared/interfaces/result-paging.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IResultPaging</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IResult.html">src/modules/shared/interfaces/result.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IResult</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadHistoryMapper.html">src/modules/shared/mapper/lead-history.mapper.ts</a>
            </td>
            <td>class</td>
            <td>LeadHistoryMapper</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadMapper.html">src/modules/shared/mapper/lead.mapper.ts</a>
            </td>
            <td>class</td>
            <td>LeadMapper</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#moment">src/modules/shared/mapper/lead.mapper.ts</a>
            </td>
            <td>variable</td>
            <td>moment</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#objectMapper">src/modules/shared/mapper/lead.mapper.ts</a>
            </td>
            <td>variable</td>
            <td>objectMapper</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadCareHistoryMapper.html">src/modules/shared/mapper/leadCare-history.mapper.ts</a>
            </td>
            <td>class</td>
            <td>LeadCareHistoryMapper</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadCareMapper.html">src/modules/shared/mapper/leadCare.mapper.ts</a>
            </td>
            <td>class</td>
            <td>LeadCareMapper</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#moment">src/modules/shared/mapper/leadCare.mapper.ts</a>
            </td>
            <td>variable</td>
            <td>moment</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#objectMapper">src/modules/shared/mapper/leadCare.mapper.ts</a>
            </td>
            <td>variable</td>
            <td>objectMapper</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/BaseModel.html">src/modules/shared/models/base/base.model.ts</a>
            </td>
            <td>class</td>
            <td>BaseModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/10)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/BaseEventStream.html">src/modules/shared/models/base/baseEventStream.model.ts</a>
            </td>
            <td>class</td>
            <td>BaseEventStream</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/16)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadRepoCommandModel.html">src/modules/shared/models/leadRepo/command.model.ts</a>
            </td>
            <td>class</td>
            <td>LeadRepoCommandModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/18)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadRepoEventStreamModel.html">src/modules/shared/models/leadRepo/eventStream.model.ts</a>
            </td>
            <td>class</td>
            <td>LeadRepoEventStreamModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/14)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DateRange.html">src/modules/shared/models/leadRepo/model.ts</a>
            </td>
            <td>class</td>
            <td>DateRange</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadRepo.html">src/modules/shared/models/leadRepo/model.ts</a>
            </td>
            <td>class</td>
            <td>LeadRepo</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/14)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadRepoConfig.html">src/modules/shared/models/leadRepo/model.ts</a>
            </td>
            <td>class</td>
            <td>LeadRepoConfig</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/17)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadRepoConfigHot.html">src/modules/shared/models/leadRepo/model.ts</a>
            </td>
            <td>class</td>
            <td>LeadRepoConfigHot</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/10)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/Notification.html">src/modules/shared/models/leadRepo/model.ts</a>
            </td>
            <td>class</td>
            <td>Notification</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/NotificationInstance.html">src/modules/shared/models/leadRepo/model.ts</a>
            </td>
            <td>class</td>
            <td>NotificationInstance</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/OrgChart.html">src/modules/shared/models/leadRepo/model.ts</a>
            </td>
            <td>class</td>
            <td>OrgChart</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/OrgChartQueueItem.html">src/modules/shared/models/leadRepo/model.ts</a>
            </td>
            <td>class</td>
            <td>OrgChartQueueItem</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/Project.html">src/modules/shared/models/leadRepo/model.ts</a>
            </td>
            <td>class</td>
            <td>Project</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/Survey.html">src/modules/shared/models/leadRepo/model.ts</a>
            </td>
            <td>class</td>
            <td>Survey</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadRepoPayload.html">src/modules/shared/models/leadRepo/payload.model.ts</a>
            </td>
            <td>class</td>
            <td>LeadRepoPayload</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/17)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadRepoCareCommandModel.html">src/modules/shared/models/leadRepoCare/command.model.ts</a>
            </td>
            <td>class</td>
            <td>LeadRepoCareCommandModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/19)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadRepoCareEventStreamModel.html">src/modules/shared/models/leadRepoCare/eventStream.model.ts</a>
            </td>
            <td>class</td>
            <td>LeadRepoCareEventStreamModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/14)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/DateRange.html">src/modules/shared/models/leadRepoCare/model.ts</a>
            </td>
            <td>class</td>
            <td>DateRange</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadRepoCare.html">src/modules/shared/models/leadRepoCare/model.ts</a>
            </td>
            <td>class</td>
            <td>LeadRepoCare</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/15)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadRepoCareConfig.html">src/modules/shared/models/leadRepoCare/model.ts</a>
            </td>
            <td>class</td>
            <td>LeadRepoCareConfig</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/15)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadRepoCareConfigHot.html">src/modules/shared/models/leadRepoCare/model.ts</a>
            </td>
            <td>class</td>
            <td>LeadRepoCareConfigHot</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/Notification.html">src/modules/shared/models/leadRepoCare/model.ts</a>
            </td>
            <td>class</td>
            <td>Notification</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/NotificationInstance.html">src/modules/shared/models/leadRepoCare/model.ts</a>
            </td>
            <td>class</td>
            <td>NotificationInstance</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/OrgChart.html">src/modules/shared/models/leadRepoCare/model.ts</a>
            </td>
            <td>class</td>
            <td>OrgChart</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/OrgChartQueueItem.html">src/modules/shared/models/leadRepoCare/model.ts</a>
            </td>
            <td>class</td>
            <td>OrgChartQueueItem</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/Project.html">src/modules/shared/models/leadRepoCare/model.ts</a>
            </td>
            <td>class</td>
            <td>Project</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/Survey.html">src/modules/shared/models/leadRepoCare/model.ts</a>
            </td>
            <td>class</td>
            <td>Survey</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/LeadRepoCarePayload.html">src/modules/shared/models/leadRepoCare/payload.model.ts</a>
            </td>
            <td>class</td>
            <td>LeadRepoCarePayload</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/27)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href=".//functions.html#setKeywords">src/modules/shared/plugins/keywords.ts</a>
            </td>
            <td>function</td>
            <td>setKeywords</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#BaseSchema">src/modules/shared/schema/schema.base.ts</a>
            </td>
            <td>variable</td>
            <td>BaseSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#getEventStreamSchema">src/modules/shared/schema/schema.base.ts</a>
            </td>
            <td>variable</td>
            <td>getEventStreamSchema</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IAclByIdRequest.html">src/modules/shared/services/acl/interfaces/acl.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IAclByIdRequest</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IAclResponse.html">src/modules/shared/services/acl/interfaces/acl.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IAclResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./injectables/BaseRepository.html">src/modules/shared/services/baseRepository/repository.base.ts</a>
            </td>
            <td>injectable</td>
            <td>BaseRepository</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/14)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommandModel.html">src/modules/shared/services/lead/impl/lead.cmd.model.ts</a>
            </td>
            <td>class</td>
            <td>CommandModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/69)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IExploitHistory.html">src/modules/shared/services/lead/interfaces/lead.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IExploitHistory</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ILead.html">src/modules/shared/services/lead/interfaces/lead.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ILead</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/59)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ILeadByIdRequest.html">src/modules/shared/services/lead/interfaces/lead.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ILeadByIdRequest</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ILeadProcessed.html">src/modules/shared/services/lead/interfaces/lead.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ILeadProcessed</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ILeadResponse.html">src/modules/shared/services/lead/interfaces/lead.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ILeadResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/13)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IProject.html">src/modules/shared/services/lead/interfaces/lead.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IProject</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ITakeCare.html">src/modules/shared/services/lead/interfaces/lead.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ITakeCare</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./classes/CommandModel.html">src/modules/shared/services/leadCare/impl/leadCare.cmd.model.ts</a>
            </td>
            <td>class</td>
            <td>CommandModel</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/63)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IExploitHistory.html">src/modules/shared/services/leadCare/interfaces/leadCare.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IExploitHistory</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ILeadCare.html">src/modules/shared/services/leadCare/interfaces/leadCare.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ILeadCare</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/52)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ILeadCareByIdRequest.html">src/modules/shared/services/leadCare/interfaces/leadCare.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ILeadCareByIdRequest</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ILeadCareProcessed.html">src/modules/shared/services/leadCare/interfaces/leadCare.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ILeadCareProcessed</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/7)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ILeadCareResponse.html">src/modules/shared/services/leadCare/interfaces/leadCare.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ILeadCareResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/13)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IProject.html">src/modules/shared/services/leadCare/interfaces/leadCare.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IProject</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ITakeCare.html">src/modules/shared/services/leadCare/interfaces/leadCare.interface.ts</a>
            </td>
            <td>interface</td>
            <td>ITakeCare</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#Usr">src/modules/shared/services/user/decorator/user.decorator.ts</a>
            </td>
            <td>variable</td>
            <td>Usr</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IUserResponse.html">src/modules/shared/services/user/user-by-id.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IUserResponse</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/IUserResquest.html">src/modules/shared/services/user/user-by-id.interface.ts</a>
            </td>
            <td>interface</td>
            <td>IUserResquest</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/GenerateCodeOptions.html">src/modules/shared/utils/codegen.ts</a>
            </td>
            <td>interface</td>
            <td>GenerateCodeOptions</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CODE_CHAR_TEMPLATE">src/modules/shared/utils/codegen.ts</a>
            </td>
            <td>variable</td>
            <td>CODE_CHAR_TEMPLATE</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#CUSTOMER_TYPE_CODE_LENGTH">src/modules/shared/utils/codegen.ts</a>
            </td>
            <td>variable</td>
            <td>CUSTOMER_TYPE_CODE_LENGTH</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#generateCode">src/modules/shared/utils/codegen.ts</a>
            </td>
            <td>variable</td>
            <td>generateCode</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#PROFILE_CODE_LENGTH">src/modules/shared/utils/codegen.ts</a>
            </td>
            <td>variable</td>
            <td>PROFILE_CODE_LENGTH</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#promiseWhile">src/modules/shared/utils/promiseWhile.ts</a>
            </td>
            <td>variable</td>
            <td>promiseWhile</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#updateArrData">src/modules/shared/utils/updateArrData.ts</a>
            </td>
            <td>variable</td>
            <td>updateArrData</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ExternalField.html">src/modules/shared/utils/utils.ts</a>
            </td>
            <td>interface</td>
            <td>ExternalField</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#getKeywords">src/modules/shared/utils/utils.ts</a>
            </td>
            <td>variable</td>
            <td>getKeywords</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#removeSign">src/modules/shared/utils/utils.ts</a>
            </td>
            <td>variable</td>
            <td>removeSign</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./miscellaneous/variables.html#splitText">src/modules/shared/utils/utils.ts</a>
            </td>
            <td>variable</td>
            <td>splitText</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
    </tbody>
</table>

<script src="js/libs/tablesort.min.js"></script>
<script src="js/libs/tablesort.number.min.js"></script>
<script>
    new Tablesort(document.getElementById('coverage-table'));
</script>

                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 0;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'coverage';
            var COMPODOC_CURRENT_PAGE_URL = 'coverage.html';
       </script>

       <script src="./js/libs/custom-elements.min.js"></script>
       <script src="./js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="./js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="./js/menu-wc.js" defer></script>

       <script src="./js/libs/bootstrap-native.js"></script>

       <script src="./js/libs/es6-shim.min.js"></script>
       <script src="./js/libs/EventDispatcher.js"></script>
       <script src="./js/libs/promise.min.js"></script>
       <script src="./js/libs/zepto.min.js"></script>

       <script src="./js/compodoc.js"></script>

       <script src="./js/tabs.js"></script>
       <script src="./js/menu.js"></script>
       <script src="./js/libs/clipboard.min.js"></script>
       <script src="./js/libs/prism.js"></script>
       <script src="./js/sourceCode.js"></script>
          <script src="./js/search/search.js"></script>
          <script src="./js/search/lunr.min.js"></script>
          <script src="./js/search/search-lunr.js"></script>
          <script src="./js/search/search_index.js"></script>
       <script src="./js/lazy-load-graphs.js"></script>


    </body>
</html>
