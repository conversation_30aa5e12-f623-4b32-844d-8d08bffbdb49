<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-lead documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="./images/favicon.ico">
	      <link rel="stylesheet" href="./styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="./" class="navbar-brand">msx-lead documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content package-dependencies">
                   <div class="content-data">














    <ol class="breadcrumb">
        <li>Dependencies</li>
    </ol>
    <ul class="dependencies-list">
        <li>
            <b>@nestjs/common</b> : 6.11.11</li>
        <li>
            <b>@nestjs/core</b> : 6.11.11</li>
        <li>
            <b>@nestjs/cqrs</b> : 6.0.1</li>
        <li>
            <b>@nestjs/microservices</b> : 6.11.11</li>
        <li>
            <b>@nestjs/passport</b> : 6.1.0</li>
        <li>
            <b>@nestjs/platform-express</b> : 6.11.11</li>
        <li>
            <b>@nestjs/swagger</b> : 3.0.2</li>
        <li>
            <b>@nestjs/testing</b> : 6.11.11</li>
        <li>
            <b>@types/express</b> : 4.17.13</li>
        <li>
            <b>@types/uuid</b> : 3.4.5</li>
        <li>
            <b>amqp-connection-manager</b> : 3.0.0</li>
        <li>
            <b>amqplib</b> : 0.5.3</li>
        <li>
            <b>bluebird</b> : ^3.7.2</li>
        <li>
            <b>body-parser</b> : 1.19.0</li>
        <li>
            <b>cache-manager</b> : 2.9.1</li>
        <li>
            <b>cache-manager-redis-store</b> : 1.5.0</li>
        <li>
            <b>class-transformer</b> : 0.2.3</li>
        <li>
            <b>class-validator</b> : 0.9.1</li>
        <li>
            <b>content-filter</b> : 1.1.2</li>
        <li>
            <b>cors</b> : 2.8.5</li>
        <li>
            <b>cross-env</b> : 5.2.0</li>
        <li>
            <b>dotenv</b> : 8.0.0</li>
        <li>
            <b>exceljs</b> : ^4.2.1</li>
        <li>
            <b>frisby</b> : 2.1.2</li>
        <li>
            <b>helmet</b> : 3.20.0</li>
        <li>
            <b>jsonwebtoken</b> : 8.5.1</li>
        <li>
            <b>lodash</b> : 4.17.11</li>
        <li>
            <b>moment</b> : 2.24.0</li>
        <li>
            <b>moment-timezone</b> : 0.5.25</li>
        <li>
            <b>mongoose</b> : 5.6.2</li>
        <li>
            <b>mongoose-beautiful-unique-validation</b> : 7.1.1</li>
        <li>
            <b>nest-access-control</b> : 2.0.1</li>
        <li>
            <b>nest-router</b> : 1.0.9</li>
        <li>
            <b>object-mapper</b> : 5.0.0</li>
        <li>
            <b>passport</b> : 0.4.0</li>
        <li>
            <b>passport-http-bearer</b> : 1.0.1</li>
        <li>
            <b>passport-jwt</b> : 4.0.0</li>
        <li>
            <b>passport-local</b> : 1.0.0</li>
        <li>
            <b>redis</b> : 2.8.0</li>
        <li>
            <b>reflect-metadata</b> : 0.1.13</li>
        <li>
            <b>rxjs</b> : 6.5.2</li>
        <li>
            <b>swagger-ui-express</b> : 4.0.7</li>
        <li>
            <b>uuid</b> : 3.3.2</li>
        <li>
            <b>xlsx</b> : 0.15.5</li>
    </ul>

                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 0;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'package-dependencies';
            var COMPODOC_CURRENT_PAGE_URL = 'dependencies.html';
       </script>

       <script src="./js/libs/custom-elements.min.js"></script>
       <script src="./js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="./js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="./js/menu-wc.js" defer></script>

       <script src="./js/libs/bootstrap-native.js"></script>

       <script src="./js/libs/es6-shim.min.js"></script>
       <script src="./js/libs/EventDispatcher.js"></script>
       <script src="./js/libs/promise.min.js"></script>
       <script src="./js/libs/zepto.min.js"></script>

       <script src="./js/compodoc.js"></script>

       <script src="./js/tabs.js"></script>
       <script src="./js/menu.js"></script>
       <script src="./js/libs/clipboard.min.js"></script>
       <script src="./js/libs/prism.js"></script>
       <script src="./js/sourceCode.js"></script>
          <script src="./js/search/search.js"></script>
          <script src="./js/search/lunr.min.js"></script>
          <script src="./js/search/search-lunr.js"></script>
          <script src="./js/search/search_index.js"></script>
       <script src="./js/lazy-load-graphs.js"></script>


    </body>
</html>
