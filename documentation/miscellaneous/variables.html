<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-lead documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-lead documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content miscellaneous-variables">
                   <div class="content-data">














                   

<ol class="breadcrumb">
  <li>Miscellaneous</li>
  <li>Variables</li>
</ol>

<section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <ul class="index-list">
                        <li>
                            <a href="#acRoles" title="src/modules/auth/app.roles.ts"><b>acRoles</b>&nbsp;&nbsp;&nbsp;(src/.../app.roles.ts)</a>
                        </li>
                        <li>
                            <a href="#addressSchema" title="src/modules/lead.domain/schemas/cqrs.domain.schema.ts"><b>addressSchema</b>&nbsp;&nbsp;&nbsp;(src/.../cqrs.domain.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#addressSchema" title="src/modules/lead.queryside/schemas/query.schema.ts"><b>addressSchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#addressSchema" title="src/modules/leadCare.domain/schemas/cqrs.domain.schema.ts"><b>addressSchema</b>&nbsp;&nbsp;&nbsp;(src/.../cqrs.domain.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#addressSchema" title="src/modules/leadCare.queryside/schemas/query.schema.ts"><b>addressSchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#BaseSchema" title="src/modules/shared/schema/schema.base.ts"><b>BaseSchema</b>&nbsp;&nbsp;&nbsp;(src/.../schema.base.ts)</a>
                        </li>
                        <li>
                            <a href="#beautifyUnique" title="src/modules/lead.domain/schemas/cqrs.domain.schema.ts"><b>beautifyUnique</b>&nbsp;&nbsp;&nbsp;(src/.../cqrs.domain.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#beautifyUnique" title="src/modules/leadCare.domain/schemas/cqrs.domain.schema.ts"><b>beautifyUnique</b>&nbsp;&nbsp;&nbsp;(src/.../cqrs.domain.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#beautifyUnique" title="src/modules/leadRepo.domain/schemas/domain.schema.ts"><b>beautifyUnique</b>&nbsp;&nbsp;&nbsp;(src/.../domain.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#beautifyUnique" title="src/modules/leadJob/infra/schema.ts"><b>beautifyUnique</b>&nbsp;&nbsp;&nbsp;(src/.../schema.ts)</a>
                        </li>
                        <li>
                            <a href="#beautifyUnique" title="src/modules/leadRepoCare.domain/schemas/domain.schema.ts"><b>beautifyUnique</b>&nbsp;&nbsp;&nbsp;(src/.../domain.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#callback" title="src/modules/auth/passport/jwt.strategy.ts"><b>callback</b>&nbsp;&nbsp;&nbsp;(src/.../jwt.strategy.ts)</a>
                        </li>
                        <li>
                            <a href="#categorySchema" title="src/modules/lead.domain/schemas/cqrs.domain.schema.ts"><b>categorySchema</b>&nbsp;&nbsp;&nbsp;(src/.../cqrs.domain.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#categorySchema" title="src/modules/lead.queryside/schemas/query.schema.ts"><b>categorySchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#categorySchema" title="src/modules/leadCare.domain/schemas/cqrs.domain.schema.ts"><b>categorySchema</b>&nbsp;&nbsp;&nbsp;(src/.../cqrs.domain.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#categorySchema" title="src/modules/leadCare.queryside/schemas/query.schema.ts"><b>categorySchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#clc" title="src/modules/leadCare.queryside/repository/query.repository.ts"><b>clc</b>&nbsp;&nbsp;&nbsp;(src/.../query.repository.ts)</a>
                        </li>
                        <li>
                            <a href="#clc" title="src/modules/leadJob/infra/repository.ts"><b>clc</b>&nbsp;&nbsp;&nbsp;(src/.../repository.ts)</a>
                        </li>
                        <li>
                            <a href="#CODE_CHAR_TEMPLATE" title="src/modules/shared/utils/codegen.ts"><b>CODE_CHAR_TEMPLATE</b>&nbsp;&nbsp;&nbsp;(src/.../codegen.ts)</a>
                        </li>
                        <li>
                            <a href="#CommandHandlers" title="src/modules/lead.domain/commands/handlers/index.ts"><b>CommandHandlers</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#CommandHandlers" title="src/modules/lead.queryside/commands/handlers/index.ts"><b>CommandHandlers</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#CommandHandlers" title="src/modules/leadCare.domain/commands/handlers/index.ts"><b>CommandHandlers</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#CommandHandlers" title="src/modules/leadCare.queryside/commands/handlers/index.ts"><b>CommandHandlers</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#configService" title="src/main.ts"><b>configService</b>&nbsp;&nbsp;&nbsp;(src/.../main.ts)</a>
                        </li>
                        <li>
                            <a href="#configService" title="src/modules/listener/listener.controller.ts"><b>configService</b>&nbsp;&nbsp;&nbsp;(src/.../listener.controller.ts)</a>
                        </li>
                        <li>
                            <a href="#CqrsDomainSchema" title="src/modules/lead.domain/schemas/cqrs.domain.schema.ts"><b>CqrsDomainSchema</b>&nbsp;&nbsp;&nbsp;(src/.../cqrs.domain.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#CqrsDomainSchema" title="src/modules/leadCare.domain/schemas/cqrs.domain.schema.ts"><b>CqrsDomainSchema</b>&nbsp;&nbsp;&nbsp;(src/.../cqrs.domain.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#CqrsProviders" title="src/modules/lead.domain/providers/cqrs.domain.providers.ts"><b>CqrsProviders</b>&nbsp;&nbsp;&nbsp;(src/.../cqrs.domain.providers.ts)</a>
                        </li>
                        <li>
                            <a href="#CqrsProviders" title="src/modules/leadCare.domain/providers/cqrs.domain.providers.ts"><b>CqrsProviders</b>&nbsp;&nbsp;&nbsp;(src/.../cqrs.domain.providers.ts)</a>
                        </li>
                        <li>
                            <a href="#CUSTOMER_TYPE_CODE_LENGTH" title="src/modules/shared/utils/codegen.ts"><b>CUSTOMER_TYPE_CODE_LENGTH</b>&nbsp;&nbsp;&nbsp;(src/.../codegen.ts)</a>
                        </li>
                        <li>
                            <a href="#DateRangeSchema" title="src/modules/leadRepo.queryside/schemas/query.schema.ts"><b>DateRangeSchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#DateRangeSchema" title="src/modules/leadRepoCare.queryside/schemas/query.schema.ts"><b>DateRangeSchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#domainDatabaseProviders" title="src/modules/database/domain/domain.database.providers.ts"><b>domainDatabaseProviders</b>&nbsp;&nbsp;&nbsp;(src/.../domain.database.providers.ts)</a>
                        </li>
                        <li>
                            <a href="#DomainSchema" title="src/modules/leadRepo.domain/schemas/domain.schema.ts"><b>DomainSchema</b>&nbsp;&nbsp;&nbsp;(src/.../domain.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#DomainSchema" title="src/modules/leadRepoCare.domain/schemas/domain.schema.ts"><b>DomainSchema</b>&nbsp;&nbsp;&nbsp;(src/.../domain.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#EventHandlers" title="src/modules/lead.domain/events/index.ts"><b>EventHandlers</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#EventHandlers" title="src/modules/lead.queryside/events/index.ts"><b>EventHandlers</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#EventHandlers" title="src/modules/leadCare.domain/events/index.ts"><b>EventHandlers</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#EventHandlers" title="src/modules/leadCare.queryside/events/index.ts"><b>EventHandlers</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#ExploitHistorySchema" title="src/modules/lead.domain/schemas/cqrs.domain.schema.ts"><b>ExploitHistorySchema</b>&nbsp;&nbsp;&nbsp;(src/.../cqrs.domain.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#ExploitHistorySchema" title="src/modules/lead.queryside/schemas/query.schema.ts"><b>ExploitHistorySchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#ExploitHistorySchema" title="src/modules/leadCare.domain/schemas/cqrs.domain.schema.ts"><b>ExploitHistorySchema</b>&nbsp;&nbsp;&nbsp;(src/.../cqrs.domain.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#ExploitHistorySchema" title="src/modules/leadCare.queryside/schemas/query.schema.ts"><b>ExploitHistorySchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#ExploitHistorySchema" title="src/modules/leadJob/infra/schema.ts"><b>ExploitHistorySchema</b>&nbsp;&nbsp;&nbsp;(src/.../schema.ts)</a>
                        </li>
                        <li>
                            <a href="#generateCode" title="src/modules/shared/utils/codegen.ts"><b>generateCode</b>&nbsp;&nbsp;&nbsp;(src/.../codegen.ts)</a>
                        </li>
                        <li>
                            <a href="#getEventStreamSchema" title="src/modules/shared/schema/schema.base.ts"><b>getEventStreamSchema</b>&nbsp;&nbsp;&nbsp;(src/.../schema.base.ts)</a>
                        </li>
                        <li>
                            <a href="#getKeywords" title="src/modules/shared/utils/utils.ts"><b>getKeywords</b>&nbsp;&nbsp;&nbsp;(src/.../utils.ts)</a>
                        </li>
                        <li>
                            <a href="#identifySchema" title="src/modules/lead.domain/schemas/cqrs.domain.schema.ts"><b>identifySchema</b>&nbsp;&nbsp;&nbsp;(src/.../cqrs.domain.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#identifySchema" title="src/modules/lead.queryside/schemas/query.schema.ts"><b>identifySchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#identifySchema" title="src/modules/leadCare.domain/schemas/cqrs.domain.schema.ts"><b>identifySchema</b>&nbsp;&nbsp;&nbsp;(src/.../cqrs.domain.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#identifySchema" title="src/modules/leadCare.queryside/schemas/query.schema.ts"><b>identifySchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#instance" title="src/modules/database/domain/domain.database.providers.ts"><b>instance</b>&nbsp;&nbsp;&nbsp;(src/.../domain.database.providers.ts)</a>
                        </li>
                        <li>
                            <a href="#instance" title="src/modules/database/query/query.database.providers.ts"><b>instance</b>&nbsp;&nbsp;&nbsp;(src/.../query.database.providers.ts)</a>
                        </li>
                        <li>
                            <a href="#LeadjobProviders" title="src/modules/leadJob/infra/providers.ts"><b>LeadjobProviders</b>&nbsp;&nbsp;&nbsp;(src/.../providers.ts)</a>
                        </li>
                        <li>
                            <a href="#LeadjobSchema" title="src/modules/leadJob/infra/schema.ts"><b>LeadjobSchema</b>&nbsp;&nbsp;&nbsp;(src/.../schema.ts)</a>
                        </li>
                        <li>
                            <a href="#LeadRepoCareConfigHotSchema" title="src/modules/leadRepoCare.queryside/schemas/query.schema.ts"><b>LeadRepoCareConfigHotSchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#LeadRepoCareConfigSchema" title="src/modules/leadRepoCare.queryside/schemas/query.schema.ts"><b>LeadRepoCareConfigSchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#LeadRepoCareDomainCommandHandlers" title="src/modules/leadRepoCare.domain/commands/handlers/index.ts"><b>LeadRepoCareDomainCommandHandlers</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#LeadRepoCareDomainProvider" title="src/modules/leadRepoCare.domain/providers/domain.provider.ts"><b>LeadRepoCareDomainProvider</b>&nbsp;&nbsp;&nbsp;(src/.../domain.provider.ts)</a>
                        </li>
                        <li>
                            <a href="#LeadRepoCareQueryCommandHandlers" title="src/modules/leadRepoCare.queryside/command/handlers/index.ts"><b>LeadRepoCareQueryCommandHandlers</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#LeadRepoCareQueryEventHandlers" title="src/modules/leadRepoCare.queryside/events/handlers/index.ts"><b>LeadRepoCareQueryEventHandlers</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#LeadRepoCareQueryProviders" title="src/modules/leadRepoCare.queryside/providers/query.provider.ts"><b>LeadRepoCareQueryProviders</b>&nbsp;&nbsp;&nbsp;(src/.../query.provider.ts)</a>
                        </li>
                        <li>
                            <a href="#LeadRepoCareSchema" title="src/modules/leadRepoCare.queryside/schemas/query.schema.ts"><b>LeadRepoCareSchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#LeadRepoConfigHotSchema" title="src/modules/leadRepo.queryside/schemas/query.schema.ts"><b>LeadRepoConfigHotSchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#LeadRepoConfigSchema" title="src/modules/leadRepo.queryside/schemas/query.schema.ts"><b>LeadRepoConfigSchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#LeadRepoDomainCommandHandlers" title="src/modules/leadRepo.domain/commands/handlers/index.ts"><b>LeadRepoDomainCommandHandlers</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#LeadRepoDomainProvider" title="src/modules/leadRepo.domain/providers/domain.provider.ts"><b>LeadRepoDomainProvider</b>&nbsp;&nbsp;&nbsp;(src/.../domain.provider.ts)</a>
                        </li>
                        <li>
                            <a href="#LeadRepoQueryCommandHandlers" title="src/modules/leadRepo.queryside/command/handlers/index.ts"><b>LeadRepoQueryCommandHandlers</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#LeadRepoQueryEventHandlers" title="src/modules/leadRepo.queryside/events/handlers/index.ts"><b>LeadRepoQueryEventHandlers</b>&nbsp;&nbsp;&nbsp;(src/.../index.ts)</a>
                        </li>
                        <li>
                            <a href="#LeadRepoQueryProviders" title="src/modules/leadRepo.queryside/providers/query.provider.ts"><b>LeadRepoQueryProviders</b>&nbsp;&nbsp;&nbsp;(src/.../query.provider.ts)</a>
                        </li>
                        <li>
                            <a href="#LeadRepoSchema" title="src/modules/leadRepo.queryside/schemas/query.schema.ts"><b>LeadRepoSchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#moment" title="src/modules/lead.queryside/service.ts"><b>moment</b>&nbsp;&nbsp;&nbsp;(src/.../service.ts)</a>
                        </li>
                        <li>
                            <a href="#moment" title="src/modules/leadCare.queryside/service.ts"><b>moment</b>&nbsp;&nbsp;&nbsp;(src/.../service.ts)</a>
                        </li>
                        <li>
                            <a href="#moment" title="src/modules/lead-history/repository/query.repository.ts"><b>moment</b>&nbsp;&nbsp;&nbsp;(src/.../query.repository.ts)</a>
                        </li>
                        <li>
                            <a href="#moment" title="src/modules/leadCare-history/repository/query.repository.ts"><b>moment</b>&nbsp;&nbsp;&nbsp;(src/.../query.repository.ts)</a>
                        </li>
                        <li>
                            <a href="#moment" title="src/modules/leadSource/repository/query.repository.ts"><b>moment</b>&nbsp;&nbsp;&nbsp;(src/.../query.repository.ts)</a>
                        </li>
                        <li>
                            <a href="#moment" title="src/modules/shared/mapper/lead.mapper.ts"><b>moment</b>&nbsp;&nbsp;&nbsp;(src/.../lead.mapper.ts)</a>
                        </li>
                        <li>
                            <a href="#moment" title="src/modules/shared/mapper/leadCare.mapper.ts"><b>moment</b>&nbsp;&nbsp;&nbsp;(src/.../leadCare.mapper.ts)</a>
                        </li>
                        <li>
                            <a href="#momentTz" title="src/modules/lead.queryside/service.ts"><b>momentTz</b>&nbsp;&nbsp;&nbsp;(src/.../service.ts)</a>
                        </li>
                        <li>
                            <a href="#momentTz" title="src/modules/leadJob/application/service.ts"><b>momentTz</b>&nbsp;&nbsp;&nbsp;(src/.../service.ts)</a>
                        </li>
                        <li>
                            <a href="#Mongoose" title="src/modules/database/domain/domain.database.providers.ts"><b>Mongoose</b>&nbsp;&nbsp;&nbsp;(src/.../domain.database.providers.ts)</a>
                        </li>
                        <li>
                            <a href="#Mongoose" title="src/modules/database/query/query.database.providers.ts"><b>Mongoose</b>&nbsp;&nbsp;&nbsp;(src/.../query.database.providers.ts)</a>
                        </li>
                        <li>
                            <a href="#NotificationInstanceSchema" title="src/modules/leadRepo.queryside/schemas/query.schema.ts"><b>NotificationInstanceSchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#NotificationInstanceSchema" title="src/modules/leadRepoCare.queryside/schemas/query.schema.ts"><b>NotificationInstanceSchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#NotificationSchema" title="src/modules/leadRepo.queryside/schemas/query.schema.ts"><b>NotificationSchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#NotificationSchema" title="src/modules/leadRepoCare.queryside/schemas/query.schema.ts"><b>NotificationSchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#objectMapper" title="src/modules/shared/mapper/lead.mapper.ts"><b>objectMapper</b>&nbsp;&nbsp;&nbsp;(src/.../lead.mapper.ts)</a>
                        </li>
                        <li>
                            <a href="#objectMapper" title="src/modules/shared/mapper/leadCare.mapper.ts"><b>objectMapper</b>&nbsp;&nbsp;&nbsp;(src/.../leadCare.mapper.ts)</a>
                        </li>
                        <li>
                            <a href="#payloadSchema" title="src/modules/lead.domain/schemas/cqrs.domain.schema.ts"><b>payloadSchema</b>&nbsp;&nbsp;&nbsp;(src/.../cqrs.domain.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#payloadSchema" title="src/modules/leadCare.domain/schemas/cqrs.domain.schema.ts"><b>payloadSchema</b>&nbsp;&nbsp;&nbsp;(src/.../cqrs.domain.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#PayloadSchema" title="src/modules/leadRepo.domain/schemas/domain.schema.ts"><b>PayloadSchema</b>&nbsp;&nbsp;&nbsp;(src/.../domain.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#PayloadSchema" title="src/modules/leadRepoCare.domain/schemas/domain.schema.ts"><b>PayloadSchema</b>&nbsp;&nbsp;&nbsp;(src/.../domain.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#PROFILE_CODE_LENGTH" title="src/modules/shared/utils/codegen.ts"><b>PROFILE_CODE_LENGTH</b>&nbsp;&nbsp;&nbsp;(src/.../codegen.ts)</a>
                        </li>
                        <li>
                            <a href="#promiseWhile" title="src/modules/shared/utils/promiseWhile.ts"><b>promiseWhile</b>&nbsp;&nbsp;&nbsp;(src/.../promiseWhile.ts)</a>
                        </li>
                        <li>
                            <a href="#Public" title="src/common/decorators/public.decorator.ts"><b>Public</b>&nbsp;&nbsp;&nbsp;(src/.../public.decorator.ts)</a>
                        </li>
                        <li>
                            <a href="#queryDatabaseProviders" title="src/modules/database/query/query.database.providers.ts"><b>queryDatabaseProviders</b>&nbsp;&nbsp;&nbsp;(src/.../query.database.providers.ts)</a>
                        </li>
                        <li>
                            <a href="#QueryProviders" title="src/modules/c-lead.queryside/providers/query.cqrs.providers.ts"><b>QueryProviders</b>&nbsp;&nbsp;&nbsp;(src/.../query.cqrs.providers.ts)</a>
                        </li>
                        <li>
                            <a href="#QueryProviders" title="src/modules/c-leadCare.queryside/providers/query.cqrs.providers.ts"><b>QueryProviders</b>&nbsp;&nbsp;&nbsp;(src/.../query.cqrs.providers.ts)</a>
                        </li>
                        <li>
                            <a href="#QueryProviders" title="src/modules/code-generate/providers/query.cqrs.providers.ts"><b>QueryProviders</b>&nbsp;&nbsp;&nbsp;(src/.../query.cqrs.providers.ts)</a>
                        </li>
                        <li>
                            <a href="#QueryProviders" title="src/modules/history-import/providers/query.cqrs.providers.ts"><b>QueryProviders</b>&nbsp;&nbsp;&nbsp;(src/.../query.cqrs.providers.ts)</a>
                        </li>
                        <li>
                            <a href="#QueryProviders" title="src/modules/employee/providers/query.cqrs.providers.ts"><b>QueryProviders</b>&nbsp;&nbsp;&nbsp;(src/.../query.cqrs.providers.ts)</a>
                        </li>
                        <li>
                            <a href="#QueryProviders" title="src/modules/lead-history/providers/query.cqrs.providers.ts"><b>QueryProviders</b>&nbsp;&nbsp;&nbsp;(src/.../query.cqrs.providers.ts)</a>
                        </li>
                        <li>
                            <a href="#QueryProviders" title="src/modules/lead.queryside/providers/query.cqrs.providers.ts"><b>QueryProviders</b>&nbsp;&nbsp;&nbsp;(src/.../query.cqrs.providers.ts)</a>
                        </li>
                        <li>
                            <a href="#QueryProviders" title="src/modules/leadCare-history/providers/query.cqrs.providers.ts"><b>QueryProviders</b>&nbsp;&nbsp;&nbsp;(src/.../query.cqrs.providers.ts)</a>
                        </li>
                        <li>
                            <a href="#QueryProviders" title="src/modules/leadCare.queryside/providers/query.cqrs.providers.ts"><b>QueryProviders</b>&nbsp;&nbsp;&nbsp;(src/.../query.cqrs.providers.ts)</a>
                        </li>
                        <li>
                            <a href="#QueryProviders" title="src/modules/leadSource/providers/query.cqrs.providers.ts"><b>QueryProviders</b>&nbsp;&nbsp;&nbsp;(src/.../query.cqrs.providers.ts)</a>
                        </li>
                        <li>
                            <a href="#QueryProviders" title="src/modules/raw/providers/query.cqrs.providers.ts"><b>QueryProviders</b>&nbsp;&nbsp;&nbsp;(src/.../query.cqrs.providers.ts)</a>
                        </li>
                        <li>
                            <a href="#QuerySchema" title="src/modules/c-lead.queryside/schemas/query.schema.ts"><b>QuerySchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#QuerySchema" title="src/modules/c-leadCare.queryside/schemas/query.schema.ts"><b>QuerySchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#QuerySchema" title="src/modules/code-generate/schemas/query.schema.ts"><b>QuerySchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#QuerySchema" title="src/modules/history-import/schemas/query.schema.ts"><b>QuerySchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#QuerySchema" title="src/modules/employee/schemas/query.schema.ts"><b>QuerySchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#QuerySchema" title="src/modules/lead-history/schemas/query.schema.ts"><b>QuerySchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#QuerySchema" title="src/modules/lead.queryside/schemas/query.schema.ts"><b>QuerySchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#QuerySchema" title="src/modules/leadCare-history/schemas/query.schema.ts"><b>QuerySchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#QuerySchema" title="src/modules/leadCare.queryside/schemas/query.schema.ts"><b>QuerySchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#QuerySchema" title="src/modules/leadRepo.queryside/schemas/query.schema.ts"><b>QuerySchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#QuerySchema" title="src/modules/leadRepoCare.queryside/schemas/query.schema.ts"><b>QuerySchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#QuerySchema" title="src/modules/leadSource/schemas/query.schema.ts"><b>QuerySchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#QuerySchema" title="src/modules/raw/schemas/query.schema.ts"><b>QuerySchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#RABBITMQ_URL" title="src/modules/mgs-sender/mgs-sender.module.ts"><b>RABBITMQ_URL</b>&nbsp;&nbsp;&nbsp;(src/.../mgs-sender.module.ts)</a>
                        </li>
                        <li>
                            <a href="#recordNumber" title="src/modules/listener/listener.controller.ts"><b>recordNumber</b>&nbsp;&nbsp;&nbsp;(src/.../listener.controller.ts)</a>
                        </li>
                        <li>
                            <a href="#removeSign" title="src/modules/shared/utils/utils.ts"><b>removeSign</b>&nbsp;&nbsp;&nbsp;(src/.../utils.ts)</a>
                        </li>
                        <li>
                            <a href="#request" title="src/modules/lead.domain/service.ts"><b>request</b>&nbsp;&nbsp;&nbsp;(src/.../service.ts)</a>
                        </li>
                        <li>
                            <a href="#request" title="src/modules/leadCare.domain/service.ts"><b>request</b>&nbsp;&nbsp;&nbsp;(src/.../service.ts)</a>
                        </li>
                        <li>
                            <a href="#Roles" title="src/common/decorators/roles.decorator.ts"><b>Roles</b>&nbsp;&nbsp;&nbsp;(src/.../roles.decorator.ts)</a>
                        </li>
                        <li>
                            <a href="#splitText" title="src/modules/shared/utils/utils.ts"><b>splitText</b>&nbsp;&nbsp;&nbsp;(src/.../utils.ts)</a>
                        </li>
                        <li>
                            <a href="#surveySchema" title="src/modules/lead.domain/schemas/cqrs.domain.schema.ts"><b>surveySchema</b>&nbsp;&nbsp;&nbsp;(src/.../cqrs.domain.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#surveySchema" title="src/modules/lead.queryside/schemas/query.schema.ts"><b>surveySchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#surveySchema" title="src/modules/leadCare.domain/schemas/cqrs.domain.schema.ts"><b>surveySchema</b>&nbsp;&nbsp;&nbsp;(src/.../cqrs.domain.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#surveySchema" title="src/modules/leadCare.queryside/schemas/query.schema.ts"><b>surveySchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#SurveySchema" title="src/modules/leadRepo.queryside/schemas/query.schema.ts"><b>SurveySchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#SurveySchema" title="src/modules/leadRepoCare.queryside/schemas/query.schema.ts"><b>SurveySchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#TakeCareSchema" title="src/modules/lead.domain/schemas/cqrs.domain.schema.ts"><b>TakeCareSchema</b>&nbsp;&nbsp;&nbsp;(src/.../cqrs.domain.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#TakeCareSchema" title="src/modules/lead.queryside/schemas/query.schema.ts"><b>TakeCareSchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#TakeCareSchema" title="src/modules/leadCare.domain/schemas/cqrs.domain.schema.ts"><b>TakeCareSchema</b>&nbsp;&nbsp;&nbsp;(src/.../cqrs.domain.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#TakeCareSchema" title="src/modules/leadCare.queryside/schemas/query.schema.ts"><b>TakeCareSchema</b>&nbsp;&nbsp;&nbsp;(src/.../query.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#TakeCareSchema" title="src/modules/leadJob/infra/schema.ts"><b>TakeCareSchema</b>&nbsp;&nbsp;&nbsp;(src/.../schema.ts)</a>
                        </li>
                        <li>
                            <a href="#timezone" title="src/modules/lead.domain/service.ts"><b>timezone</b>&nbsp;&nbsp;&nbsp;(src/.../service.ts)</a>
                        </li>
                        <li>
                            <a href="#timezone" title="src/modules/lead.queryside/service.ts"><b>timezone</b>&nbsp;&nbsp;&nbsp;(src/.../service.ts)</a>
                        </li>
                        <li>
                            <a href="#timezone" title="src/modules/leadCare.domain/service.ts"><b>timezone</b>&nbsp;&nbsp;&nbsp;(src/.../service.ts)</a>
                        </li>
                        <li>
                            <a href="#timezone" title="src/modules/leadCare.queryside/service.ts"><b>timezone</b>&nbsp;&nbsp;&nbsp;(src/.../service.ts)</a>
                        </li>
                        <li>
                            <a href="#updateArrData" title="src/modules/shared/utils/updateArrData.ts"><b>updateArrData</b>&nbsp;&nbsp;&nbsp;(src/.../updateArrData.ts)</a>
                        </li>
                        <li>
                            <a href="#Usr" title="src/modules/shared/services/user/decorator/user.decorator.ts"><b>Usr</b>&nbsp;&nbsp;&nbsp;(src/.../user.decorator.ts)</a>
                        </li>
                        <li>
                            <a href="#uuid" title="src/modules/lead.domain/service.ts"><b>uuid</b>&nbsp;&nbsp;&nbsp;(src/.../service.ts)</a>
                        </li>
                        <li>
                            <a href="#uuid" title="src/modules/leadCare.domain/service.ts"><b>uuid</b>&nbsp;&nbsp;&nbsp;(src/.../service.ts)</a>
                        </li>
                        <li>
                            <a href="#uuid" title="src/modules/lead.domain/schemas/cqrs.domain.schema.ts"><b>uuid</b>&nbsp;&nbsp;&nbsp;(src/.../cqrs.domain.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#uuid" title="src/modules/leadCare.domain/schemas/cqrs.domain.schema.ts"><b>uuid</b>&nbsp;&nbsp;&nbsp;(src/.../cqrs.domain.schema.ts)</a>
                        </li>
                        <li>
                            <a href="#uuid" title="src/modules/leadJob/application/service.auth.ts"><b>uuid</b>&nbsp;&nbsp;&nbsp;(src/.../service.auth.ts)</a>
                        </li>
                        <li>
                            <a href="#uuid" title="src/modules/leadJob/application/service.ts"><b>uuid</b>&nbsp;&nbsp;&nbsp;(src/.../service.ts)</a>
                        </li>
                        <li>
                            <a href="#validListStatus" title="src/modules/lead.queryside/service.ts"><b>validListStatus</b>&nbsp;&nbsp;&nbsp;(src/.../service.ts)</a>
                        </li>
                        <li>
                            <a href="#validReportStatus" title="src/modules/lead.queryside/service.ts"><b>validReportStatus</b>&nbsp;&nbsp;&nbsp;(src/.../service.ts)</a>
                        </li>
                        <li>
                            <a href="#validReportStatus" title="src/modules/lead.queryside/repository/query.repository.ts"><b>validReportStatus</b>&nbsp;&nbsp;&nbsp;(src/.../query.repository.ts)</a>
                        </li>
                        <li>
                            <a href="#XLSX" title="src/modules/lead.domain/service.extend.ts"><b>XLSX</b>&nbsp;&nbsp;&nbsp;(src/.../service.extend.ts)</a>
                        </li>
                        <li>
                            <a href="#XLSX" title="src/modules/lead.domain/service.ts"><b>XLSX</b>&nbsp;&nbsp;&nbsp;(src/.../service.ts)</a>
                        </li>
                        <li>
                            <a href="#XLSX" title="src/modules/leadCare.domain/service.ts"><b>XLSX</b>&nbsp;&nbsp;&nbsp;(src/.../service.ts)</a>
                        </li>
                        <li>
                            <a href="#XLSX" title="src/modules/leadCare.domain/service.extend.ts"><b>XLSX</b>&nbsp;&nbsp;&nbsp;(src/.../service.extend.ts)</a>
                        </li>
                    </ul>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    <h3>src/modules/auth/app.roles.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="acRoles"></a>
                        <span class="name">
                            <b>
                            acRoles</b>
                            <a href="#acRoles"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="../miscellaneous/variables.html#Roles" target="_self" >RolesBuilder</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new RolesBuilder()</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/lead.domain/schemas/cqrs.domain.schema.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="addressSchema"></a>
                        <span class="name">
                            <b>
                            addressSchema</b>
                            <a href="#addressSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    nation: { type: String },
    province: { type: String },
    district: { type: String },
    ward: { type: String },
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="beautifyUnique"></a>
                        <span class="name">
                            <b>
                            beautifyUnique</b>
                            <a href="#beautifyUnique"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&#x27;mongoose-beautiful-unique-validation&#x27;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="categorySchema"></a>
                        <span class="name">
                            <b>
                            categorySchema</b>
                            <a href="#categorySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    id: { type: String },
    name: { type: String },
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="CqrsDomainSchema"></a>
                        <span class="name">
                            <b>
                            CqrsDomainSchema</b>
                            <a href="#CqrsDomainSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    _id: { type: String, },
    id: { type: String, index: true },
    streamId: { type: String, index: true },
    aggregate: String,
    aggregateId: String,
    context: String,
    streamRevision: Number,
    commitId: String,
    commitSequence: Number,
    commitStamp: { type: Date, default: () &#x3D;&gt; Date.now() },
    eventName: { type: String },
    payload: payloadSchema,
    payloads: [payloadSchema],
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="ExploitHistorySchema"></a>
                        <span class="name">
                            <b>
                            ExploitHistorySchema</b>
                            <a href="#ExploitHistorySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema(
  {
      status: { type: String },
      updatedAt: { type: Date },
      updatedBy: { type: String },
      takeCareId: {type: String},
  },
  { _id: false }
)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="identifySchema"></a>
                        <span class="name">
                            <b>
                            identifySchema</b>
                            <a href="#identifySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    type: { type: String },
    num: { type: String },
    date: { type: String },
    issueBy: { type: String },
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="payloadSchema"></a>
                        <span class="name">
                            <b>
                            payloadSchema</b>
                            <a href="#payloadSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    _id: { type: String, default: uuid.v4 },
    id: { type: String, index: true },
    email: { type: String },
    name: { type: String },
    profileUrl: { type: String },
    address: { type: String },
    phone: { type: String },
    pos: { type: Object },
    type: { type: String },
    status: { type: String, default: StatusEnum.GREEN },
    lifeCycleStatus: { type: String, default: LifeCycleStatusEnum.IN_POOL },
    processBy: { type: String, default: null },
    t0: { type: Date },
    t1: { type: Date },
    t2: { type: Date },
    t3: { type: Date },
    timeOut: { type: Date, default: null },
    timezoneclient: { type: String },
    reason: { type: [String] },
    notes: { type: Object },

    timestamp: { type: Number },
    customerId: { type: String },

    description: { type: String, default: &#x27;&#x27; },
    active: { type: Boolean, default: true },
    softDelete: { type: Boolean, default: false },
    modifiedBy: { type: String },

    createdDate: { type: Date, default: () &#x3D;&gt; Date.now() },
    updatedDate: { type: Date, default: () &#x3D;&gt; Date.now() },
    eventName: { type: String },
    actionName: { type: String },
    revision: { type: Number, index: true },
    source: { type: String },
    code: { type: String },
    customer: { type: Object },
    employee: { type: Object },
    property: { type: Object },
    processedDate: { type: Date, default: () &#x3D;&gt; Date.now() },
    assignedDate: { type: Date },
    processedHistory: { type: Array },
    isCalled: { type: Boolean, default: false },
    advisingType: { type: String },
    price: { type: Number },
    categoryId: { type: String },
    desirablePrice: { type: String },
    category: { type: categorySchema },
    usedFloorArea: { type: Number },

    updatedName: { type: String },
    updatedPhone: { type: String },
    updatedEmail: { type: String },
    updatedProfileUrl: { type: String },

    isInNeed: { type: String },
    reasonNoNeed: { type: String },
    otherReason: { type: String },
    interestedProduct: { type: Object },
    direction: { type: Object },
    needLoan: { type: Boolean, default: false },
    isAppointment: { type: Boolean, default: false },
    isVisited: { type: Boolean, default: false },
    note: { type: String },
    callHistory: { type: Array },
    importedBy: { type: Object },

    sex: { type: String },
    subPhone: { type: [Array], default: []},
    objAddress: {type: addressSchema, default: {}},
    needPersons: { type: String },
    identification: { type: identifySchema },
    incomePerMonth: { type: Number, default: 0},
    sourceIncome: { type: String },
    dob: { type: String },
    major: { type: String },
    maritalStatus: {type: String },
    surveys: {type: [Object], default: []},

    // Lead repository feature &#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;

    exploitStatus: { type: String, default: ExploitEnum.NEW },
    exploitStatusModifiedBy: { type: Object, default: null },
    exploitHistory: [ExploitHistorySchema],
    // assignedHistory: [AssignHistorySchema],
    // takeCareId: { type: String },
    takeCare: TakeCareSchema,
    assignDuration: { type: Number },

    repoId: { type: String },
    repoConfigCode: { type: String },
    isHot: { type: Boolean, default: false },
    expireTime: {type: Date},
    countAssign: { type: Number, default: 0 },
    notiUser: {type: Object},
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="surveySchema"></a>
                        <span class="name">
                            <b>
                            surveySchema</b>
                            <a href="#surveySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    name: { type: String },
    value: { type: String },
    code: { type: String },
    type: { type: String },
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="TakeCareSchema"></a>
                        <span class="name">
                            <b>
                            TakeCareSchema</b>
                            <a href="#TakeCareSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
  id: { type: String },
  name: { type: String },
  email: { type: String },
  phone: { type: String },
}, {_id: false})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="uuid"></a>
                        <span class="name">
                            <b>
                            uuid</b>
                            <a href="#uuid"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&#x27;uuid&#x27;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/lead.queryside/schemas/query.schema.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="addressSchema"></a>
                        <span class="name">
                            <b>
                            addressSchema</b>
                            <a href="#addressSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    nation: { type: String },
    province: { type: String },
    district: { type: String },
    ward: { type: String },
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="categorySchema"></a>
                        <span class="name">
                            <b>
                            categorySchema</b>
                            <a href="#categorySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    id: { type: String },
    name: { type: String },
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="ExploitHistorySchema"></a>
                        <span class="name">
                            <b>
                            ExploitHistorySchema</b>
                            <a href="#ExploitHistorySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    status: { type: String },
    updatedAt: { type: Date },
    updatedBy: { type: String },
    takeCareId: { type: String },
    takeCareInfo: TakeCareSchema,
}, { _id: false })</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="identifySchema"></a>
                        <span class="name">
                            <b>
                            identifySchema</b>
                            <a href="#identifySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    type: { type: String },
    num: { type: String },
    date: { type: String },
    issueBy: { type: String },
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="QuerySchema"></a>
                        <span class="name">
                            <b>
                            QuerySchema</b>
                            <a href="#QuerySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    _id: { type: String },
    id: { type: String, default: uuid.v4, index: true },
    email: { type: String },
    name: { type: String },
    profileUrl: { type: String },
    address: { type: String },
    phone: { type: String },
    pos: { type: Object },
    type: { type: String, default: CommonConst.TYPE.PRIMARY },
    status: { type: String, default: StatusEnum.GREEN },
    lifeCycleStatus: { type: String, default: LifeCycleStatusEnum.IN_POOL },
    processBy: { type: String, default: null },
    t0: { type: Date },
    t1: { type: Date },
    t2: { type: Date },
    t3: { type: Date },
    timeOut: { type: Date, default: null },
    timezoneclient: { type: String },
    notes: { type: Object },
    customerId: { type: String },

    createdDate: { type: Date, default: () &#x3D;&gt; Date.now() },
    updatedDate: { type: Date, default: () &#x3D;&gt; Date.now() },
    description: { type: String, default: &#x27;&#x27; },
    active: { type: Boolean, default: true },
    modifiedBy: { type: String, default: null },
    source: { type: String, default: null },
    code: { type: String },
    customer: { type: Object },
    employee: { type: Object },
    property: { type: Object },
    processedDate: { type: Date },
    assignedDate: { type: Date },
    processedHistory: { type: Array },
    isCalled: { type: Boolean, default: false },
    images: { type: Object },
    advisingType: { type: String },
    price: { type: Number },
    categoryId: { type: String },
    desirablePrice: { type: String },
    category: { type: categorySchema },
    usedFloorArea: { type: Number },

    updatedName: { type: String },
    updatedPhone: { type: String },
    updatedEmail: { type: String },
    updatedProfileUrl: { type: String },

    isInNeed: { type: String },
    reasonNoNeed: { type: String },
    otherReason: { type: String },
    reason: { type: [String] },
    interestedProduct: { type: Object },
    direction: { type: Object },
    needLoan: { type: Boolean, default: false },
    isAppointment: { type: Boolean, default: false },
    isVisited: { type: Boolean, default: false },
    note: { type: String },
    callHistory: { type: Array },
    importedBy: { type: Object },

    sex: { type: String },
    subPhone: { type: [Array], default: []},
    objAddress: { type: addressSchema, default: {}},
    needPersons: { type: String },
    identification: { type: identifySchema },
    incomePerMonth: { type: Number, default: 0},
    sourceIncome: { type: String },
    dob: { type: String },
    major: { type: String },
    maritalStatus: { type: String },
    surveys: { type: [Object], default: []},

    // Lead Repository + Automatic delivery
    exploitStatus: { type: String, default: ExploitEnum.NEW },
    exploitStatusModifiedBy: { type: Object },
    takeCare: TakeCareSchema,
    exploitHistory: [ExploitHistorySchema],
    assignDuration: { type: Number },
    countAssign: { type: Number, default: 0 },
    repoId: { type: String },
    repoConfigCode: { type: String },
    isHot: { type: Boolean, default: false },
    project: {type: Object, default: {}},
    expireTime: {type: Date},
    notiUser: {type: Object},
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="surveySchema"></a>
                        <span class="name">
                            <b>
                            surveySchema</b>
                            <a href="#surveySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    name: { type: String },
    value: { type: String },
    code: { type: String },
    type: { type: String },
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="TakeCareSchema"></a>
                        <span class="name">
                            <b>
                            TakeCareSchema</b>
                            <a href="#TakeCareSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
  id: { type: String },
  name: { type: String },
  email: { type: String },
  phone: { type: String },
}, {_id: false})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadCare.domain/schemas/cqrs.domain.schema.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="addressSchema"></a>
                        <span class="name">
                            <b>
                            addressSchema</b>
                            <a href="#addressSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    nation: { type: String },
    province: { type: String },
    district: { type: String },
    ward: { type: String },
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="beautifyUnique"></a>
                        <span class="name">
                            <b>
                            beautifyUnique</b>
                            <a href="#beautifyUnique"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&#x27;mongoose-beautiful-unique-validation&#x27;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="categorySchema"></a>
                        <span class="name">
                            <b>
                            categorySchema</b>
                            <a href="#categorySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    id: { type: String },
    name: { type: String },
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="CqrsDomainSchema"></a>
                        <span class="name">
                            <b>
                            CqrsDomainSchema</b>
                            <a href="#CqrsDomainSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    _id: { type: String, },
    id: { type: String, index: true },
    streamId: { type: String, index: true },
    aggregate: String,
    aggregateId: String,
    context: String,
    streamRevision: Number,
    commitId: String,
    commitSequence: Number,
    commitStamp: { type: Date, default: () &#x3D;&gt; Date.now() },
    eventName: { type: String },
    payload: payloadSchema,
    payloads: [payloadSchema],
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="ExploitHistorySchema"></a>
                        <span class="name">
                            <b>
                            ExploitHistorySchema</b>
                            <a href="#ExploitHistorySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema(
  {
      status: { type: String },
      updatedAt: { type: Date },
      updatedBy: { type: String },
      takeCareId: {type: String},
  },
  { _id: false }
)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="identifySchema"></a>
                        <span class="name">
                            <b>
                            identifySchema</b>
                            <a href="#identifySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    type: { type: String },
    num: { type: String },
    date: { type: String },
    issueBy: { type: String },
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="payloadSchema"></a>
                        <span class="name">
                            <b>
                            payloadSchema</b>
                            <a href="#payloadSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    _id: { type: String, default: uuid.v4 },
    id: { type: String, index: true },
    email: { type: String },
    name: { type: String },
    address: { type: String },
    phone: { type: String },
    pos: { type: Object },
    type: { type: String },
    status: { type: String, default: StatusEnum.GREEN },
    lifeCycleStatus: { type: String, default: LifeCycleStatusEnum.IN_POOL },
    processBy: { type: String, default: null },
    t0: { type: Date },
    t1: { type: Date },
    t2: { type: Date },
    t3: { type: Date },
    timeOut: { type: Date, default: null },
    timezoneclient: { type: String },
    reason: { type: String },
    notes: { type: Object },

    timestamp: { type: Number },
    customerId: { type: String },
    title: { type: String, default: &#x27;&#x27; },
    customData: { type: Object, default: {} },

    description: { type: String, default: &#x27;&#x27; },
    active: { type: Boolean, default: true },
    softDelete: { type: Boolean, default: false },
    modifiedBy: { type: String },

    createdDate: { type: Date, default: () &#x3D;&gt; Date.now() },
    updatedDate: { type: Date, default: () &#x3D;&gt; Date.now() },
    eventName: { type: String },
    actionName: { type: String },
    revision: { type: Number, index: true },
    source: { type: String },
    code: { type: String },
    customer: { type: Object },
    employee: { type: Object },
    property: { type: Object },
    processedDate: { type: Date},
    assignedDate: { type: Date},
    processedHistory: { type: Array },
    isCalled: { type: Boolean, default: false },
    advisingType: { type: String },
    price: { type: Number },
    categoryId: { type: String },
    desirablePrice: { type: String },
    category: { type: categorySchema },
    usedFloorArea: { type: Number },

    updatedName: { type: String },
    updatedPhone: { type: String },
    updatedEmail: { type: String },

    isInNeed: { type: String },
    reasonNoNeed: { type: String },
    otherReason: { type: String },
    interestedProduct: { type: Object },
    direction: { type: Object },
    needLoan: { type: Boolean, default: false },
    isAppointment: { type: Boolean, default: false },
    isVisited: { type: Boolean, default: false },
    note: { type: String },
    callHistory: { type: Array },
    importedBy: { type: Object },

    sex: { type: String },
    subPhone: { type: [Array], default: []},
    objAddress: {type: addressSchema, default: {}},
    needPersons: { type: String },
    identification: { type: identifySchema },
    incomePerMonth: { type: Number, default: 0},
    sourceIncome: { type: String },
    dob: { type: String },
    major: { type: String },
    maritalStatus: {type: String },
    surveys: {type: [Object], default: []},
    surveyAnswers: { type: [Object], default: []},
    isRequireSurvey: { type: Boolean, default: false },

    // Lead repository feature &#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;

    exploitStatus: { type: String, default: ExploitCareEnum.NEW },
    exploitStatusModifiedBy: { type: Object, default: null },
    exploitHistory: [ExploitHistorySchema],
    // assignedHistory: [AssignHistorySchema],
    // takeCareId: { type: String },
    takeCare: TakeCareSchema,
    assignDuration: { type: Number },
    expireTime: { type: Date },

    repoId: { type: String },
    repoConfigCode: { type: String },
    isHot: { type: Boolean, default: false },
    forBQL: { type: Boolean, default: false },
    dateEndWork:{type: Date , default:null},
    noteWork:{type:String},
    block:{type:String},
    idRepoConfig:{type:String},
    nameRepoConfig:{type:String},
    rateValue: { type: Number, default: null },
    
    rateDescription: { type: String, default: &#x27;&#x27; },
    conversations: {type: [Object]},
    leadJob: {type: Object},
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="surveySchema"></a>
                        <span class="name">
                            <b>
                            surveySchema</b>
                            <a href="#surveySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    name: { type: String },
    value: { type: String },
    code: { type: String },
    type: { type: String },
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="TakeCareSchema"></a>
                        <span class="name">
                            <b>
                            TakeCareSchema</b>
                            <a href="#TakeCareSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
  id: { type: String },
  name: { type: String },
  email: { type: String },
  phone: { type: String },
  code:{type:String},
  role:{type: Object}
}, {_id: false})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="uuid"></a>
                        <span class="name">
                            <b>
                            uuid</b>
                            <a href="#uuid"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&#x27;uuid&#x27;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadCare.queryside/schemas/query.schema.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="addressSchema"></a>
                        <span class="name">
                            <b>
                            addressSchema</b>
                            <a href="#addressSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    nation: { type: String },
    province: { type: String },
    district: { type: String },
    ward: { type: String },
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="categorySchema"></a>
                        <span class="name">
                            <b>
                            categorySchema</b>
                            <a href="#categorySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    id: { type: String },
    name: { type: String },
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="ExploitHistorySchema"></a>
                        <span class="name">
                            <b>
                            ExploitHistorySchema</b>
                            <a href="#ExploitHistorySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema(
  {
      status: { type: String },
      updatedAt: { type: Date },
      updatedBy: { type: String },
      takeCareId: {type: String},
  },
  { _id: false }
)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="identifySchema"></a>
                        <span class="name">
                            <b>
                            identifySchema</b>
                            <a href="#identifySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    type: { type: String },
    num: { type: String },
    date: { type: String },
    issueBy: { type: String },
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="QuerySchema"></a>
                        <span class="name">
                            <b>
                            QuerySchema</b>
                            <a href="#QuerySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    _id: { type: String },
    id: { type: String, default: uuid.v4, index: true },
    email: { type: String },
    name: { type: String },
    address: { type: String },
    phone: { type: String },
    pos: { type: Object },
    type: { type: String, default: CommonConst.TYPE.PRIMARY },
    status: { type: String, default: StatusEnum.GREEN },
    lifeCycleStatus: { type: String, default: LifeCycleStatusEnum.IN_POOL },
    processBy: { type: String, default: null },
    t0: { type: Date },
    t1: { type: Date },
    t2: { type: Date },
    t3: { type: Date },
    timeOut: { type: Date, default: null },
    timezoneclient: { type: String },
    notes: { type: Object },
    customerId: { type: String },
    title: { type: String, default: &#x27;&#x27; },
    customData: { type: Object, default: {} },
    repoType: { type: LeadRepoCareEnum },
    repoCode: { type: String, default: &#x27;&#x27; },

    createdDate: { type: Date, default: () &#x3D;&gt; Date.now() },
    updatedDate: { type: Date, default: () &#x3D;&gt; Date.now() },
    description: { type: String, default: &#x27;&#x27; },
    active: { type: Boolean, default: true },
    modifiedBy: { type: String, default: null },
    source: { type: String, default: null },
    code: { type: String },
    customer: { type: Object },
    employee: { type: Object },
    property: { type: Object },
    processedDate: { type: Date },
    assignedDate: { type: Date},
    processedHistory: { type: Array },
    isCalled: { type: Boolean, default: false },
    images: { type: Object },
    advisingType: { type: String },
    price: { type: Number },
    categoryId: { type: String },
    desirablePrice: { type: String },
    category: { type: categorySchema },
    usedFloorArea: { type: Number },

    updatedName: { type: String },
    updatedPhone: { type: String },
    updatedEmail: { type: String },

    isInNeed: { type: String },
    reasonNoNeed: { type: String },
    otherReason: { type: String },
    interestedProduct: { type: Object },
    direction: { type: Object },
    needLoan: { type: Boolean, default: false },
    isAppointment: { type: Boolean, default: false },
    isVisited: { type: Boolean, default: false },
    note: { type: String },
    callHistory: { type: Array },
    importedBy: { type: Object },

    sex: { type: String },
    subPhone: { type: [Array], default: []},
    objAddress: { type: addressSchema, default: {}},
    needPersons: { type: String },
    identification: { type: identifySchema },
    incomePerMonth: { type: Number, default: 0},
    sourceIncome: { type: String },
    dob: { type: String },
    major: { type: String },
    maritalStatus: { type: String },
    surveys: { type: [Object], default: []},
    surveyAnswers: { type: [Object], default: []},
    isRequireSurvey: { type: Boolean, default: false },

    // LeadCare Repository + Automatic delivery
    exploitStatus: { type: String, default: ExploitCareEnum.NEW },
    exploitStatusModifiedBy: { type: Object },
    takeCare: TakeCareSchema,
    exploitHistory: [ExploitHistorySchema],
    assignDuration: { type: Number },
    expireTime: { type: Date },

    repoId: { type: String },
    repoConfigCode: { type: String },
    isHot: { type: Boolean, default: false },
    project: {type: Object, default: {}},
    forBQL: { type: Boolean, default: false },
    dateEndWork:{type: Date , default:null},
    noteWork:{type:String},
    block:{type:String},
    idRepoConfig:{type:String},
    nameRepoConfig:{type:String},
    rateValue: { type: Number, default: null },
    
    rateDescription: { type: String, default: &#x27;&#x27; },

    target: {type:String}, // đối tượng yêu cầu
    reason: {type:String}, // lý do từ chối
    canSurvey: {type:Boolean}, // có thể survey hay không
    submitSurvey: {type:Boolean}, // KH đã trả lời survey chưa
    conversations: {type: [Object]},
    leadJob: {type: Object},
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="surveySchema"></a>
                        <span class="name">
                            <b>
                            surveySchema</b>
                            <a href="#surveySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    name: { type: String },
    value: { type: String },
    code: { type: String },
    type: { type: String },
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="TakeCareSchema"></a>
                        <span class="name">
                            <b>
                            TakeCareSchema</b>
                            <a href="#TakeCareSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
  id: { type: String },
  name: { type: String },
  email: { type: String },
  phone: { type: String },
  code: {type: String},
  role:{type: Object}
}, {_id: false})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/shared/schema/schema.base.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="BaseSchema"></a>
                        <span class="name">
                            <b>
                            BaseSchema</b>
                            <a href="#BaseSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>SchemaDefinition</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    _id: { type: String },
    id: { type: String, default: uuid.v4, index: true },
    description: { type: String },
    active: { type: Boolean },
    softDelete: { type: Boolean },
    modifiedBy: { type: String },
    timezoneClient: { type: String },
    createdDate: { type: Date, default: () &#x3D;&gt; Date.now() },
    updatedDate: { type: Date, default: () &#x3D;&gt; Date.now() },
}</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="getEventStreamSchema"></a>
                        <span class="name">
                            <b>
                            getEventStreamSchema</b>
                            <a href="#getEventStreamSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>(payload: Schema): Schema &#x3D;&gt; {
    return new Schema({
        ...BaseSchema,
        payload,
        payloads: [payload],
        streamId: { type: String, index: true },
        aggregate: String,
        aggregateId: String,
        context: String,
        streamRevision: Number,
        commitId: String,
        commitSequence: Number,
        commitStamp: { type: Date, default: () &#x3D;&gt; Date.now() },
        eventName: { type: String },
    });
}</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadRepo.domain/schemas/domain.schema.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="beautifyUnique"></a>
                        <span class="name">
                            <b>
                            beautifyUnique</b>
                            <a href="#beautifyUnique"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&#x27;mongoose-beautiful-unique-validation&#x27;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="DomainSchema"></a>
                        <span class="name">
                            <b>
                            DomainSchema</b>
                            <a href="#DomainSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>getEventStreamSchema(PayloadSchema)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="PayloadSchema"></a>
                        <span class="name">
                            <b>
                            PayloadSchema</b>
                            <a href="#PayloadSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    ...LeadRepoSchema,
    eventName: { type: String },
    actionName: { type: String },
    revision: { type: Number },
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadJob/infra/schema.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="beautifyUnique"></a>
                        <span class="name">
                            <b>
                            beautifyUnique</b>
                            <a href="#beautifyUnique"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&#x27;mongoose-beautiful-unique-validation&#x27;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="ExploitHistorySchema"></a>
                        <span class="name">
                            <b>
                            ExploitHistorySchema</b>
                            <a href="#ExploitHistorySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
      status: { type: String },
      updatedAt: { type: Date },
      updatedBy: { type: String },
      takeCareId: {type: String},
  },{ _id: false })</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LeadjobSchema"></a>
                        <span class="name">
                            <b>
                            LeadjobSchema</b>
                            <a href="#LeadjobSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
   _id: { type: String, default: uuid.v4 },
    id: { type: String, index: true },
    email: { type: String },
    name: { type: String },
    address: { type: String },
    phone: { type: String },
    pos: { type: Object },
    type: { type: String },
    status: { type: String, default: StatusEnum.GREEN },
    lifeCycleStatus: { type: String, default: LifeCycleStatusEnum.IN_POOL },
    processBy: { type: String, default: null },
    timeOut: { type: Date, default: null },
    timezoneclient: { type: String },
    reason: { type: String },
    notes: { type: Object },

    timestamp: { type: Number },
    customerId: { type: String },
    title: { type: String, default: &#x27;&#x27; },
    customData: { type: Object, default: {} },

    description: { type: String, default: &#x27;&#x27; },
    modifiedBy: { type: String },
    createdBy: { type: String },

    createdDate: { type: Date, default: () &#x3D;&gt; Date.now() },
    updatedDate: { type: Date, default: () &#x3D;&gt; Date.now() },
    eventName: { type: String },
    actionName: { type: String },
    source: { type: String },
    code: { type: String },
    customer: { type: Object },
    employee: { type: Object },
    property: { type: Object },
    processedDate: { type: Date},
    assignedDate: { type: Date },
    processedHistory: { type: Array },
    categoryId: { type: String },
    usedFloorArea: { type: Number },
    updatedName: { type: String },
    updatedPhone: { type: String },
    updatedEmail: { type: String },
    note: { type: String },
    importedBy: { type: Object },
    // Lead repository feature &#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;

    exploitStatus: { type: String, default: ExploitCareEnum.NEW },
    exploitStatusModifiedBy: { type: Object, default: null },
    exploitHistory: [ExploitHistorySchema],
    // assignedHistory: [AssignHistorySchema],
    // takeCareId: { type: String },
    takeCare: TakeCareSchema,
    assignDuration: { type: Number },
    expireTime: { type: Date },

    forBQL: { type: Boolean, default: true },
  // Khai báo các field của riêng từng chức năng
    project: {type:Object},
    leadCareId: {type:String},
    isMonthly: {type:Boolean , default : false},
    dateEndWork: {type:Date},
    block :{type:String},
    timeStartWork: {type:Date},
    timeEndWork : {type:Date},
    implementMaxDate: {type:Date},
    repoCode: { type: String, default: &#x27;&#x27; },
    idRepoConfig:{type:String},
    nameRepoConfig:{type:String},
    isFirstWorkId: {type: String}
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="TakeCareSchema"></a>
                        <span class="name">
                            <b>
                            TakeCareSchema</b>
                            <a href="#TakeCareSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
  id: { type: String },
  name: { type: String },
  email: { type: String },
  phone: { type: String },
  code: {type: String},
  role:{type:Object}
}, {_id: false})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadRepoCare.domain/schemas/domain.schema.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="beautifyUnique"></a>
                        <span class="name">
                            <b>
                            beautifyUnique</b>
                            <a href="#beautifyUnique"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&#x27;mongoose-beautiful-unique-validation&#x27;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="DomainSchema"></a>
                        <span class="name">
                            <b>
                            DomainSchema</b>
                            <a href="#DomainSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>getEventStreamSchema(PayloadSchema)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="PayloadSchema"></a>
                        <span class="name">
                            <b>
                            PayloadSchema</b>
                            <a href="#PayloadSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    ...LeadRepoCareSchema,
    eventName: { type: String },
    actionName: { type: String },
    revision: { type: Number },
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/auth/passport/jwt.strategy.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="callback"></a>
                        <span class="name">
                            <b>
                            callback</b>
                            <a href="#callback"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>(err, user, info) &#x3D;&gt; {
  let message;
  if (err) {
    return (err || new UnauthorizedException(info.message));
  } else if (typeof info !&#x3D;&#x3D; &#x27;undefined&#x27; || !user) {
    switch (info.message) {
      case &#x27;No auth token&#x27;:
      case &#x27;invalid signature&#x27;:
      case &#x27;jwt malformed&#x27;:
      case &#x27;invalid token&#x27;:
      case &#x27;invalid signature&#x27;:
        message &#x3D; &#x27;You must provide a valid authenticated access token&#x27;;
        break;
      case &#x27;jwt expired&#x27;:
        message &#x3D; &#x27;Your session has expired&#x27;;
        break;
      default:
        message &#x3D; info.message;
        break;
    }
    throw new UnauthorizedException(message);
  }
  return user;
}</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadCare.queryside/repository/query.repository.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="clc"></a>
                        <span class="name">
                            <b>
                            clc</b>
                            <a href="#clc"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&quot;cli-color&quot;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadJob/infra/repository.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="clc"></a>
                        <span class="name">
                            <b>
                            clc</b>
                            <a href="#clc"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&quot;cli-color&quot;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/shared/utils/codegen.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="CODE_CHAR_TEMPLATE"></a>
                        <span class="name">
                            <b>
                            CODE_CHAR_TEMPLATE</b>
                            <a href="#CODE_CHAR_TEMPLATE"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;abcdefghijklmnopqrstuvwxyz1234567890&#x27;</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="CUSTOMER_TYPE_CODE_LENGTH"></a>
                        <span class="name">
                            <b>
                            CUSTOMER_TYPE_CODE_LENGTH</b>
                            <a href="#CUSTOMER_TYPE_CODE_LENGTH"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>4</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="generateCode"></a>
                        <span class="name">
                            <b>
                            generateCode</b>
                            <a href="#generateCode"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>({
    length &#x3D; 10,
    prefix,
    postfix,
    template,
}: GenerateCodeOptions): string &#x3D;&gt; {
    let code &#x3D; &#x27;&#x27;;
    const characters &#x3D; template || CODE_CHAR_TEMPLATE;
    for (let i &#x3D; 0; i &lt; length; i +&#x3D; 1) {
        code +&#x3D; characters.charAt(
            Math.floor(Math.random() * characters.length)
        );
    }

    if (postfix) {
        code &#x3D; &#x60;${code}-${postfix.toLowerCase()}&#x60;;
    }

    if (prefix) {
        code &#x3D; &#x60;${prefix.toLowerCase()}-${code}&#x60;;
    }

    return code;
}</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="PROFILE_CODE_LENGTH"></a>
                        <span class="name">
                            <b>
                            PROFILE_CODE_LENGTH</b>
                            <a href="#PROFILE_CODE_LENGTH"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>6</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/lead.domain/commands/handlers/index.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="CommandHandlers"></a>
                        <span class="name">
                            <b>
                            CommandHandlers</b>
                            <a href="#CommandHandlers"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
    CreateCommandHandler,
    CompleteCommandHandler,
    FailCommandHandler,
    ProcessCommandHandler,
    UnprocessCommandHandler,
    AssignCommandHandler,
    ReassignCommandHandler,
    FailCommandHandler,
    PendingCommandHandler,
    UpdateCommandHandler,
    ExpiredCommandHandler,
    ChangeStatusCommandHandler,
    ImportLeadCommandHandler,
    RenewLeadCommandHandler,
]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/lead.queryside/commands/handlers/index.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="CommandHandlers"></a>
                        <span class="name">
                            <b>
                            CommandHandlers</b>
                            <a href="#CommandHandlers"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
    CreateQueryCommandHandler, 
    DeleteQueryCommandHandler,  
    ProcessQueryCommandHandler,
    UnprocessQueryCommandHandler,
    AssignQueryCommandHandler,
    ExpireQueryCommandHandler,
    ReassignQueryCommandHandler,
    FailQueryCommandHandler,
    PendingQueryCommandHandler,
    UpdateQueryCommandHandler,
    ImportLeadQueryCommandHandler,
    RenewLeadQueryCommandHandler,
]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadCare.domain/commands/handlers/index.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="CommandHandlers"></a>
                        <span class="name">
                            <b>
                            CommandHandlers</b>
                            <a href="#CommandHandlers"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
    CareCreateCommandHandler,
    CareCompleteCommandHandler,
    CareFailCommandHandler,
    CareProcessCommandHandler,
    CareUnprocessCommandHandler,
    CareAssignCommandHandler,
    CareReassignCommandHandler,
    CareFailCommandHandler,
    CarePendingCommandHandler,
    CareUpdateCommandHandler,
    CareExpiredCommandHandler,
    CareChangeStatusCommandHandler,
    CareImportLeadCareCommandHandler,
    CareCustomerCloseTicketCommandHandler
]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadCare.queryside/commands/handlers/index.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="CommandHandlers"></a>
                        <span class="name">
                            <b>
                            CommandHandlers</b>
                            <a href="#CommandHandlers"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
    CareCreateQueryCommandHandler,
    CareDeleteQueryCommandHandler,
    CareProcessQueryCommandHandler,
    CareUnprocessQueryCommandHandler,
    CareAssignQueryCommandHandler,
    CareExpireQueryCommandHandler,
    CareReassignQueryCommandHandler,
    CareFailQueryCommandHandler,
    CarePendingQueryCommandHandler,
    CareUpdateQueryCommandHandler,
    CareImportLeadCareQueryCommandHandler,
    CustomerClosedTicketCareLeadQueryCommandHandler
]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/main.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="configService"></a>
                        <span class="name">
                            <b>
                            configService</b>
                            <a href="#configService"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new ConfigService(&#x60;.env.${process.env.NODE_ENV}&#x60;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/listener/listener.controller.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="configService"></a>
                        <span class="name">
                            <b>
                            configService</b>
                            <a href="#configService"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new ConfigService(&#x60;.env.${process.env.NODE_ENV}&#x60;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="recordNumber"></a>
                        <span class="name">
                            <b>
                            recordNumber</b>
                            <a href="#recordNumber"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>parseInt(configService.get(&#x27;RECORD_NUMBER_IN_JOB&#x27;)) || 10</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/lead.domain/providers/cqrs.domain.providers.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="CqrsProviders"></a>
                        <span class="name">
                            <b>
                            CqrsProviders</b>
                            <a href="#CqrsProviders"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
  {
    provide: CommonConst.DOMAIN_MODEL_TOKEN,
    useFactory: (connection: Connection) &#x3D;&gt; connection.model(CommonConst.LEAD_EVENTS, CqrsDomainSchema),
    inject: [CommonConst.DOMAIN_CONNECTION_TOKEN],
  },
]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadCare.domain/providers/cqrs.domain.providers.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="CqrsProviders"></a>
                        <span class="name">
                            <b>
                            CqrsProviders</b>
                            <a href="#CqrsProviders"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
  {
    provide: CommonConst.DOMAIN_MODEL_TOKEN,
    useFactory: (connection: Connection) &#x3D;&gt; connection.model(CommonConst.LEAD_CARE_EVENTS, CqrsDomainSchema),
    inject: [CommonConst.DOMAIN_CONNECTION_TOKEN],
  },
]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadRepo.queryside/schemas/query.schema.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="DateRangeSchema"></a>
                        <span class="name">
                            <b>
                            DateRangeSchema</b>
                            <a href="#DateRangeSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema(
    {
        from: { type: Date },
        to: { type: Date },
    },
    { _id: false }
)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LeadRepoConfigHotSchema"></a>
                        <span class="name">
                            <b>
                            LeadRepoConfigHotSchema</b>
                            <a href="#LeadRepoConfigHotSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema(
    {
        assignDuration: { type: Number },
        orgChartIds: { type: [String] },
        orgChartQueue: [
            {
                id: { type: String },
                employeeQueue: { type: [Object] },
            },
        ],
        orgCharts: [
            {
                id: { type: String },
                name: { type: String },
                staffIds: {type: [String]}
            },
        ],
        notification: NotificationSchema,
        visiblePhone: { type: Boolean, default: false },
        manualDeliver: { type: Boolean, default: false },
        isWorkingTime: { type: Boolean, default: false },
        workingTime: [{
            _id: false,
            startTime: String,
            endTime: String
        }]
    },
    { _id: false }
)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LeadRepoConfigSchema"></a>
                        <span class="name">
                            <b>
                            LeadRepoConfigSchema</b>
                            <a href="#LeadRepoConfigSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema(
    {
        orgChartIds: { type: [String] },
        orgChartQueue: [
            {
                id: { type: String },
                employeeQueue: { type: [Object] },
            },
        ],
        orgCharts: [
            {
                id: { type: String },
                name: { type: String },
                staffIds: {type: [String]}
            },
        ],
        assignDuration: { type: Number },
        notification: NotificationSchema,
        projectId: { type: String },
        project: {
            id: { type: String },
            name: { type: String },
        },
        exploitTime: DateRangeSchema,
        code: { type: String },
        name: { type: String },
        active: { type: Boolean, default: true },
        surveys: [SurveySchema],
        visiblePhone: { type: Boolean, default: false },
        manualDeliver: { type: Boolean, default: false },
        isWorkingTime: { type: Boolean, default: false },
        workingTime: [{
            _id: false,
            startTime: String,
            endTime: String
        }]
    },
)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LeadRepoSchema"></a>
                        <span class="name">
                            <b>
                            LeadRepoSchema</b>
                            <a href="#LeadRepoSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>mongoose.SchemaDefinition</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    ...BaseSchema,
    code: { type: String, index: true },
    name: { type: String },
    configHot: LeadRepoConfigHotSchema,
    configs: [LeadRepoConfigSchema],
}</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="NotificationInstanceSchema"></a>
                        <span class="name">
                            <b>
                            NotificationInstanceSchema</b>
                            <a href="#NotificationInstanceSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema(
    {
        title: { type: String },
        content: { type: String },
        active: { type: Boolean },
    },
    { _id: false }
)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="NotificationSchema"></a>
                        <span class="name">
                            <b>
                            NotificationSchema</b>
                            <a href="#NotificationSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema(
    {
        email: NotificationInstanceSchema,
        web: NotificationInstanceSchema,
        app: NotificationInstanceSchema,
        sms: NotificationInstanceSchema,
        smsCus: NotificationInstanceSchema,
        notiUser: { type: Object }
    },
    { _id: false }
)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="QuerySchema"></a>
                        <span class="name">
                            <b>
                            QuerySchema</b>
                            <a href="#QuerySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    ...LeadRepoSchema,
    keywords: { type: [String], text: true, index: true },
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="SurveySchema"></a>
                        <span class="name">
                            <b>
                            SurveySchema</b>
                            <a href="#SurveySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema(
  {
      name: { type: String },
      value: { type: String },
      code: { type: String },
      type: { type: String },
  },
  { _id: false }
)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadRepoCare.queryside/schemas/query.schema.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="DateRangeSchema"></a>
                        <span class="name">
                            <b>
                            DateRangeSchema</b>
                            <a href="#DateRangeSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema(
    {
        from: { type: Date },
        to: { type: Date },
    },
    { _id: false }
)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LeadRepoCareConfigHotSchema"></a>
                        <span class="name">
                            <b>
                            LeadRepoCareConfigHotSchema</b>
                            <a href="#LeadRepoCareConfigHotSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema(
    {
        assignDuration: { type: Number },
        orgChartIds: { type: [String] },
        orgChartQueue: [
            {
                id: { type: String },
                employeeQueue: { type: [Object] },
            },
        ],
        orgCharts: [
            {
                id: { type: String },
                name: { type: String },
                staffIds: {type: [String]}
            },
        ],
        notification: NotificationSchema,
    },
    { _id: false }
)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LeadRepoCareConfigSchema"></a>
                        <span class="name">
                            <b>
                            LeadRepoCareConfigSchema</b>
                            <a href="#LeadRepoCareConfigSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema(
    {
        orgChartIds: { type: [String] },
        orgChartQueue: [
            {
                id: { type: String },
                employeeQueue: { type: [Object] },
            },
        ],
        orgCharts: [
            {
                id: { type: String },
                name: { type: String },
                staffIds: {type: [String]}
            },
        ],
        assignDuration: { type: Number },
        notification: NotificationSchema,
        projectId: { type: String },
        project: {
            id: { type: String },
            name: { type: String },
        },
        exploitTime: DateRangeSchema,
        code: { type: String },
        name: { type: String },
        active: { type: Boolean, default: true },
        surveys: [SurveySchema],
        target: { type: String },  // đối tượng yêu cầu
        forLoggedUser: { type: Boolean },  // dành cho user đã login
        forNotLoggedUser: { type: Boolean }, // dành cho user chưa login
        canSurvey: { type: Boolean },  // có thể survey hay không
        isStopTransfer: { type: Boolean, default: false }, // Ngừng nhận yêu cầu chuyển nhượng
    },
)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LeadRepoCareSchema"></a>
                        <span class="name">
                            <b>
                            LeadRepoCareSchema</b>
                            <a href="#LeadRepoCareSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>mongoose.SchemaDefinition</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{
    ...BaseSchema,
    code: { type: String, index: true },
    name: { type: String },
    configHot: LeadRepoCareConfigHotSchema,
    configs: [LeadRepoCareConfigSchema],
    type: { type: LeadRepoCareEnum }
}</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="NotificationInstanceSchema"></a>
                        <span class="name">
                            <b>
                            NotificationInstanceSchema</b>
                            <a href="#NotificationInstanceSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema(
    {
        title: { type: String },
        content: { type: String },
        active: { type: Boolean },
    },
    { _id: false }
)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="NotificationSchema"></a>
                        <span class="name">
                            <b>
                            NotificationSchema</b>
                            <a href="#NotificationSchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema(
    {
        email: NotificationInstanceSchema,
        web: NotificationInstanceSchema,
        app: NotificationInstanceSchema,
        sms: NotificationInstanceSchema,
    },
    { _id: false }
)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="QuerySchema"></a>
                        <span class="name">
                            <b>
                            QuerySchema</b>
                            <a href="#QuerySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    ...LeadRepoCareSchema,
    keywords: { type: [String], text: true, index: true },
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="SurveySchema"></a>
                        <span class="name">
                            <b>
                            SurveySchema</b>
                            <a href="#SurveySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema(
  {
      name: { type: String },
      value: { type: String },
      code: { type: String },
      type: { type: String },
  },
  { _id: false }
)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/database/domain/domain.database.providers.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="domainDatabaseProviders"></a>
                        <span class="name">
                            <b>
                            domainDatabaseProviders</b>
                            <a href="#domainDatabaseProviders"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
  {
    provide: CommonConst.DOMAIN_CONNECTION_TOKEN,
    useFactory: async (): Promise&lt;typeof mongoose&gt; &#x3D;&gt; {
      (instance as any).Promise &#x3D; global.Promise;
      const configService &#x3D; new ConfigService(&#x60;.env.${process.env.NODE_ENV}&#x60;);
      const mongoUrl &#x3D; configService.get(&#x27;MONGODB_URL_EVENT_STORE&#x27;);
      instance.set(&#x27;useCreateIndex&#x27;, true);
      return await instance.connect(mongoUrl, { useNewUrlParser: true }, (err) &#x3D;&gt; {
        if (err) {
          console.log(&#x27;Has error connect db&#x27;, err);
          throw err;
        }
      });
    },
  },
]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="instance"></a>
                        <span class="name">
                            <b>
                            instance</b>
                            <a href="#instance"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new Mongoose()</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="Mongoose"></a>
                        <span class="name">
                            <b>
                            Mongoose</b>
                            <a href="#Mongoose"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>mongoose.Mongoose</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/lead.domain/events/index.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="EventHandlers"></a>
                        <span class="name">
                            <b>
                            EventHandlers</b>
                            <a href="#EventHandlers"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[EventStreamCreatedEventHandler]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/lead.queryside/events/index.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="EventHandlers"></a>
                        <span class="name">
                            <b>
                            EventHandlers</b>
                            <a href="#EventHandlers"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[QueryEventHandler, ImportLeadQueryEventHandler, RenewLeadQueryEventHandler]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadCare.domain/events/index.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="EventHandlers"></a>
                        <span class="name">
                            <b>
                            EventHandlers</b>
                            <a href="#EventHandlers"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[CareEventStreamCreatedEventHandler]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadCare.queryside/events/index.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="EventHandlers"></a>
                        <span class="name">
                            <b>
                            EventHandlers</b>
                            <a href="#EventHandlers"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[CareQueryEventHandler, ImportLeadCareQueryEventHandler]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/shared/utils/utils.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="getKeywords"></a>
                        <span class="name">
                            <b>
                            getKeywords</b>
                            <a href="#getKeywords"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>(
  target: Record&lt;string, any&gt;,
  fields: string[]
): string[] &#x3D;&gt; {
  const keys &#x3D; [];
  Object.keys(target).forEach((key) &#x3D;&gt; {
    if (
      target[key] &amp;&amp;
      typeof target[key] &#x3D;&#x3D;&#x3D; &#x27;string&#x27; &amp;&amp;
      fields.includes(key)
    ) {
      const removedSign &#x3D; removeSign(target[key]);
      keys.push(...splitText(removedSign), removedSign);
    }
  });

  return [...new Set(keys)];
}</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="removeSign"></a>
                        <span class="name">
                            <b>
                            removeSign</b>
                            <a href="#removeSign"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>(str): string &#x3D;&gt; {
  str &#x3D; str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, &#x27;a&#x27;);
  str &#x3D; str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, &#x27;e&#x27;);
  str &#x3D; str.replace(/ì|í|ị|ỉ|ĩ/g, &#x27;i&#x27;);
  str &#x3D; str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, &#x27;o&#x27;);
  str &#x3D; str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, &#x27;u&#x27;);
  str &#x3D; str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, &#x27;y&#x27;);
  str &#x3D; str.replace(/đ/g, &#x27;d&#x27;);
  str &#x3D; str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, &#x27;A&#x27;);
  str &#x3D; str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, &#x27;E&#x27;);
  str &#x3D; str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, &#x27;I&#x27;);
  str &#x3D; str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, &#x27;O&#x27;);
  str &#x3D; str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, &#x27;U&#x27;);
  str &#x3D; str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, &#x27;Y&#x27;);
  str &#x3D; str.replace(/Đ/g, &#x27;D&#x27;);
  return str;
}</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="splitText"></a>
                        <span class="name">
                            <b>
                            splitText</b>
                            <a href="#splitText"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>(text: string): string[] &#x3D;&gt;
  text.match(/[^ ,.&lt;&gt;;:/?&#x27;&quot;\\|[\](){}\-_&#x3D;+~&#x60;!@#$%^&amp;*]+/g)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/database/query/query.database.providers.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="instance"></a>
                        <span class="name">
                            <b>
                            instance</b>
                            <a href="#instance"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new Mongoose()</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="Mongoose"></a>
                        <span class="name">
                            <b>
                            Mongoose</b>
                            <a href="#Mongoose"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>mongoose.Mongoose</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="queryDatabaseProviders"></a>
                        <span class="name">
                            <b>
                            queryDatabaseProviders</b>
                            <a href="#queryDatabaseProviders"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
  {
    provide: CommonConst.QUERY_CONNECTION_TOKEN,
    useFactory: async (): Promise&lt;typeof mongoose&gt; &#x3D;&gt; {
      (instance as any).Promise &#x3D; global.Promise;
      const configService &#x3D; new ConfigService(&#x60;.env.${process.env.NODE_ENV}&#x60;);
      const mongoUrl &#x3D; configService.get(&#x27;MONGODB_URL_READ_MODEL&#x27;);
      instance.set(&#x27;useCreateIndex&#x27;, true);
      return await instance.connect(mongoUrl, { useNewUrlParser: true }, (err) &#x3D;&gt; {
        if (err) {
          console.log(&#x27;Has error connect db&#x27;, err);
          throw err;
        }
      });
    }
  },
]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadJob/infra/providers.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LeadjobProviders"></a>
                        <span class="name">
                            <b>
                            LeadjobProviders</b>
                            <a href="#LeadjobProviders"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
  {
    provide: CommonConst.LEADJOB_QUERY_MODEL_TOKEN,
    useFactory: (connection: Connection) &#x3D;&gt;
      connection.model(CommonConst.LEADJOB_COLLECTION, LeadjobSchema),
    inject: [CommonConst.QUERY_CONNECTION_TOKEN],
  },
]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadRepoCare.domain/commands/handlers/index.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LeadRepoCareDomainCommandHandlers"></a>
                        <span class="name">
                            <b>
                            LeadRepoCareDomainCommandHandlers</b>
                            <a href="#LeadRepoCareDomainCommandHandlers"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
    CreateLeadRepoCareCommandHandler,
    UpdateLeadRepoCareCommandHandler,
    UpdateConfigLeadRepoCareCommandHandler,
    ActiveConfigLeadRepoCareCommandHandler,
]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadRepoCare.domain/providers/domain.provider.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LeadRepoCareDomainProvider"></a>
                        <span class="name">
                            <b>
                            LeadRepoCareDomainProvider</b>
                            <a href="#LeadRepoCareDomainProvider"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
  {
    provide: CommonConst.DOMAIN_MODEL_TOKEN,
    useFactory: (connection: Connection) &#x3D;&gt;
      connection.model(CommonConst.LEAD_REPO_CARE_EVENTS, DomainSchema),
    inject: [CommonConst.DOMAIN_CONNECTION_TOKEN],
  },
]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadRepoCare.queryside/command/handlers/index.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LeadRepoCareQueryCommandHandlers"></a>
                        <span class="name">
                            <b>
                            LeadRepoCareQueryCommandHandlers</b>
                            <a href="#LeadRepoCareQueryCommandHandlers"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
    CreateLeadRepoCareQueryCommandHandler,
    UpdateLeadRepoCareQueryCommandHandler,
]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadRepoCare.queryside/events/handlers/index.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LeadRepoCareQueryEventHandlers"></a>
                        <span class="name">
                            <b>
                            LeadRepoCareQueryEventHandlers</b>
                            <a href="#LeadRepoCareQueryEventHandlers"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
    CreateLeadRepoCareQueryEventHandler,
    UpdateLeadRepoCareQueryEventHandler,
]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadRepoCare.queryside/providers/query.provider.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LeadRepoCareQueryProviders"></a>
                        <span class="name">
                            <b>
                            LeadRepoCareQueryProviders</b>
                            <a href="#LeadRepoCareQueryProviders"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
  {
    provide: CommonConst.QUERY_MODEL_TOKEN,
    useFactory: (connection: Connection) &#x3D;&gt;
      connection.model(CommonConst.LEAD_REPO_CARE_COLLECTION, QuerySchema),
    inject: [CommonConst.QUERY_CONNECTION_TOKEN],
  },
]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadRepo.domain/commands/handlers/index.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LeadRepoDomainCommandHandlers"></a>
                        <span class="name">
                            <b>
                            LeadRepoDomainCommandHandlers</b>
                            <a href="#LeadRepoDomainCommandHandlers"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
    CreateLeadRepoCommandHandler,
    UpdateLeadRepoCommandHandler,
    UpdateConfigLeadRepoCommandHandler,
    ActiveConfigLeadRepoCommandHandler,
    BulkUpdateLeadRepoCommandHandler
]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadRepo.domain/providers/domain.provider.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LeadRepoDomainProvider"></a>
                        <span class="name">
                            <b>
                            LeadRepoDomainProvider</b>
                            <a href="#LeadRepoDomainProvider"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
  {
    provide: CommonConst.DOMAIN_MODEL_TOKEN,
    useFactory: (connection: Connection) &#x3D;&gt;
      connection.model(CommonConst.LEAD_REPO_EVENTS, DomainSchema),
    inject: [CommonConst.DOMAIN_CONNECTION_TOKEN],
  },
]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadRepo.queryside/command/handlers/index.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LeadRepoQueryCommandHandlers"></a>
                        <span class="name">
                            <b>
                            LeadRepoQueryCommandHandlers</b>
                            <a href="#LeadRepoQueryCommandHandlers"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
    CreateLeadRepoQueryCommandHandler,
    UpdateLeadRepoQueryCommandHandler,
    BulkUpdateLeadRepoQueryCommandHandler
]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadRepo.queryside/events/handlers/index.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LeadRepoQueryEventHandlers"></a>
                        <span class="name">
                            <b>
                            LeadRepoQueryEventHandlers</b>
                            <a href="#LeadRepoQueryEventHandlers"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
    CreateLeadRepoQueryEventHandler,
    UpdateLeadRepoQueryEventHandler,
    BulkUpdateLeadRepoQueryEventHandler
]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadRepo.queryside/providers/query.provider.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LeadRepoQueryProviders"></a>
                        <span class="name">
                            <b>
                            LeadRepoQueryProviders</b>
                            <a href="#LeadRepoQueryProviders"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
  {
    provide: CommonConst.QUERY_MODEL_TOKEN,
    useFactory: (connection: Connection) &#x3D;&gt;
      connection.model(CommonConst.LEAD_REPO_COLLECTION, QuerySchema),
    inject: [CommonConst.QUERY_CONNECTION_TOKEN],
  },
]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/lead.queryside/service.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="moment"></a>
                        <span class="name">
                            <b>
                            moment</b>
                            <a href="#moment"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&#x27;moment&#x27;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="momentTz"></a>
                        <span class="name">
                            <b>
                            momentTz</b>
                            <a href="#momentTz"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&#x27;moment-timezone&#x27;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="timezone"></a>
                        <span class="name">
                            <b>
                            timezone</b>
                            <a href="#timezone"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&#x27;moment-timezone&#x27;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="validListStatus"></a>
                        <span class="name">
                            <b>
                            validListStatus</b>
                            <a href="#validListStatus"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
    ExploitEnum.ASSIGN,
    ExploitEnum.REASSIGN,
    ExploitEnum.PROCESSING,
    ExploitEnum.DONE,
    ExploitEnum.CANCEL,
]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="validReportStatus"></a>
                        <span class="name">
                            <b>
                            validReportStatus</b>
                            <a href="#validReportStatus"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[ExploitEnum.DONE, ExploitEnum.CANCEL]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadCare.queryside/service.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="moment"></a>
                        <span class="name">
                            <b>
                            moment</b>
                            <a href="#moment"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&#x27;moment&#x27;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="timezone"></a>
                        <span class="name">
                            <b>
                            timezone</b>
                            <a href="#timezone"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&#x27;moment-timezone&#x27;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/lead-history/repository/query.repository.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="moment"></a>
                        <span class="name">
                            <b>
                            moment</b>
                            <a href="#moment"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&#x27;moment&#x27;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadCare-history/repository/query.repository.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="moment"></a>
                        <span class="name">
                            <b>
                            moment</b>
                            <a href="#moment"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&#x27;moment&#x27;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadSource/repository/query.repository.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="moment"></a>
                        <span class="name">
                            <b>
                            moment</b>
                            <a href="#moment"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&#x27;moment&#x27;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/shared/mapper/lead.mapper.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="moment"></a>
                        <span class="name">
                            <b>
                            moment</b>
                            <a href="#moment"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&#x27;moment-timezone&#x27;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="objectMapper"></a>
                        <span class="name">
                            <b>
                            objectMapper</b>
                            <a href="#objectMapper"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&#x27;object-mapper&#x27;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/shared/mapper/leadCare.mapper.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="moment"></a>
                        <span class="name">
                            <b>
                            moment</b>
                            <a href="#moment"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&#x27;moment-timezone&#x27;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="objectMapper"></a>
                        <span class="name">
                            <b>
                            objectMapper</b>
                            <a href="#objectMapper"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&#x27;object-mapper&#x27;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadJob/application/service.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="momentTz"></a>
                        <span class="name">
                            <b>
                            momentTz</b>
                            <a href="#momentTz"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&#x27;moment-timezone&#x27;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="uuid"></a>
                        <span class="name">
                            <b>
                            uuid</b>
                            <a href="#uuid"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&quot;uuid&quot;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/shared/utils/promiseWhile.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="promiseWhile"></a>
                        <span class="name">
                            <b>
                            promiseWhile</b>
                            <a href="#promiseWhile"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&lt;T&gt;(condition: ConditionFunc&lt;T&gt;, action: ActionFunc&lt;T&gt;, initialValue: T, checkWhenInit &#x3D; false): Promise&lt;T&gt; &#x3D;&gt;
  Bluebird.method&lt;T, ConditionFunc&lt;T&gt;, ActionFunc&lt;T&gt;, T&gt;(async function fn(
    _condition,
    _action,
    _initialValue
  ): Promise&lt;T&gt; {
    if (checkWhenInit &amp;&amp; !_condition(initialValue)) return initialValue;
    const value &#x3D; await _action(_initialValue);
    if (!_condition(value)) return value;
    return fn(_condition, _action, value);
  })(condition, action, initialValue)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/common/decorators/public.decorator.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="Public"></a>
                        <span class="name">
                            <b>
                            Public</b>
                            <a href="#Public"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>() &#x3D;&gt; SetMetadata(&#x27;isPublic&#x27;, true)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/c-lead.queryside/providers/query.cqrs.providers.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="QueryProviders"></a>
                        <span class="name">
                            <b>
                            QueryProviders</b>
                            <a href="#QueryProviders"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
  {
    provide: CommonConst.QUERY_MODEL_TOKEN,
    useFactory: (connection: Connection) &#x3D;&gt; connection.model(CommonConst.C_LEAD_COLLECTION, QuerySchema),
    inject: [CommonConst.QUERY_CONNECTION_TOKEN],
  }
]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/c-leadCare.queryside/providers/query.cqrs.providers.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="QueryProviders"></a>
                        <span class="name">
                            <b>
                            QueryProviders</b>
                            <a href="#QueryProviders"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
  {
    provide: CommonConst.QUERY_MODEL_TOKEN,
    useFactory: (connection: Connection) &#x3D;&gt; connection.model(CommonConst.C_LEAD_CARE_COLLECTION, QuerySchema),
    inject: [CommonConst.QUERY_CONNECTION_TOKEN],
  }
]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/code-generate/providers/query.cqrs.providers.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="QueryProviders"></a>
                        <span class="name">
                            <b>
                            QueryProviders</b>
                            <a href="#QueryProviders"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
  {
    provide: CommonConst.QUERY_MODEL_TOKEN,
    useFactory: (connection: Connection) &#x3D;&gt; connection.model(CommonConst.CODE_COLLECTION, QuerySchema),
    inject: [CommonConst.QUERY_CONNECTION_TOKEN],
  },
]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/history-import/providers/query.cqrs.providers.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="QueryProviders"></a>
                        <span class="name">
                            <b>
                            QueryProviders</b>
                            <a href="#QueryProviders"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
  {
    provide: CommonConst.QUERY_MODEL_TOKEN,
    useFactory: (connection: Connection) &#x3D;&gt; connection.model(CommonConst.HISTORY_IMPORT_COLLECTION, QuerySchema),
    inject: [CommonConst.QUERY_CONNECTION_TOKEN],
  },
]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/employee/providers/query.cqrs.providers.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="QueryProviders"></a>
                        <span class="name">
                            <b>
                            QueryProviders</b>
                            <a href="#QueryProviders"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
  {
    provide: CommonConst.QUERY_MODEL_TOKEN,
    useFactory: (connection: Connection) &#x3D;&gt; connection.model(CommonConst.EMPLOYEE_COLLECTION, QuerySchema),
    inject: [CommonConst.QUERY_CONNECTION_TOKEN],
  },
]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/lead-history/providers/query.cqrs.providers.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="QueryProviders"></a>
                        <span class="name">
                            <b>
                            QueryProviders</b>
                            <a href="#QueryProviders"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
  {
    provide: CommonConst.QUERY_MODEL_TOKEN,
    useFactory: (connection: Connection) &#x3D;&gt; connection.model(CommonConst.LEAD_HISTORY_COLLECTION, QuerySchema),
    inject: [CommonConst.QUERY_CONNECTION_TOKEN],
  },
]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/lead.queryside/providers/query.cqrs.providers.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="QueryProviders"></a>
                        <span class="name">
                            <b>
                            QueryProviders</b>
                            <a href="#QueryProviders"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
  {
    provide: CommonConst.QUERY_MODEL_TOKEN,
    useFactory: (connection: Connection) &#x3D;&gt; connection.model(CommonConst.LEAD_COLLECTION, QuerySchema),
    inject: [CommonConst.QUERY_CONNECTION_TOKEN],
  },
]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadCare-history/providers/query.cqrs.providers.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="QueryProviders"></a>
                        <span class="name">
                            <b>
                            QueryProviders</b>
                            <a href="#QueryProviders"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
  {
    provide: CommonConst.QUERY_MODEL_TOKEN,
    useFactory: (connection: Connection) &#x3D;&gt; connection.model(CommonConst.LEAD_CARE_HISTORY_COLLECTION, QuerySchema),
    inject: [CommonConst.QUERY_CONNECTION_TOKEN],
  },
]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadCare.queryside/providers/query.cqrs.providers.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="QueryProviders"></a>
                        <span class="name">
                            <b>
                            QueryProviders</b>
                            <a href="#QueryProviders"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
  {
    provide: CommonConst.QUERY_MODEL_TOKEN,
    useFactory: (connection: Connection) &#x3D;&gt; connection.model(CommonConst.LEAD_CARE_COLLECTION, QuerySchema),
    inject: [CommonConst.QUERY_CONNECTION_TOKEN],
  },
]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadSource/providers/query.cqrs.providers.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="QueryProviders"></a>
                        <span class="name">
                            <b>
                            QueryProviders</b>
                            <a href="#QueryProviders"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
  {
    provide: CommonConst.QUERY_MODEL_TOKEN,
    useFactory: (connection: Connection) &#x3D;&gt; connection.model(CommonConst.LEAD_SOURCE_COLLECTION, QuerySchema),
    inject: [CommonConst.QUERY_CONNECTION_TOKEN],
  },
]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/raw/providers/query.cqrs.providers.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="QueryProviders"></a>
                        <span class="name">
                            <b>
                            QueryProviders</b>
                            <a href="#QueryProviders"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[
  {
    provide: CommonConst.QUERY_MODEL_TOKEN,
    useFactory: (connection: Connection) &#x3D;&gt; connection.model(CommonConst.RAW_COLLECTION, QuerySchema),
    inject: [CommonConst.QUERY_CONNECTION_TOKEN],
  },
]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/c-lead.queryside/schemas/query.schema.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="QuerySchema"></a>
                        <span class="name">
                            <b>
                            QuerySchema</b>
                            <a href="#QuerySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    _id: { type: Object }
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/c-leadCare.queryside/schemas/query.schema.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="QuerySchema"></a>
                        <span class="name">
                            <b>
                            QuerySchema</b>
                            <a href="#QuerySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    _id: { type: Object }
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/code-generate/schemas/query.schema.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="QuerySchema"></a>
                        <span class="name">
                            <b>
                            QuerySchema</b>
                            <a href="#QuerySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    name: { type: String, index: true },
    index: { type: Number, default: 0 },
    prefix: { type: String, required: true},
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/history-import/schemas/query.schema.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="QuerySchema"></a>
                        <span class="name">
                            <b>
                            QuerySchema</b>
                            <a href="#QuerySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    _id: { type: String },
    id: { type: String, default: uuid.v4, index: true },
    fileName: { type: String },
    processBy: { type: Object },
    type: { type: String },
    createdDate: { type: Date },
    updatedDate: { type: Date },
    description: { type: Object },
    success: { type: Number },
    fail: { type: Number },
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/employee/schemas/query.schema.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="QuerySchema"></a>
                        <span class="name">
                            <b>
                            QuerySchema</b>
                            <a href="#QuerySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    _id: { type: String },
    id: { type: String, index: true },
    name: { type: String },
    email: { type: String },
    isPenalty: { type: Boolean, default: false },
    code: { type: String },
    pos: { type: Object },
    timePullLatest: { type: String, default: &#x27;&#x27; },
    staffIds: {type: Object},
    leadsPullLatest: {type: Object, default: []},
    active: { type: Boolean, default: true },
    level: { type: Number },
    workingAt: { type: String, default: null },
    managerAt: { type: String , default: null},
    phone: { type: String},
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/lead-history/schemas/query.schema.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="QuerySchema"></a>
                        <span class="name">
                            <b>
                            QuerySchema</b>
                            <a href="#QuerySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    _id: { type: String },
    id: { type: String, index: true },
    email: { type: String },
    name: { type: String },
    address: { type: String },
    phone: { type: String },
    pos: { type: Object },
    status: { type: String },
    lifeCycleStatus: { type: String },
    processBy: { type: String },
    type: { type: String },
    createdDate: { type: Date },
    updatedDate: { type: Date },
    description: { type: String },
    demandId: { type: String },
    reason: { type: String },
    notes: { type: Object },
    decisionDate: { type: Date, default: () &#x3D;&gt; Date.now() },
    processedDate: { type: Date},
    assignedDate: { type: Date},
    customerId: { type: String },
    code: { type: String },
    source: { type: String },
    surveyCode: { type: String }
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadCare-history/schemas/query.schema.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="QuerySchema"></a>
                        <span class="name">
                            <b>
                            QuerySchema</b>
                            <a href="#QuerySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    _id: { type: String },
    id: { type: String, index: true },
    email: { type: String },
    name: { type: String },
    address: { type: String },
    phone: { type: String },
    pos: { type: Object },
    status: { type: String },
    lifeCycleStatus: { type: String },
    processBy: { type: String },
    type: { type: String },
    createdDate: { type: Date },
    updatedDate: { type: Date },
    description: { type: String },
    demandId: { type: String },
    reason: { type: String },
    notes: { type: Object },
    decisionDate: { type: Date, default: () &#x3D;&gt; Date.now() },
    processedDate: { type: Date},
    assignedDate: { type: Date},
    customerId: { type: String },
    code: { type: String },
    source: { type: String },
    surveyCode: { type: String }
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadSource/schemas/query.schema.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="QuerySchema"></a>
                        <span class="name">
                            <b>
                            QuerySchema</b>
                            <a href="#QuerySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
  _id: { type: String },
  id: { type: String, default: uuid.v4, index: true },
  name: { type: String },
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/raw/schemas/query.schema.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="QuerySchema"></a>
                        <span class="name">
                            <b>
                            QuerySchema</b>
                            <a href="#QuerySchema"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new mongoose.Schema({
    _id: { type: String },
    id: { type: String, index: true },
    name: { type: String },
    email: { type: String },
    phone: { type: String},
    customerId: { type: String }
})</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/mgs-sender/mgs-sender.module.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="RABBITMQ_URL"></a>
                        <span class="name">
                            <b>
                            RABBITMQ_URL</b>
                            <a href="#RABBITMQ_URL"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>new ConfigService(&#x60;.env.${process.env.NODE_ENV}&#x60;).get(&#x27;RABBITMQ_URL&#x27;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/lead.domain/service.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="request"></a>
                        <span class="name">
                            <b>
                            request</b>
                            <a href="#request"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&#x27;request&#x27;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="timezone"></a>
                        <span class="name">
                            <b>
                            timezone</b>
                            <a href="#timezone"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&#x27;moment-timezone&#x27;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="uuid"></a>
                        <span class="name">
                            <b>
                            uuid</b>
                            <a href="#uuid"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&#x27;uuid&#x27;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="XLSX"></a>
                        <span class="name">
                            <b>
                            XLSX</b>
                            <a href="#XLSX"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&#x27;xlsx&#x27;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadCare.domain/service.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="request"></a>
                        <span class="name">
                            <b>
                            request</b>
                            <a href="#request"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&#x27;request&#x27;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="timezone"></a>
                        <span class="name">
                            <b>
                            timezone</b>
                            <a href="#timezone"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&#x27;moment-timezone&#x27;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="uuid"></a>
                        <span class="name">
                            <b>
                            uuid</b>
                            <a href="#uuid"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&#x27;uuid&#x27;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="XLSX"></a>
                        <span class="name">
                            <b>
                            XLSX</b>
                            <a href="#XLSX"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&#x27;xlsx&#x27;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/common/decorators/roles.decorator.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="Roles"></a>
                        <span class="name">
                            <b>
                            Roles</b>
                            <a href="#Roles"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>(...roles: string[]) &#x3D;&gt; ReflectMetadata(&#x27;roles&#x27;, roles)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/shared/utils/updateArrData.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="updateArrData"></a>
                        <span class="name">
                            <b>
                            updateArrData</b>
                            <a href="#updateArrData"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&lt;T&gt;(target: T, data: T[], index: number) &#x3D;&gt; {
    return [...data.slice(0, index), target, ...data.slice(index + 1)];
}</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/shared/services/user/decorator/user.decorator.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="Usr"></a>
                        <span class="name">
                            <b>
                            Usr</b>
                            <a href="#Usr"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>createParamDecorator((data, req) &#x3D;&gt; req.user)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadJob/application/service.auth.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="uuid"></a>
                        <span class="name">
                            <b>
                            uuid</b>
                            <a href="#uuid"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&quot;uuid&quot;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/lead.queryside/repository/query.repository.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="validReportStatus"></a>
                        <span class="name">
                            <b>
                            validReportStatus</b>
                            <a href="#validReportStatus"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>[]</code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>[ExploitEnum.DONE, ExploitEnum.CANCEL]</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/lead.domain/service.extend.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="XLSX"></a>
                        <span class="name">
                            <b>
                            XLSX</b>
                            <a href="#XLSX"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&#x27;xlsx&#x27;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>
    <h3>src/modules/leadCare.domain/service.extend.ts</h3>
    <section>
    <h3></h3>        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="XLSX"></a>
                        <span class="name">
                            <b>
                            XLSX</b>
                            <a href="#XLSX"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>require(&#x27;xlsx&#x27;)</code>
                        </td>
                    </tr>


            </tbody>
        </table>
</section>



                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'miscellaneous-variables';
            var COMPODOC_CURRENT_PAGE_URL = 'variables.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
