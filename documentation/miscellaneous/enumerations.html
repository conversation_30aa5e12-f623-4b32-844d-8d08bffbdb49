<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-lead documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-lead documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content miscellaneous-enumerations">
                   <div class="content-data">














                   

<ol class="breadcrumb">
  <li>Miscellaneous</li>
  <li>Enumerations</li>
</ol>

<section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <ul class="index-list">
                        <li>
                            <a href="#Action" title="src/modules/shared/enum/action.enum.ts"><b>Action</b>&nbsp;&nbsp;&nbsp;(src/.../action.enum.ts)</a>
                        </li>
                        <li>
                            <a href="#CacheEnum" title="src/modules/shared/enum/cache.enum.ts"><b>CacheEnum</b>&nbsp;&nbsp;&nbsp;(src/.../cache.enum.ts)</a>
                        </li>
                        <li>
                            <a href="#CareSourceEnum" title="src/modules/shared/enum/source.enum.ts"><b>CareSourceEnum</b>&nbsp;&nbsp;&nbsp;(src/.../source.enum.ts)</a>
                        </li>
                        <li>
                            <a href="#ConversationEnum" title="src/modules/shared/enum/exploit.enum.ts"><b>ConversationEnum</b>&nbsp;&nbsp;&nbsp;(src/.../exploit.enum.ts)</a>
                        </li>
                        <li>
                            <a href="#ExploitCareEnum" title="src/modules/shared/enum/exploit.enum.ts"><b>ExploitCareEnum</b>&nbsp;&nbsp;&nbsp;(src/.../exploit.enum.ts)</a>
                        </li>
                        <li>
                            <a href="#ExploitEnum" title="src/modules/shared/enum/exploit.enum.ts"><b>ExploitEnum</b>&nbsp;&nbsp;&nbsp;(src/.../exploit.enum.ts)</a>
                        </li>
                        <li>
                            <a href="#LeadRepoCareEnum" title="src/modules/shared/enum/type.enum.ts"><b>LeadRepoCareEnum</b>&nbsp;&nbsp;&nbsp;(src/.../type.enum.ts)</a>
                        </li>
                        <li>
                            <a href="#LifeCycleStatusEnum" title="src/modules/shared/enum/life-cycle-status.enum.ts"><b>LifeCycleStatusEnum</b>&nbsp;&nbsp;&nbsp;(src/.../life-cycle-status.enum.ts)</a>
                        </li>
                        <li>
                            <a href="#MaritalStatusEnum" title="src/modules/shared/enum/marital-status.enum.ts"><b>MaritalStatusEnum</b>&nbsp;&nbsp;&nbsp;(src/.../marital-status.enum.ts)</a>
                        </li>
                        <li>
                            <a href="#PermissionEnum" title="src/modules/shared/enum/permission.enum.ts"><b>PermissionEnum</b>&nbsp;&nbsp;&nbsp;(src/.../permission.enum.ts)</a>
                        </li>
                        <li>
                            <a href="#SexEnum" title="src/modules/shared/enum/sex.enum.ts"><b>SexEnum</b>&nbsp;&nbsp;&nbsp;(src/.../sex.enum.ts)</a>
                        </li>
                        <li>
                            <a href="#SourceEnum" title="src/modules/shared/enum/source.enum.ts"><b>SourceEnum</b>&nbsp;&nbsp;&nbsp;(src/.../source.enum.ts)</a>
                        </li>
                        <li>
                            <a href="#StageEnum" title="src/modules/shared/enum/statge.enum.ts"><b>StageEnum</b>&nbsp;&nbsp;&nbsp;(src/.../statge.enum.ts)</a>
                        </li>
                        <li>
                            <a href="#StatusEnum" title="src/modules/shared/enum/status.enum.ts"><b>StatusEnum</b>&nbsp;&nbsp;&nbsp;(src/.../status.enum.ts)</a>
                        </li>
                        <li>
                            <a href="#TransactionTypeEnum" title="src/modules/shared/enum/transaction-type.enum.ts"><b>TransactionTypeEnum</b>&nbsp;&nbsp;&nbsp;(src/.../transaction-type.enum.ts)</a>
                        </li>
                        <li>
                            <a href="#VerifyAccountStatusEnum" title="src/modules/shared/enum/care-customer.enum.ts"><b>VerifyAccountStatusEnum</b>&nbsp;&nbsp;&nbsp;(src/.../care-customer.enum.ts)</a>
                        </li>
                    </ul>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    <h3>src/modules/shared/enum/action.enum.ts</h3>
    <section>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="Action"></a>
                        <span class="name"><b>Action</b><a href="#Action"><span class="icon ion-ios-link"></span></a></span>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;CREATE
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>create</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;UPDATE
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>update</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;DELETE
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>delete</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;NOTIFY
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>notify</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;COMPLETE
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>complete</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;FAIL
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>fail</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;PROCESS
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>process</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;UNPROCESS
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>unprocess</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;ASSIGN
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>assign</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;REASSIGN
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>reassign</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;PENDING
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>pending</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;EXPIRED
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>expired</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;CHANGE_STATUS
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>change-status</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;UPDATE_MAIN
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>updateMain</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;UPDATE_CONFIG
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>updateConfig</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;ACTIVE_CONFIG
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>activeConfig</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;IMPORT_LEAD
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>importLead</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;RENEW_LEAD
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>renewLead</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;IMPORT_LEAD_CARE
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>importLeadCare</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;BULK_UPDATE
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>bulkUpdate</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;CLOSED_TICKET
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>closedTicket</code>
                            </td>
                        </tr>
            </tbody>
        </table>
</section>
    <h3>src/modules/shared/enum/cache.enum.ts</h3>
    <section>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="CacheEnum"></a>
                        <span class="name"><b>CacheEnum</b><a href="#CacheEnum"><span class="icon ion-ios-link"></span></a></span>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;ALL_DEMAND
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>demand.all</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;ALL_ORGCHART
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>orgchart.all</code>
                            </td>
                        </tr>
            </tbody>
        </table>
</section>
    <h3>src/modules/shared/enum/source.enum.ts</h3>
    <section>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="CareSourceEnum"></a>
                        <span class="name"><b>CareSourceEnum</b><a href="#CareSourceEnum"><span class="icon ion-ios-link"></span></a></span>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;Care
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>Care</code>
                            </td>
                        </tr>
            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="SourceEnum"></a>
                        <span class="name"><b>SourceEnum</b><a href="#SourceEnum"><span class="icon ion-ios-link"></span></a></span>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;YeuCau
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>BrokerYeuCau</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;DangTin
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>BrokerDangTin</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;MKT
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>MKT</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;PKT
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>PKT</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;KTN
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>KTN</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;SHARE_HOLDER
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>SHARE_HOLDER</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;OLD_CUS
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>OLD_CUS</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;MKT_DXG
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>MKT_DXG</code>
                            </td>
                        </tr>
            </tbody>
        </table>
</section>
    <h3>src/modules/shared/enum/exploit.enum.ts</h3>
    <section>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="ConversationEnum"></a>
                        <span class="name"><b>ConversationEnum</b><a href="#ConversationEnum"><span class="icon ion-ios-link"></span></a></span>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;QUESTION
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>QUESTION</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;ANSWER
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>ANSWER</code>
                            </td>
                        </tr>
            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="ExploitCareEnum"></a>
                        <span class="name"><b>ExploitCareEnum</b><a href="#ExploitCareEnum"><span class="icon ion-ios-link"></span></a></span>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;NEW
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>new</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;ASSIGN
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>assign</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;RENEW
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>renew</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;REASSIGN
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>reassign</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;PROCESSING
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>processing</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;SURVEY
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>survey</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;CANCEL
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>cancel</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;CUSTOMER_CLOSED_TICKET
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>customer_closed_ticket</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;SYSTEM_CLOSED_TICKET
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>system_closed_ticket</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;DONE
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>done</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;ADD_QUESTION
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>add_question</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;ADD_ANSWER
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>add_answer</code>
                            </td>
                        </tr>
            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="ExploitEnum"></a>
                        <span class="name"><b>ExploitEnum</b><a href="#ExploitEnum"><span class="icon ion-ios-link"></span></a></span>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;NEW
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>new</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;MANUAL_DELIVER
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>manual_deliver</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;ASSIGN
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>assign</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;RENEW
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>renew</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;REASSIGN
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>reassign</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;PROCESSING
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>processing</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;CANCEL
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>cancel</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;DONE
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>done</code>
                            </td>
                        </tr>
            </tbody>
        </table>
</section>
    <h3>src/modules/shared/enum/type.enum.ts</h3>
    <section>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LeadRepoCareEnum"></a>
                        <span class="name"><b>LeadRepoCareEnum</b><a href="#LeadRepoCareEnum"><span class="icon ion-ios-link"></span></a></span>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;PROJECT
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>project</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;TRANSFER
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>transfer</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;SERVICE
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>service</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;RENT
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>rent</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;OTHER
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>other</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;BQL
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>bql</code>
                            </td>
                        </tr>
            </tbody>
        </table>
</section>
    <h3>src/modules/shared/enum/life-cycle-status.enum.ts</h3>
    <section>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="LifeCycleStatusEnum"></a>
                        <span class="name"><b>LifeCycleStatusEnum</b><a href="#LifeCycleStatusEnum"><span class="icon ion-ios-link"></span></a></span>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;IN_POOL
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>inpool</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;ASSIGNED
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>assigned</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;PROCESSING
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>processing</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;PRIMARY_PROCESSING
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>primary_processing</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;COMPLETED
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>completed</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;REMOVED
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>removed</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;PENDING
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>pending</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;CUSTOMER_CLOSED_TICKET
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>customer_closed_ticket</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;SYSTEM_CLOSED_TICKET
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>system_closed_ticket</code>
                            </td>
                        </tr>
            </tbody>
        </table>
</section>
    <h3>src/modules/shared/enum/marital-status.enum.ts</h3>
    <section>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="MaritalStatusEnum"></a>
                        <span class="name"><b>MaritalStatusEnum</b><a href="#MaritalStatusEnum"><span class="icon ion-ios-link"></span></a></span>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;ALONE
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>alone</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;MARRIED
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>married</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;OTHER
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>other</code>
                            </td>
                        </tr>
            </tbody>
        </table>
</section>
    <h3>src/modules/shared/enum/permission.enum.ts</h3>
    <section>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="PermissionEnum"></a>
                        <span class="name"><b>PermissionEnum</b><a href="#PermissionEnum"><span class="icon ion-ios-link"></span></a></span>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;LEADJOB_CREATE
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>leadjob.create</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;LEADJOB_UPDATE
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>leadjob.update</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;LEADJOB_DELETE
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>leadjob.delete</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;LEADJOB_GET_ALL
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>leadjob.get.all</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;LEADJOB_GET_ID
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>leadjob.get.id</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;LEADJOB_GET_BY_ADMIN
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>leadjob.get.by.admin</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;LEADJOB_GET_BY_POS
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>leadjob.get.by.pos</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;LEADJOB_GET
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>leadjob.get</code>
                            </td>
                        </tr>
            </tbody>
        </table>
</section>
    <h3>src/modules/shared/enum/sex.enum.ts</h3>
    <section>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="SexEnum"></a>
                        <span class="name"><b>SexEnum</b><a href="#SexEnum"><span class="icon ion-ios-link"></span></a></span>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;MALE
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>male</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;FEMALE
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>female</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;OTHER
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>other</code>
                            </td>
                        </tr>
            </tbody>
        </table>
</section>
    <h3>src/modules/shared/enum/statge.enum.ts</h3>
    <section>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="StageEnum"></a>
                        <span class="name"><b>StageEnum</b><a href="#StageEnum"><span class="icon ion-ios-link"></span></a></span>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;NEW
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>NEW</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;EDIT
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>EDIT</code>
                            </td>
                        </tr>
            </tbody>
        </table>
</section>
    <h3>src/modules/shared/enum/status.enum.ts</h3>
    <section>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="StatusEnum"></a>
                        <span class="name"><b>StatusEnum</b><a href="#StatusEnum"><span class="icon ion-ios-link"></span></a></span>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;GREEN
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>green</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;YELLOW
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>darkorange</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;RED
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>red</code>
                            </td>
                        </tr>
            </tbody>
        </table>
</section>
    <h3>src/modules/shared/enum/transaction-type.enum.ts</h3>
    <section>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="TransactionTypeEnum"></a>
                        <span class="name"><b>TransactionTypeEnum</b><a href="#TransactionTypeEnum"><span class="icon ion-ios-link"></span></a></span>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;sell
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>SELL</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;lease
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>LEASE</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;buy
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>BUY</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;rent
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>RENT</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;other
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>OTHER</code>
                            </td>
                        </tr>
            </tbody>
        </table>
</section>
    <h3>src/modules/shared/enum/care-customer.enum.ts</h3>
    <section>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="VerifyAccountStatusEnum"></a>
                        <span class="name"><b>VerifyAccountStatusEnum</b><a href="#VerifyAccountStatusEnum"><span class="icon ion-ios-link"></span></a></span>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;INIT
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>init</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;WAITING
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>waiting</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;APPROVED
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>approved</code>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                &nbsp;REJECTED
                            </td>
                        </tr>
                        <tr>
                            <td class="col-md-4">
                                <i>Value : </i><code>rejected</code>
                            </td>
                        </tr>
            </tbody>
        </table>
</section>



                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'miscellaneous-enumerations';
            var COMPODOC_CURRENT_PAGE_URL = 'enumerations.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
