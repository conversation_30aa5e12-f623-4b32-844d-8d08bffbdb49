<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-lead documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-lead documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content miscellaneous-typealiases">
                   <div class="content-data">














                   

<ol class="breadcrumb">
  <li>Miscellaneous</li>
  <li>Type aliases</li>
</ol>

<section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <ul class="index-list">
                        <li>
                            <a href="#ActionFunc" title="src/modules/shared/utils/promiseWhile.ts"><b>ActionFunc</b>&nbsp;&nbsp;&nbsp;(src/.../promiseWhile.ts)</a>
                        </li>
                        <li>
                            <a href="#ConditionFunc" title="src/modules/shared/utils/promiseWhile.ts"><b>ConditionFunc</b>&nbsp;&nbsp;&nbsp;(src/.../promiseWhile.ts)</a>
                        </li>
                        <li>
                            <a href="#Maybe" title="src/modules/shared/interfaces/baseFilter.interface.ts"><b>Maybe</b>&nbsp;&nbsp;&nbsp;(src/.../baseFilter.interface.ts)</a>
                        </li>
                        <li>
                            <a href="#ModelFilter" title="src/modules/shared/interfaces/baseFilter.interface.ts"><b>ModelFilter</b>&nbsp;&nbsp;&nbsp;(src/.../baseFilter.interface.ts)</a>
                        </li>
                        <li>
                            <a href="#Orperator" title="src/modules/shared/interfaces/baseFilter.interface.ts"><b>Orperator</b>&nbsp;&nbsp;&nbsp;(src/.../baseFilter.interface.ts)</a>
                        </li>
                        <li>
                            <a href="#PrimitiveValueType" title="src/modules/shared/interfaces/baseFilter.interface.ts"><b>PrimitiveValueType</b>&nbsp;&nbsp;&nbsp;(src/.../baseFilter.interface.ts)</a>
                        </li>
                        <li>
                            <a href="#ProjectionModel" title="src/modules/shared/interfaces/baseFilter.interface.ts"><b>ProjectionModel</b>&nbsp;&nbsp;&nbsp;(src/.../baseFilter.interface.ts)</a>
                        </li>
                        <li>
                            <a href="#ProjectionValue" title="src/modules/shared/interfaces/baseFilter.interface.ts"><b>ProjectionValue</b>&nbsp;&nbsp;&nbsp;(src/.../baseFilter.interface.ts)</a>
                        </li>
                        <li>
                            <a href="#RangeValueType" title="src/modules/shared/interfaces/baseFilter.interface.ts"><b>RangeValueType</b>&nbsp;&nbsp;&nbsp;(src/.../baseFilter.interface.ts)</a>
                        </li>
                        <li>
                            <a href="#SortFilter" title="src/modules/shared/interfaces/baseFilter.interface.ts"><b>SortFilter</b>&nbsp;&nbsp;&nbsp;(src/.../baseFilter.interface.ts)</a>
                        </li>
                        <li>
                            <a href="#SortValueType" title="src/modules/shared/interfaces/baseFilter.interface.ts"><b>SortValueType</b>&nbsp;&nbsp;&nbsp;(src/.../baseFilter.interface.ts)</a>
                        </li>
                        <li>
                            <a href="#WhereFilter" title="src/modules/shared/interfaces/baseFilter.interface.ts"><b>WhereFilter</b>&nbsp;&nbsp;&nbsp;(src/.../baseFilter.interface.ts)</a>
                        </li>
                    </ul>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    <h3>src/modules/shared/utils/promiseWhile.ts</h3>
    <section>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ActionFunc"></a>
                    <span class="name"><b>ActionFunc</b><a href="#ActionFunc"><span class="icon ion-ios-link"></span></a></span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <code>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/function" target="_blank" >function</a></code>
</code>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ConditionFunc"></a>
                    <span class="name"><b>ConditionFunc</b><a href="#ConditionFunc"><span class="icon ion-ios-link"></span></a></span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <code>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/function" target="_blank" >function</a></code>
</code>
                </td>
            </tr>
        </tbody>
    </table>
</section>    <h3>src/modules/shared/interfaces/baseFilter.interface.ts</h3>
    <section>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="Maybe"></a>
                    <span class="name"><b>Maybe</b><a href="#Maybe"><span class="icon ion-ios-link"></span></a></span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <code>    <code>T | null</code>
</code>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ModelFilter"></a>
                    <span class="name"><b>ModelFilter</b><a href="#ModelFilter"><span class="icon ion-ios-link"></span></a></span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <code>    <code></code>
</code>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="Orperator"></a>
                    <span class="name"><b>Orperator</b><a href="#Orperator"><span class="icon ion-ios-link"></span></a></span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <code>    <code>&quot;$in&quot; | &quot;$nin&quot; | &quot;$gt&quot; | &quot;$gte&quot; | &quot;$lt&quot; | &quot;$lte&quot;</code>
</code>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="PrimitiveValueType"></a>
                    <span class="name"><b>PrimitiveValueType</b><a href="#PrimitiveValueType"><span class="icon ion-ios-link"></span></a></span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <code>    <code>string | number</code>
</code>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ProjectionModel"></a>
                    <span class="name"><b>ProjectionModel</b><a href="#ProjectionModel"><span class="icon ion-ios-link"></span></a></span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <code>    <code></code>
</code>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ProjectionValue"></a>
                    <span class="name"><b>ProjectionValue</b><a href="#ProjectionValue"><span class="icon ion-ios-link"></span></a></span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <code>    <code></code>
</code>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="RangeValueType"></a>
                    <span class="name"><b>RangeValueType</b><a href="#RangeValueType"><span class="icon ion-ios-link"></span></a></span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <code>    <code>number | Date</code>
</code>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="SortFilter"></a>
                    <span class="name"><b>SortFilter</b><a href="#SortFilter"><span class="icon ion-ios-link"></span></a></span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <code>    <code></code>
</code>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="SortValueType"></a>
                    <span class="name"><b>SortValueType</b><a href="#SortValueType"><span class="icon ion-ios-link"></span></a></span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <code>    <code>&quot;1&quot; | &quot;undefined&quot;</code>
</code>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="WhereFilter"></a>
                    <span class="name"><b>WhereFilter</b><a href="#WhereFilter"><span class="icon ion-ios-link"></span></a></span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <code>    <code></code>
</code>
                </td>
            </tr>
        </tbody>
    </table>
</section>


                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'miscellaneous-typealiases';
            var COMPODOC_CURRENT_PAGE_URL = 'typealiases.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
