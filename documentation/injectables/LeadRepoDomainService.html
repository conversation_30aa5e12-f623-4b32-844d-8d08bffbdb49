<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-lead documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-lead documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">







<ol class="breadcrumb">
  <li>Injectables</li>
  <li>LeadRepoDomainService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/leadRepo.domain/service.ts</code>
        </p>




            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Readonly</span>
                                <a href="#configPrefix">configPrefix</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Readonly</span>
                                <a href="#context">context</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Readonly</span>
                                <a href="#leadRepoPrefix">leadRepoPrefix</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Readonly</span>
                                <a href="#successResponse">successResponse</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#activeLeadRepoConfig">activeLeadRepoConfig</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#bulkUpdateLeadConfig">bulkUpdateLeadConfig</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createLeadRepo">createLeadRepo</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#executeCommand">executeCommand</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#getCode">getCode</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#updateConfigData">updateConfigData</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateLeadRepoConfig">updateLeadRepoConfig</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateLeadRepoConfigs">updateLeadRepoConfigs</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateLeadRepoMain">updateLeadRepoMain</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#validateDuration">validateDuration</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateExploitTime">validateExploitTime</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateOrgChart">validateOrgChart</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateProject">validateProject</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section>
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(commandBus: CommandBus, propertyClient: <a href="../injectables/PropertyClient.html">PropertyClient</a>, loggerService: <a href="../injectables/MsxLoggerService.html">MsxLoggerService</a>, queryRepo: <a href="../injectables/LeadRepoQueryRepository.html">LeadRepoQueryRepository</a>, orgchartClient: <a href="../injectables/OrgchartClient.html">OrgchartClient</a>, codeSrv: <a href="../injectables/CodeGenerateService.html">CodeGenerateService</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="39" class="link-to-prism">src/modules/leadRepo.domain/service.ts:39</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>commandBus</td>
                                                  
                                                        <td>
                                                                    <code>CommandBus</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>propertyClient</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/PropertyClient.html" target="_self" >PropertyClient</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>loggerService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/MsxLoggerService.html" target="_self" >MsxLoggerService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>queryRepo</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/LeadRepoQueryRepository.html" target="_self" >LeadRepoQueryRepository</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>orgchartClient</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/OrgchartClient.html" target="_self" >OrgchartClient</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>codeSrv</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/CodeGenerateService.html" target="_self" >CodeGenerateService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section>
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="activeLeadRepoConfig"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            activeLeadRepoConfig
                        </b>
                        <a href="#activeLeadRepoConfig"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>activeLeadRepoConfig(undefined: <a href="../classes/ActiveLeadRepoConfigDto.html">ActiveLeadRepoConfigDto</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, timezoneClient: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="272"
                            class="link-to-prism">src/modules/leadRepo.domain/service.ts:272</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                                <code><a href="../classes/ActiveLeadRepoConfigDto.html" target="_self" >ActiveLeadRepoConfigDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>userId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>timezoneClient</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="bulkUpdateLeadConfig"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            bulkUpdateLeadConfig
                        </b>
                        <a href="#bulkUpdateLeadConfig"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>bulkUpdateLeadConfig(repos: <a href="../classes/LeadRepo.html">LeadRepo[]</a>, actionName?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="320"
                            class="link-to-prism">src/modules/leadRepo.domain/service.ts:320</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>repos</td>
                                    <td>
                                                <code><a href="../classes/LeadRepo.html" target="_self" >LeadRepo[]</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createLeadRepo"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            createLeadRepo
                        </b>
                        <a href="#createLeadRepo"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createLeadRepo(record: <a href="../classes/CreateLeadRepoDto.html">CreateLeadRepoDto</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, timezoneClient: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="55"
                            class="link-to-prism">src/modules/leadRepo.domain/service.ts:55</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>record</td>
                                    <td>
                                                <code><a href="../classes/CreateLeadRepoDto.html" target="_self" >CreateLeadRepoDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>userId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>timezoneClient</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="executeCommand"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            executeCommand
                        </b>
                        <a href="#executeCommand"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>executeCommand(action: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, commandId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, item: <a href="../classes/LeadRepo.html">LeadRepo | LeadRepo[]</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="397"
                            class="link-to-prism">src/modules/leadRepo.domain/service.ts:397</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>action</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>commandId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>item</td>
                                    <td>
                                                <code><a href="../classes/LeadRepo.html" target="_self" >LeadRepo | LeadRepo[]</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getCode"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            getCode
                        </b>
                        <a href="#getCode"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getCode(config)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="49"
                            class="link-to-prism">src/modules/leadRepo.domain/service.ts:49</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>config</td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>false</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateConfigData"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            updateConfigData
                        </b>
                        <a href="#updateConfigData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateConfigData(src: LeadRepoConfig[], code: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, record: <a href="../classes/LeadRepoConfig.html">LeadRepoConfig</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="429"
                            class="link-to-prism">src/modules/leadRepo.domain/service.ts:429</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>src</td>
                                    <td>
                                            <code>LeadRepoConfig[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>code</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>record</td>
                                    <td>
                                                <code><a href="../classes/LeadRepoConfig.html" target="_self" >LeadRepoConfig</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateLeadRepoConfig"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateLeadRepoConfig
                        </b>
                        <a href="#updateLeadRepoConfig"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateLeadRepoConfig(undefined: <a href="../classes/UpdateLeadRepoConfigDto.html">UpdateLeadRepoConfigDto</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, timezoneClient: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="159"
                            class="link-to-prism">src/modules/leadRepo.domain/service.ts:159</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                                <code><a href="../classes/UpdateLeadRepoConfigDto.html" target="_self" >UpdateLeadRepoConfigDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>userId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>timezoneClient</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateLeadRepoConfigs"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateLeadRepoConfigs
                        </b>
                        <a href="#updateLeadRepoConfigs"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateLeadRepoConfigs(id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, configs: LeadRepoConfig[], actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, timezoneClient: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="213"
                            class="link-to-prism">src/modules/leadRepo.domain/service.ts:213</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>configs</td>
                                    <td>
                                            <code>LeadRepoConfig[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>userId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>timezoneClient</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateLeadRepoMain"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateLeadRepoMain
                        </b>
                        <a href="#updateLeadRepoMain"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateLeadRepoMain(undefined: <a href="../classes/UpdateLeadRepoMainDto.html">UpdateLeadRepoMainDto</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, timezoneClient: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, skipValidate)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="114"
                            class="link-to-prism">src/modules/leadRepo.domain/service.ts:114</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                                <code><a href="../classes/UpdateLeadRepoMainDto.html" target="_self" >UpdateLeadRepoMainDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>userId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>timezoneClient</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>skipValidate</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>false</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateDuration"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            validateDuration
                        </b>
                        <a href="#validateDuration"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateDuration(duration: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>, hotConfig)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="382"
                            class="link-to-prism">src/modules/leadRepo.domain/service.ts:382</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>duration</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>hotConfig</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>false</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateExploitTime"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            validateExploitTime
                        </b>
                        <a href="#validateExploitTime"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateExploitTime(exploitTime: <a href="../classes/DateRange.html">DateRange</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="363"
                            class="link-to-prism">src/modules/leadRepo.domain/service.ts:363</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>exploitTime</td>
                                    <td>
                                                <code><a href="../classes/DateRange.html" target="_self" >DateRange</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateOrgChart"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            validateOrgChart
                        </b>
                        <a href="#validateOrgChart"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateOrgChart(orgChartIds: string[])</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="328"
                            class="link-to-prism">src/modules/leadRepo.domain/service.ts:328</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>orgChartIds</td>
                                    <td>
                                            <code>string[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateProject"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            validateProject
                        </b>
                        <a href="#validateProject"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateProject(projectId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="350"
                            class="link-to-prism">src/modules/leadRepo.domain/service.ts:350</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>projectId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>
            <section>
    
        <h3 id="inputs">
            Properties
        </h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="configPrefix"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Private</span>
                                <span class="modifier">Readonly</span>
                            configPrefix</b>
                            <a href="#configPrefix"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;CH&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="39" class="link-to-prism">src/modules/leadRepo.domain/service.ts:39</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="context"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Private</span>
                                <span class="modifier">Readonly</span>
                            context</b>
                            <a href="#context"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>LeadRepoDomainService.name</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="36" class="link-to-prism">src/modules/leadRepo.domain/service.ts:36</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="leadRepoPrefix"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Private</span>
                                <span class="modifier">Readonly</span>
                            leadRepoPrefix</b>
                            <a href="#leadRepoPrefix"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;KTL&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="38" class="link-to-prism">src/modules/leadRepo.domain/service.ts:38</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="successResponse"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Private</span>
                                <span class="modifier">Readonly</span>
                            successResponse</b>
                            <a href="#successResponse"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{ success: true }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="37" class="link-to-prism">src/modules/leadRepo.domain/service.ts:37</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { BadRequestException, Injectable } from &#x27;@nestjs/common&#x27;;
import { CommandBus } from &#x27;@nestjs/cqrs&#x27;;
import { PropertyClient } from &#x27;../mgs-sender/property.client&#x27;;
import {
    LeadRepo,
    DateRange,
    LeadRepoConfig,
} from &#x27;../shared/models/leadRepo/model&#x27;;
import * as Bluebird from &#x27;bluebird&#x27;;
import { CmdPatternConst } from &#x27;../shared/constant/cmd-pattern.const&#x27;;
import { ErrorConst } from &#x27;../shared/constant/error.const&#x27;;
import uuid &#x3D; require(&#x27;uuid&#x27;);
import { Action } from &#x27;../shared/enum/action.enum&#x27;;
import { CreateLeadRepoCommand } from &#x27;./commands/impl/create.cmd&#x27;;
import { MsxLoggerService } from &#x27;../logger/logger.service&#x27;;
import { LeadRepoQueryRepository } from &#x27;../leadRepo.queryside/repositories/query.repository&#x27;;
import { UpdateLeadRepoCommand } from &#x27;./commands/impl/update.cmd&#x27;;
import { OrgchartClient } from &#x27;../mgs-sender/orgchart.client&#x27;;
import { CodeGenerateService } from &#x27;../code-generate/service&#x27;;
import { cloneDeep, isEmpty } from &#x27;lodash&#x27;;
import {
    ActiveLeadRepoConfigDto,
    CreateLeadRepoDto,
    UpdateLeadRepoConfigDto,
    UpdateLeadRepoMainDto,
} from &#x27;./dto/domain.dto&#x27;;
import { LeadRepoCommandModel } from &#x27;../shared/models/leadRepo/command.model&#x27;;
import { LeadRepoDomainModel } from &#x27;./models/domain.model&#x27;;
import { updateArrData } from &#x27;../shared/utils/updateArrData&#x27;;
import { UpdateConfigLeadRepoCommand } from &#x27;./commands/impl/updateConfig&#x27;;
import { ActiveConfigLeadRepoCommand } from &#x27;./commands/impl/activeConfig&#x27;;
import { BulkUpdateLeadRepoCommand } from &#x27;./commands/impl/bulkUpdate.cmd&#x27;;

@Injectable()
export class LeadRepoDomainService {
    private readonly context &#x3D; LeadRepoDomainService.name;
    private readonly successResponse &#x3D; { success: true };
    private readonly leadRepoPrefix &#x3D; &#x27;KTL&#x27;;
    private readonly configPrefix &#x3D; &#x27;CH&#x27;;
    constructor(
        private readonly commandBus: CommandBus,
        private readonly propertyClient: PropertyClient,
        private readonly loggerService: MsxLoggerService,
        private readonly queryRepo: LeadRepoQueryRepository,
        private readonly orgchartClient: OrgchartClient,
        protected readonly codeSrv: CodeGenerateService
    ) {}

    private async getCode(config &#x3D; false) {
        const prefix &#x3D; config ? this.configPrefix : this.leadRepoPrefix;
        const code &#x3D; await this.codeSrv.generateCode(prefix);
        return code;
    }

    async createLeadRepo(
        record: CreateLeadRepoDto,
        actionName: string,
        userId: string,
        timezoneClient: string
    ) {
        this.loggerService.log(this.context, &#x27;createLeadRepo&#x27;);
        const { configs, configHot } &#x3D; record;

        if (!configs?.length || !configHot) {
            throw new BadRequestException({
                errors: ErrorConst.CommonError(
                    ErrorConst.INVALID_INPUT,
                    &#x27;configs | hotConfig&#x27;
                ),
            });
        }

        const hotOrgCharts &#x3D; await this.validateOrgChart(configHot.orgChartIds);
        this.validateDuration(configHot.assignDuration, true);

        Object.assign(configHot, { orgCharts: hotOrgCharts });

        const validConfigs &#x3D; [];
        await Bluebird.mapSeries(configs, async (config) &#x3D;&gt; {
            await this.validateExploitTime(config.exploitTime);
            const configOrgCharts &#x3D; await this.validateOrgChart(config.orgChartIds);
            await this.validateProject(config.projectId);
            this.validateDuration(config.assignDuration);
            const configCode &#x3D; await this.getCode(true);
            Object.assign(config, {
              code: configCode,
              orgCharts: configOrgCharts,
            });
            validConfigs.push(config);
        });

        const commandId &#x3D; uuid.v4();

        const leadRepoCode &#x3D; await this.getCode();

        Object.assign(record, {
            code: leadRepoCode,
            configs: validConfigs,
            configHot,
            modifiedBy: userId,
            timezoneClient,
        });

        await this.executeCommand(
            Action.CREATE,
            actionName,
            commandId,
            record as LeadRepoCommandModel
        );

        return this.successResponse;
    }

    async updateLeadRepoMain(
        { id, configHot, name }: UpdateLeadRepoMainDto,
        actionName: string,
        userId: string,
        timezoneClient: string,
        skipValidate &#x3D; false
    ) {
        this.loggerService.log(this.context, &#x27;updateLeadRepoMain&#x27;);
        const leadRepo &#x3D; await this.queryRepo.findById(id, true);
        if (!leadRepo) {
            throw new BadRequestException({
                errors: ErrorConst.CommonError(
                    ErrorConst.NOT_FOUND,
                    &#x27;leadRepo&#x27;
                ),
            });
        }

        if (!skipValidate) {
          const hotOrgCharts &#x3D; await this.validateOrgChart(configHot.orgChartIds);
          this.validateDuration(configHot.assignDuration, true);
          Object.assign(configHot, { orgCharts: hotOrgCharts });
        }

        const commandId &#x3D; uuid.v4();

        Object.assign(leadRepo, {
            configHot,
            name,
            updatedDate: Date.now(),
            modifiedBy: userId || leadRepo.modifiedBy,
            timezoneClient: timezoneClient || leadRepo.timezoneClient,
        });


        await this.executeCommand(
            Action.UPDATE_MAIN,
            actionName,
            commandId,
            leadRepo as LeadRepoCommandModel
        );

        return this.successResponse;
    }

    async updateLeadRepoConfig(
        { id, config }: UpdateLeadRepoConfigDto,
        actionName: string,
        userId: string,
        timezoneClient: string
    ) {
        this.loggerService.log(this.context, &#x27;updateLeadRepoConfig&#x27;);
        const leadRepo &#x3D; await this.queryRepo.findById(id, true);
        if (!leadRepo) {
            throw new BadRequestException({
                errors: ErrorConst.CommonError(
                    ErrorConst.NOT_FOUND,
                    &#x27;leadRepo&#x27;
                ),
            });
        }

        let { configs } &#x3D; leadRepo;
        await this.validateExploitTime(config.exploitTime);
        const configOrgCharts &#x3D; await this.validateOrgChart(config.orgChartIds);
        await this.validateProject(config.projectId);
        this.validateDuration(config.assignDuration);
        Object.assign(config, {orgCharts: configOrgCharts});

        const existed &#x3D; configs.find(i &#x3D;&gt; i.code &#x3D;&#x3D;&#x3D; config.code);
        if (!existed) {
          const newCode &#x3D; await this.getCode(true);
          Object.assign(config, { code: newCode });
          configs &#x3D; this.updateConfigData(configs, null, config);
        } else {
            Object.assign(existed, config);
            configs &#x3D; this.updateConfigData(configs, existed.code, existed);
        }
        

        Object.assign(leadRepo, {
            configs,
            updatedDate: Date.now(),
            modifiedBy: userId || leadRepo.modifiedBy,
            timezoneClient: timezoneClient || leadRepo.timezoneClient,
        });

        const commandId &#x3D; uuid.v4();

        await this.executeCommand(
            Action.UPDATE_CONFIG,
            actionName,
            commandId,
            leadRepo as LeadRepoCommandModel
        );

        return this.successResponse;
    }

    async updateLeadRepoConfigs(
      id: string,
      configs: LeadRepoConfig[],
      actionName: string,
      userId: string,
      timezoneClient: string
    ) {
      this.loggerService.log(this.context, &#x27;updateLeadRepoConfig&#x27;);
      const leadRepo &#x3D; await this.queryRepo.findById(id, true);
      if (!leadRepo) {
          throw new BadRequestException({
              errors: ErrorConst.CommonError(
                  ErrorConst.NOT_FOUND,
                  &#x27;leadRepo&#x27;
              ),
          });
      }

      let { configs: defaultConfigs } &#x3D; leadRepo;

      await Bluebird.mapSeries(configs, async config &#x3D;&gt; {
        await this.validateExploitTime(config.exploitTime);
        const configOrgCharts &#x3D; await this.validateOrgChart(config.orgChartIds);
        await this.validateProject(config.projectId);

        Object.assign(config, {orgCharts: configOrgCharts});

        const existed &#x3D; defaultConfigs.find(i &#x3D;&gt; i.code &#x3D;&#x3D;&#x3D; config.code);
        if (!existed) {
          defaultConfigs &#x3D; this.updateConfigData(defaultConfigs, null, config);
        } else {
          Object.assign(existed, config);
          defaultConfigs &#x3D; this.updateConfigData(
            defaultConfigs,
            existed.code,
            existed
          );
        }
      });
    
      Object.assign(leadRepo, {
          configs,
          updatedDate: Date.now(),
          modifiedBy: userId || leadRepo.modifiedBy,
          timezoneClient: timezoneClient || leadRepo.timezoneClient,
      });

      const commandId &#x3D; uuid.v4();

      await this.executeCommand(
          Action.UPDATE_CONFIG,
          actionName,
          commandId,
          leadRepo as LeadRepoCommandModel
      );

      return this.successResponse;
    }

    async activeLeadRepoConfig(
        { id, configCode }: ActiveLeadRepoConfigDto,
        actionName: string,
        userId: string,
        timezoneClient: string
    ) {
        this.loggerService.log(this.context, &#x27;activeLeadRepoConfig&#x27;);
        const leadRepo &#x3D; await this.queryRepo.findById(id, true);
        if (!leadRepo) {
            throw new BadRequestException({
                errors: ErrorConst.CommonError(
                    ErrorConst.NOT_FOUND,
                    &#x27;leadRepo&#x27;
                ),
            });
        }

        const { configs } &#x3D; leadRepo;

        const targetIdx &#x3D; configs.findIndex((item) &#x3D;&gt; item.code &#x3D;&#x3D;&#x3D; configCode);
        if (targetIdx &#x3D;&#x3D;&#x3D; -1) {
            throw new BadRequestException({
                errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, &#x27;config&#x27;),
            });
        }

        const target &#x3D; cloneDeep(configs[targetIdx]);
        Object.assign(target, { active: !target.active });

        const updatedConfigs &#x3D; updateArrData(target, configs, targetIdx);
        Object.assign(leadRepo, {
            configs: updatedConfigs,
            modifiedBy: userId,
            updatedDate: Date.now(),
            timezoneClient,
        });

        const commandId &#x3D; uuid.v4();

        await this.executeCommand(
            Action.ACTIVE_CONFIG,
            actionName,
            commandId,
            leadRepo as LeadRepoCommandModel
        );
        return this.successResponse;
    }

    async bulkUpdateLeadConfig(
      repos: LeadRepo[],
      actionName?: string
    ) {
      const commandId &#x3D; uuid.v4();
      return this.executeCommand(Action.BULK_UPDATE, actionName, commandId, repos);
    }

    private async validateOrgChart(orgChartIds: string[]) {
        const orgCharts: any[] &#x3D; await this.orgchartClient.sendDataPromise(
            { posIds: orgChartIds },
            CmdPatternConst.ORGCHART.GET_POS_BY_QUERY
        );

        if (orgCharts?.length !&#x3D;&#x3D; orgChartIds.length) {
            throw new BadRequestException({
                errors: ErrorConst.CommonError(
                    ErrorConst.NOT_FOUND,
                    &#x27;orgchart&#x27;
                ),
            });
        }

        return orgCharts.map((item) &#x3D;&gt; ({
          id: item.id,
          name: item.name,
          staffIds: item.staffIds,
        }));
    }

    private async validateProject(projectId: string) {
        const project &#x3D; await this.propertyClient.sendDataPromise(
            { id: projectId },
            CmdPatternConst.PROJECT.GET_PROJECT_BY_ID
        );

        if (!project) {
            throw new BadRequestException({
                errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, &#x27;project&#x27;),
            });
        }
    }

    private async validateExploitTime(exploitTime: DateRange) {
        if (!exploitTime?.from || !exploitTime?.to) {
            throw new BadRequestException({
                errors: ErrorConst.CommonError(
                    ErrorConst.INVALID_FIELD,
                    &#x27;exploitTime&#x27;
                ),
            });
        }
        if (new Date(exploitTime.from) &gt; new Date(exploitTime.to)) {
            throw new BadRequestException({
                errors: ErrorConst.CommonError(
                    ErrorConst.INVALID_FIELD,
                    &#x27;exploitTime&#x27;
                ),
            });
        }
    }

    private validateDuration(duration: number, hotConfig &#x3D; false) {
      let minDuration &#x3D; Number(process.env.MIN_DURATION) || 10;
      if (hotConfig) {
        minDuration &#x3D; Number(process.env.MIN_DURATION_HOT) || 1;
      }
      if (duration &lt; minDuration) {
          throw new BadRequestException({
              errors: ErrorConst.CommonError(
                  ErrorConst.INVALID_FIELD,
                  &#x27;assignDuration&#x27;
              ),
          });
      }
    }

    private async executeCommand(
        action: string,
        actionName: string,
        commandId: string,
        item: LeadRepo | LeadRepo[]
    ) {
        switch (action) {
            case Action.CREATE:
                return this.commandBus.execute(
                    new CreateLeadRepoCommand(actionName, commandId, item as any)
                );
            case Action.UPDATE_MAIN:
                return this.commandBus.execute(
                    new UpdateLeadRepoCommand(actionName, commandId, item as any)
                );
            case Action.UPDATE_CONFIG:
                return this.commandBus.execute(
                    new UpdateConfigLeadRepoCommand(actionName, commandId, item as any)
                );
            case Action.ACTIVE_CONFIG:
                return this.commandBus.execute(
                    new ActiveConfigLeadRepoCommand(actionName, commandId, item as any)
                );
            case Action.BULK_UPDATE:
                return this.commandBus.execute(
                  new BulkUpdateLeadRepoCommand(actionName, commandId, item as any)
                )
            default:
                break;
        }
    }

    private updateConfigData(src: LeadRepoConfig[], code: string, record: LeadRepoConfig) {
      if (!code) {
        src &#x3D; src.concat(record);
      } else {
        const idx &#x3D; src.findIndex(item &#x3D;&gt; item.code &#x3D;&#x3D;&#x3D; code);
        src &#x3D; [...src.slice(0, idx), record, ...src.slice(idx + 1)];
      }

      return src;
    }
}
</code></pre>
    </div>

</div>







                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'LeadRepoDomainService.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
