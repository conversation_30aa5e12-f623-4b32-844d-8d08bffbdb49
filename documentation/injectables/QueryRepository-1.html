<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-lead documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-lead documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">







<ol class="breadcrumb">
  <li>Injectables</li>
  <li>QueryRepository</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/leadCare.queryside/repository/query.repository.ts</code>
        </p>




            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Readonly</span>
                                <a href="#context">context</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#assignLeadCare">assignLeadCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#assignLeadCareForEmployee">assignLeadCareForEmployee</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#checkDuplicate">checkDuplicate</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#checkDuplicateUpdate">checkDuplicateUpdate</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#closeTicketLeadCare">closeTicketLeadCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#count">count</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#countAll">countAll</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#countAllTickets">countAllTickets</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#countLeadCareReadyForAssigningByEmp">countLeadCareReadyForAssigningByEmp</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#countLeadCareReadyForAssigningByPOS">countLeadCareReadyForAssigningByPOS</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#countLeadCaresProcessBy">countLeadCaresProcessBy</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#countLifeCycleStatusByEmployee">countLifeCycleStatusByEmployee</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#countLifeCycleStatusForLeadCares">countLifeCycleStatusForLeadCares</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#countPrimaryLeadCare">countPrimaryLeadCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#countProcessTicketsConsignmentByUser">countProcessTicketsConsignmentByUser</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#countProcessTicketsDemandByUser">countProcessTicketsDemandByUser</a>
                            </li>
                            <li>
                                <a href="#countReport">countReport</a>
                            </li>
                            <li>
                                <a href="#countReportInPool">countReportInPool</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#create">create</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createMany">createMany</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#delete">delete</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#deleteMany">deleteMany</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#expiredLeadCare">expiredLeadCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#failLeadCare">failLeadCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#filter">filter</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#filterReport">filterReport</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAggregateModelById">findAggregateModelById</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAll">findAll</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAllAdvising">findAllAdvising</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAllCommon">findAllCommon</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAllPrimary">findAllPrimary</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAllPrimaryBQL">findAllPrimaryBQL</a>
                            </li>
                            <li>
                                <a href="#findLeadCareByCustomerId">findLeadCareByCustomerId</a>
                            </li>
                            <li>
                                <a href="#findLeadCareByEmployeeId">findLeadCareByEmployeeId</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findLeadCareById">findLeadCareById</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findLeadCareByIdBql">findLeadCareByIdBql</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findLeadCaresAssigned">findLeadCaresAssigned</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findLeadCaresAssignedEmployee">findLeadCaresAssignedEmployee</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findLeadCaresProcessBy">findLeadCaresProcessBy</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findLeadCaresToAssign">findLeadCaresToAssign</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findMany">findMany</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findOne">findOne</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findTransferByContractId">findTransferByContractId</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getAllByPos">getAllByPos</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getByCustomer">getByCustomer</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getLeadCareByStatus">getLeadCareByStatus</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getLeadCaresByStatusForExport">getLeadCaresByStatusForExport</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#isReadyToAssign">isReadyToAssign</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#isValidBeforePull">isValidBeforePull</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#isValidMarkProcessing">isValidMarkProcessing</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#listAllAdvising">listAllAdvising</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#listAllCommon">listAllCommon</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#listLeadCaresProcessBy">listLeadCaresProcessBy</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#pendingLeadCare">pendingLeadCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#processingLeadCare">processingLeadCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#reassignLeadCare">reassignLeadCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#saveAll">saveAll</a>
                            </li>
                            <li>
                                    <span class="modifier">Protected</span>
                                <a href="#transformSort">transformSort</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#unprocessingLeadCare">unprocessingLeadCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#update">update</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateByQuery">updateByQuery</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateOne">updateOne</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateStatus">updateStatus</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section>
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(readModel: Model<ILeadCareDocument>, employeeRepository: <a href="../injectables/EmployeeQueryRepository.html">EmployeeQueryRepository</a>, loggerService: <a href="../injectables/MsxLoggerService.html">MsxLoggerService</a>, propertyClient: <a href="../injectables/PropertyClient.html">PropertyClient</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="28" class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:28</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>readModel</td>
                                                  
                                                        <td>
                                                                    <code>Model&lt;ILeadCareDocument&gt;</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>employeeRepository</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/EmployeeQueryRepository.html" target="_self" >EmployeeQueryRepository</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>loggerService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/MsxLoggerService.html" target="_self" >MsxLoggerService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>propertyClient</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/PropertyClient.html" target="_self" >PropertyClient</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section>
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="assignLeadCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            assignLeadCare
                        </b>
                        <a href="#assignLeadCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>assignLeadCare(model)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="679"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:679</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>model</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="assignLeadCareForEmployee"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            assignLeadCareForEmployee
                        </b>
                        <a href="#assignLeadCareForEmployee"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>assignLeadCareForEmployee(model)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="400"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:400</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>model</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="checkDuplicate"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            checkDuplicate
                        </b>
                        <a href="#checkDuplicate"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>checkDuplicate(query)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="360"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:360</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;boolean&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="checkDuplicateUpdate"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            checkDuplicateUpdate
                        </b>
                        <a href="#checkDuplicateUpdate"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>checkDuplicateUpdate(query)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="375"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:375</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;boolean&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="closeTicketLeadCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            closeTicketLeadCare
                        </b>
                        <a href="#closeTicketLeadCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>closeTicketLeadCare(model)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="700"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:700</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>model</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="count"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            count
                        </b>
                        <a href="#count"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>count(query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="561"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:561</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="countAll"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            countAll
                        </b>
                        <a href="#countAll"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>countAll(query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="2044"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:2044</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>&quot;countAll&quot;</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="countAllTickets"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            countAllTickets
                        </b>
                        <a href="#countAllTickets"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>countAllTickets()</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="914"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:914</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="countLeadCareReadyForAssigningByEmp"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            countLeadCareReadyForAssigningByEmp
                        </b>
                        <a href="#countLeadCareReadyForAssigningByEmp"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>countLeadCareReadyForAssigningByEmp(employee: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, type: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1548"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:1548</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>employee</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>type</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="countLeadCareReadyForAssigningByPOS"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            countLeadCareReadyForAssigningByPOS
                        </b>
                        <a href="#countLeadCareReadyForAssigningByPOS"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>countLeadCareReadyForAssigningByPOS(posId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, type: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1527"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:1527</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>posId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>type</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="countLeadCaresProcessBy"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            countLeadCaresProcessBy
                        </b>
                        <a href="#countLeadCaresProcessBy"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>countLeadCaresProcessBy(loggedUser: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="959"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:959</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>loggedUser</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;number&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="countLifeCycleStatusByEmployee"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            countLifeCycleStatusByEmployee
                        </b>
                        <a href="#countLifeCycleStatusByEmployee"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>countLifeCycleStatusByEmployee(employeeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String" target="_blank">String</a>, lifeCycleStatus: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="885"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:885</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>employeeId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String" target="_blank" >String</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>lifeCycleStatus</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="countLifeCycleStatusForLeadCares"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            countLifeCycleStatusForLeadCares
                        </b>
                        <a href="#countLifeCycleStatusForLeadCares"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>countLifeCycleStatusForLeadCares(leadCareIds, lifeCycleStatus)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="900"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:900</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>leadCareIds</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>lifeCycleStatus</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="countPrimaryLeadCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            countPrimaryLeadCare
                        </b>
                        <a href="#countPrimaryLeadCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>countPrimaryLeadCare(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1580"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:1580</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="countProcessTicketsConsignmentByUser"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            countProcessTicketsConsignmentByUser
                        </b>
                        <a href="#countProcessTicketsConsignmentByUser"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>countProcessTicketsConsignmentByUser(query)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="940"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:940</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="countProcessTicketsDemandByUser"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            countProcessTicketsDemandByUser
                        </b>
                        <a href="#countProcessTicketsDemandByUser"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>countProcessTicketsDemandByUser(query)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="921"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:921</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="countReport"></a>
                    <span class="name">
                        <b>
                            countReport
                        </b>
                        <a href="#countReport"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>countReport(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, posInPool: string[])</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1368"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:1368</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>posInPool</td>
                                    <td>
                                            <code>string[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>[]</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="countReportInPool"></a>
                    <span class="name">
                        <b>
                            countReportInPool
                        </b>
                        <a href="#countReportInPool"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>countReportInPool(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, posInPool: string[])</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1476"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:1476</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>posInPool</td>
                                    <td>
                                            <code>string[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>[]</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="create"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            create
                        </b>
                        <a href="#create"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>create(readModel)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="339"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:339</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>readModel</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;ILeadCareDocument&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createMany"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            createMany
                        </b>
                        <a href="#createMany"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createMany(records: ILeadCare[])</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1827"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:1827</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>records</td>
                                    <td>
                                            <code>ILeadCare[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="delete"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            delete
                        </b>
                        <a href="#delete"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>delete(id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="662"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:662</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteMany"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            deleteMany
                        </b>
                        <a href="#deleteMany"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteMany()</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="670"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:670</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="expiredLeadCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            expiredLeadCare
                        </b>
                        <a href="#expiredLeadCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>expiredLeadCare(model: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="842"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:842</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>model</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="failLeadCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            failLeadCare
                        </b>
                        <a href="#failLeadCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>failLeadCare(model: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="791"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:791</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>model</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="filter"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            filter
                        </b>
                        <a href="#filter"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>filter(query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, isPaging: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1179"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:1179</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>isPaging</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>false</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="filterReport"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            filterReport
                        </b>
                        <a href="#filterReport"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>filterReport(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, posInPool: any[], isPaging: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1236"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:1236</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>posInPool</td>
                                    <td>
                                            <code>any[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>[]</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>isPaging</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>false</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAggregateModelById"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findAggregateModelById
                        </b>
                        <a href="#findAggregateModelById"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAggregateModelById(id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="327"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:327</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;QueryAggregateModel&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAll"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findAll
                        </b>
                        <a href="#findAll"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAll()</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="38"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:38</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;ILeadCareDocument[]&gt;</code>

                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAllAdvising"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findAllAdvising
                        </b>
                        <a href="#findAllAdvising"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAllAdvising()</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="76"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:76</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;ILeadCareDocument[]&gt;</code>

                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAllCommon"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findAllCommon
                        </b>
                        <a href="#findAllCommon"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAllCommon(query)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="47"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:47</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;ILeadCareDocument[]&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAllPrimary"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findAllPrimary
                        </b>
                        <a href="#findAllPrimary"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAllPrimary(user, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, isTransfer)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="97"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:97</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>isTransfer</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>false</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;any&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAllPrimaryBQL"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findAllPrimaryBQL
                        </b>
                        <a href="#findAllPrimaryBQL"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAllPrimaryBQL(user, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, projectBqlMember: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1847"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:1847</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>projectBqlMember</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;any&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findLeadCareByCustomerId"></a>
                    <span class="name">
                        <b>
                            findLeadCareByCustomerId
                        </b>
                        <a href="#findLeadCareByCustomerId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findLeadCareByCustomerId(customerId: string[], mapping: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1152"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:1152</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>customerId</td>
                                    <td>
                                            <code>string[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>[]</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>mapping</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>false</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findLeadCareByEmployeeId"></a>
                    <span class="name">
                        <b>
                            findLeadCareByEmployeeId
                        </b>
                        <a href="#findLeadCareByEmployeeId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findLeadCareByEmployeeId(employeeId: string[])</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1574"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:1574</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>employeeId</td>
                                    <td>
                                            <code>string[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>[]</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findLeadCareById"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findLeadCareById
                        </b>
                        <a href="#findLeadCareById"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findLeadCareById(id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="295"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:295</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;ILeadCareDocument&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findLeadCareByIdBql"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findLeadCareByIdBql
                        </b>
                        <a href="#findLeadCareByIdBql"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findLeadCareByIdBql(id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="2009"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:2009</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findLeadCaresAssigned"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findLeadCaresAssigned
                        </b>
                        <a href="#findLeadCaresAssigned"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findLeadCaresAssigned()</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="756"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:756</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findLeadCaresAssignedEmployee"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findLeadCaresAssignedEmployee
                        </b>
                        <a href="#findLeadCaresAssignedEmployee"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findLeadCaresAssignedEmployee(employeeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="745"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:745</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>employeeId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findLeadCaresProcessBy"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findLeadCaresProcessBy
                        </b>
                        <a href="#findLeadCaresProcessBy"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findLeadCaresProcessBy(loggedUser, type)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="435"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:435</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>loggedUser</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>type</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findLeadCaresToAssign"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findLeadCaresToAssign
                        </b>
                        <a href="#findLeadCaresToAssign"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findLeadCaresToAssign(employeeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, posId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, exchangeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, type: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, shared: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="586"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:586</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>employeeId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>posId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>exchangeId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>type</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>shared</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>false</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findMany"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findMany
                        </b>
                        <a href="#findMany"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findMany(filter, project: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank">object</a>, sort: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank">object</a>, limit: null)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1842"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:1842</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>filter</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>project</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>sort</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>limit</td>
                                    <td>
                                            <code>null</code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>null</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findOne"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findOne
                        </b>
                        <a href="#findOne"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findOne(query, fields: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank">object</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="265"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:265</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>fields</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;ILeadCareDocument&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findTransferByContractId"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findTransferByContractId
                        </b>
                        <a href="#findTransferByContractId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findTransferByContractId(customerId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String" target="_blank">String</a>, contractId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String" target="_blank">String</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="274"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:274</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>customerId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String" target="_blank" >String</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>contractId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String" target="_blank" >String</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;ILeadCareDocument[]&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getAllByPos"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getAllByPos
                        </b>
                        <a href="#getAllByPos"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getAllByPos(posId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String" target="_blank">String</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="250"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:250</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>posId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String" target="_blank" >String</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;ILeadCareDocument[]&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getByCustomer"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getByCustomer
                        </b>
                        <a href="#getByCustomer"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getByCustomer(customerId)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="241"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:241</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>customerId</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getLeadCareByStatus"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getLeadCareByStatus
                        </b>
                        <a href="#getLeadCareByStatus"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getLeadCareByStatus(user, status: <a href="../undefineds/ExploitCareEnum.html">ExploitCareEnum</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, page: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>, pageSize: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1711"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:1711</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>status</td>
                                    <td>
                                                <code><a href="../miscellaneous/enumerations.html#ExploitCareEnum" target="_self" >ExploitCareEnum</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>page</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>pageSize</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getLeadCaresByStatusForExport"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getLeadCaresByStatusForExport
                        </b>
                        <a href="#getLeadCaresByStatusForExport"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getLeadCaresByStatusForExport(user, status: <a href="../undefineds/ExploitCareEnum.html">ExploitCareEnum</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1754"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:1754</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>status</td>
                                    <td>
                                                <code><a href="../miscellaneous/enumerations.html#ExploitCareEnum" target="_self" >ExploitCareEnum</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isReadyToAssign"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            isReadyToAssign
                        </b>
                        <a href="#isReadyToAssign"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>isReadyToAssign(leadCareId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="722"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:722</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>leadCareId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isValidBeforePull"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            isValidBeforePull
                        </b>
                        <a href="#isValidBeforePull"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>isValidBeforePull(employeeId, type: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="564"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:564</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>employeeId</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>type</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isValidMarkProcessing"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            isValidMarkProcessing
                        </b>
                        <a href="#isValidMarkProcessing"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>isValidMarkProcessing(employeeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, leadCareId)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="732"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:732</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>employeeId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>leadCareId</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="listAllAdvising"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            listAllAdvising
                        </b>
                        <a href="#listAllAdvising"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>listAllAdvising(page: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>, pageSize: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>, query)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1117"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:1117</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-description"><p>Lấy danh sách yêu cầu tư vấn dịch vụ</p>
</div>

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>page</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>1</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>pageSize</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>10</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;ILeadCareDocument[]&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="listAllCommon"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            listAllCommon
                        </b>
                        <a href="#listAllCommon"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>listAllCommon(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, page: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>, pageSize: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="974"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:974</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>page</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>1</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>pageSize</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>10</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;ILeadCareDocument[]&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="listLeadCaresProcessBy"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            listLeadCaresProcessBy
                        </b>
                        <a href="#listLeadCaresProcessBy"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>listLeadCaresProcessBy(loggedUser: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="534"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:534</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Description</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>loggedUser</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                    <td>
                                        <p>user</p>

                                    </td>
                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                    <td>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="pendingLeadCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            pendingLeadCare
                        </b>
                        <a href="#pendingLeadCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>pendingLeadCare(model)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="866"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:866</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>model</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="processingLeadCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            processingLeadCare
                        </b>
                        <a href="#processingLeadCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>processingLeadCare(id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String" target="_blank">String</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="621"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:621</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String" target="_blank" >String</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="reassignLeadCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            reassignLeadCare
                        </b>
                        <a href="#reassignLeadCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>reassignLeadCare(model: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="767"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:767</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>model</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="saveAll"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            saveAll
                        </b>
                        <a href="#saveAll"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>saveAll(models: any[])</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="2056"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:2056</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>models</td>
                                    <td>
                                            <code>any[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;any&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="transformSort"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Protected</span>
                            transformSort
                        </b>
                        <a href="#transformSort"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>transformSort(paramSort?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String" target="_blank">String</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1161"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:1161</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>paramSort</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String" target="_blank" >String</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="unprocessingLeadCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            unprocessingLeadCare
                        </b>
                        <a href="#unprocessingLeadCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>unprocessingLeadCare(model)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="644"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:644</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>model</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="update"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            update
                        </b>
                        <a href="#update"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>update(model)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="349"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:349</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>model</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;ILeadCareDocument&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateByQuery"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateByQuery
                        </b>
                        <a href="#updateByQuery"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateByQuery(query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, updateQuery)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="829"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:829</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>updateQuery</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateOne"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateOne
                        </b>
                        <a href="#updateOne"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateOne(model: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="814"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:814</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>model</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateStatus"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateStatus
                        </b>
                        <a href="#updateStatus"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateStatus(model)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="421"
                            class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:421</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>model</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>
            <section>
    
        <h3 id="inputs">
            Properties
        </h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="context"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Private</span>
                                <span class="modifier">Readonly</span>
                            context</b>
                            <a href="#context"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>QueryRepository.name</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="28" class="link-to-prism">src/modules/leadCare.queryside/repository/query.repository.ts:28</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Model } from &#x27;mongoose&#x27;;
import { Inject, Injectable } from &#x27;@nestjs/common&#x27;;
import { QueryAggregateModel } from &#x27;../models/query-aggregate.model&#x27;;
import { ILeadCareDocument } from &#x27;../interfaces/document.interface&#x27;;
import _ &#x3D; require(&#x27;lodash&#x27;);
import { LifeCycleStatusEnum } from &#x27;../../shared/enum/life-cycle-status.enum&#x27;;
import { SourceEnum } from &#x27;../../shared/enum/source.enum&#x27;;
import { isNullOrUndefined } from &#x27;util&#x27;;
import { CommonConst } from &#x27;../../../modules/shared/constant/common.const&#x27;;
import { TransactionTypeEnum } from &#x27;../../../modules/shared/enum/transaction-type.enum&#x27;;
import { EmployeeQueryRepository } from &#x27;../../employee/repository/query.repository&#x27;;
import { PermissionConst } from &#x27;../../../modules/shared/constant/permission.const&#x27;;
import { ExploitCareEnum, ExploitEnum } from &#x27;../../shared/enum/exploit.enum&#x27;;
import { ILeadCare } from &#x27;../../shared/services/leadCare/interfaces/leadCare.interface&#x27;;
import * as Bluebird from &#x27;bluebird&#x27;;
import * as moment from &#x27;moment&#x27;;
import { MsxLoggerService } from &#x27;../../logger/logger.service&#x27;;
import { PropertyClient } from &#x27;../../mgs-sender/property.client&#x27;;
import { CmdPatternConst } from &#x27;../../shared/constant/cmd-pattern.const&#x27;;
import { AnyNaptrRecord } from &#x27;dns&#x27;;
import { LeadRepoCareEnum } from &#x27;../../../modules/shared/enum/type.enum&#x27;;


const clc &#x3D; require(&quot;cli-color&quot;);

@Injectable()
export class QueryRepository {
    private readonly context &#x3D; QueryRepository.name;

    constructor(
        @Inject(CommonConst.QUERY_MODEL_TOKEN)
        private readonly readModel: Model&lt;ILeadCareDocument&gt;,
        private readonly employeeRepository: EmployeeQueryRepository,
        private readonly loggerService: MsxLoggerService,
        private readonly propertyClient: PropertyClient
    ) { }

    async findAll(): Promise&lt;ILeadCareDocument[]&gt; {

        return await this.readModel.find()
            .exec()
            .then(result &#x3D;&gt; {
                return result;
            });
    }

    async findAllCommon(query): Promise&lt;ILeadCareDocument[]&gt; {
        const match: any &#x3D; {
            source : {
                $nin: [
                    SourceEnum.DangTin,
                    SourceEnum.YeuCau
            ]},
            type: query.type
        };
        if (!_.isEmpty(query.keywords)) {
            match[&#x27;name&#x27;] &#x3D; { $regex: new RegExp(query.keywords), $options: &#x27;i&#x27; };
        }
        return await this.readModel.aggregate([
            {
                $match: match
            },
            {
                $lookup: {
                    from: &#x27;employees&#x27;,
                    localField: &#x27;processBy&#x27;,
                    foreignField: &#x27;id&#x27;,
                    as: &#x27;employeeTakeCare&#x27;
                }
            }

        ]).allowDiskUse(true)
            .exec();
    }

    async findAllAdvising(): Promise&lt;ILeadCareDocument[]&gt; {
        return await this.readModel.aggregate([

            {
                $match: {
                    type: CommonConst.TYPE.ADVISING
                }
            },
            {
                $lookup: {
                    from: &#x27;employees&#x27;,
                    localField: &#x27;processBy&#x27;,
                    foreignField: &#x27;id&#x27;,
                    as: &#x27;employeeTakeCare&#x27;
                }
            }
        ])
            .allowDiskUse(true)
            .exec();
    }

    async findAllPrimary(user, query: any, isTransfer &#x3D; false): Promise&lt;any&gt; {
        const match: any &#x3D; {};
        const matchKeywords: any &#x3D; {};
        let sort: any &#x3D; {
            code: 1
        };
        // match.type &#x3D; CommonConst.TYPE.PRIMARY;
        match.$and &#x3D; [];
        match.$and.push({type: CommonConst.TYPE.PRIMARY});

        if (!user.roles.includes(PermissionConst.LEAD_REPORT_QUERY_ALL_CARE)) {
            if (user &amp;&amp; user.id) {
                if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_PROJECT_CARE)) {
                  const projectBqlMember &#x3D; await this.propertyClient.sendDataPromise({id:user.id},CmdPatternConst.PROJECT.GET_PROJECT_BY_CUSTOMER_SERVICE)
                  match[&#x27;project.id&#x27;] &#x3D; { $in: projectBqlMember }

                } else if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_EMP_CARE)) {
                    const emp &#x3D; await this.employeeRepository.findOne({id: user.id});
                    const $or &#x3D; [
                      {processBy: {$nin: [null, &#x27;&#x27;]}}, {processBy: {$in: emp.staffIds}},
                      {&#x27;takeCare.id&#x27;: {$nin: [null, &#x27;&#x27;]}}, {&#x27;takeCare.id&#x27;: {$in: emp.staffIds}}
                    ]
                    match.$and.push({$or});
                } else if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_IMPORTER_CARE)) {
                    match.$and.push({&#x27;importedBy.id&#x27;: user.id});
                } else {
                    const $or &#x3D; [
                      {processBy: user.id},
                      {&#x27;takeCare.id&#x27;: user.id}
                    ]
                    match.$and.push({$or});
                }
            } else {
               return [];
            }
        }

        if (query) {
            if (!_.isEmpty(query.query)) {
                matchKeywords.$or &#x3D; [
                    { &#x27;name&#x27;: { $regex: query.query, $options: &#x27;i&#x27; } },
                    { &#x27;phone&#x27;: { $regex: query.query, $options: &#x27;i&#x27; } },
                    { &#x27;code&#x27;: { $regex: query.query, $options: &#x27;i&#x27; } },
                    { &#x27;updatedName&#x27;: { $regex: query.query, $options: &#x27;i&#x27; } },
                    { &#x27;updatedPhone&#x27;: { $regex: query.query, $options: &#x27;i&#x27; } },
                    { &#x27;note&#x27;: { $regex: query.query, $options: &#x27;i&#x27; } },
                    { &#x27;project.name&#x27;: { $regex: query.query, $options: &#x27;i&#x27; } },
                ];
            }

            // Transfer chỉ lấy đc theo pos.
            if (isTransfer) {
                match[&#x27;repoType&#x27;] &#x3D; LeadRepoCareEnum.TRANSFER;

                // Quyền get all transfer
                if (!user.roles.includes(PermissionConst.ADMIN_GET_ALL_TRANSER_HISTORY)) {
                    match[&#x27;repoId&#x27;] &#x3D; {$in : query.repoIds ? query.repoIds : []};
                }
            } else {
                match[&#x27;repoType&#x27;] &#x3D; {$ne : LeadRepoCareEnum.TRANSFER};
            }

            if (!_.isEmpty(query[&#x27;exploitStatus&#x27;])) {
                let exploitStatus &#x3D; query[&#x27;exploitStatus&#x27;].split(&#x27;,&#x27;)
                match[&#x27;exploitStatus&#x27;] &#x3D; {$in : exploitStatus};
            }
            if (!_.isEmpty(query[&#x27;resource&#x27;])) {
                match[&#x27;resource&#x27;] &#x3D; query[&#x27;resource&#x27;];
            }

            if (!_.isEmpty(query.employeeId)) {
                match.$and.push({processBy: query.employeeId});
            } else if (!_.isEmpty(query.posId)) {
                match.$and.push({&#x27;pos.id&#x27;: query.posId});
            } else if (!_.isEmpty(query.exchangeId)) {
                match.$and.push({$or: [
                    {&#x27;pos.id&#x27;: query.exchangeId},
                    {&#x27;pos.parentId&#x27;: query.exchangeId}
                ]});
            }

            if(!_.isEmpty(query[&#x27;customerId&#x27;])) {
                match.$and.push({customerId: query[&#x27;customerId&#x27;]})
            }

            if (!_.isEmpty(query.assignedDateFrom)) {
                match.$and.push({assignedDate: {$gte: new Date(parseInt(query.assignedDateFrom, 10))}});
            }

            if (!_.isEmpty(query.assignedDateTo)) {
                match.$and.push({assignedDate: {$lte: new Date(parseInt(query.assignedDateTo, 10))}});
            }

            if (!_.isEmpty(query.sort)) {
                sort &#x3D; this.transformSort(query.sort) || {
                    code: 1
                };
            }

            if (!_.isEmpty(query[&#x27;createdFrom&#x27;]) || !_.isEmpty(query[&#x27;createdTo&#x27;])) {
                match[&#x27;createdDate&#x27;] &#x3D; {};
                if (!_.isEmpty(query[&#x27;createdFrom&#x27;])) {
                    match[&#x27;createdDate&#x27;][&#x27;$gte&#x27;] &#x3D; new Date(parseInt(query[&#x27;createdFrom&#x27;]));
                }
                if (!_.isEmpty(query[&#x27;createdTo&#x27;])) {
                    match[&#x27;createdDate&#x27;][&#x27;$lte&#x27;] &#x3D; new Date(parseInt(query[&#x27;createdTo&#x27;]));
                }
            }
            if (!_.isEmpty(query.forBQL)) {
                match[&#x27;forBQL&#x27;] &#x3D; true;
            } else {
                match[&#x27;forBQL&#x27;] &#x3D; { $ne: true };
            }
        }

        const aggregate: any[] &#x3D; [
            {
                $match: matchKeywords
            },
            {
                $match: match
            },
            { $sort: sort },
        ]
        console.log(JSON.stringify(aggregate))
        if (!_.isEmpty(query[&#x27;page&#x27;]) || !_.isEmpty(query[&#x27;pageSize&#x27;])) {
            const page: number &#x3D; parseInt(query[&#x27;page&#x27;]) || 1;
            const pageSize: number &#x3D; parseInt(query[&#x27;pageSize&#x27;]) || 10;
            aggregate.push({
                $facet: {
                    rows: [{ $skip: Math.floor((pageSize * page) - pageSize) }, { $limit: pageSize }],
                    totalCount: [
                        {
                            $count: &#x27;count&#x27;
                        }
                    ]
                }
            });
        }
        // console.log(JSON.stringify(aggregate));
        return await this.readModel.aggregate(aggregate)
            .allowDiskUse(true)
            .exec();
    }
    async getByCustomer(customerId) {
        return await this.readModel.find({ customerId })
            .sort({createdDate: -1})
            .exec()
            .then(result &#x3D;&gt; {
                return result;
            });
    }

    async getAllByPos(posId: String): Promise&lt;ILeadCareDocument[]&gt; {

        return await this.readModel.aggregate([
            { $match: { &#x27;pos.id&#x27;: posId } },
            { $sort: { createDate: 1 } }
        ]).allowDiskUse(true)
            .exec()
            .then(rs &#x3D;&gt; {
                return rs;
            })
            .catch(err &#x3D;&gt; {
                return err;
            });
    }

    async findOne(query, fields &#x3D; {}): Promise&lt;ILeadCareDocument&gt; {

        return await this.readModel.findOne(query, fields)
            .exec()
            .then(result &#x3D;&gt; {
                return result;
            });
    }

    async findTransferByContractId(customerId: String, contractId: String): Promise&lt;ILeadCareDocument[]&gt; {
        return await this.readModel.aggregate([
            { 
                $match: { 
                    &quot;customData.contractId&quot;:contractId,
                    &quot;repoType&quot;:&quot;transfer&quot;,
                    &quot;customerId&quot;:customerId
                } 
            },
            { $sort: { createdDate: -1 } },
            { $limit: 1}
        ]).allowDiskUse(true)
            .exec()
            .then(rs &#x3D;&gt; {
                return rs;
            })
            .catch(err &#x3D;&gt; {
                return err;
            });
    }

    async findLeadCareById(id: string): Promise&lt;ILeadCareDocument&gt; {
        return await this.readModel.aggregate(
            [
                {
                    $match: { id }
                },
                {
                    $lookup: {
                        from: &#x27;lead-repo-cares&#x27;,
                        localField: &#x27;repoId&#x27;,
                        foreignField: &#x27;id&#x27;,
                        as: &#x27;configData&#x27;
                    }
                }
            ]).allowDiskUse(true)
            .exec()
            .then((response) &#x3D;&gt; {
                if (response.length &gt; 0) {
                    if  (response[0].configData.length){
                        response[0].configData &#x3D; response[0].configData[0].configs.find((item) &#x3D;&gt; item.code &#x3D;&#x3D;&#x3D; response[0].repoConfigCode);
                    } else {
                        response[0].configData &#x3D; null;
                    }
                    return response[0];
                }
                return null;
            })
            .catch((exp) &#x3D;&gt; {
                return exp;
            });
    }

    async findAggregateModelById(id: string): Promise&lt;QueryAggregateModel&gt; {
        return await this.readModel.findOne({ id })
            .exec()
            .then((response) &#x3D;&gt; {
                console.log(&#x27;findAggregateModelById leadCare query side&#x27;);
                return new QueryAggregateModel(id);
            })
            .catch((exp) &#x3D;&gt; {
                return exp;
            });
    }

    async create(readModel): Promise&lt;ILeadCareDocument&gt; {
        return await this.readModel.create(readModel)
            .then((response) &#x3D;&gt; {
                console.log(&#x27;createEvent LeadCare at query side&#x27;);
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    async update(model): Promise&lt;ILeadCareDocument&gt; {
        model.updatedDate &#x3D; Date.now();
        return await this.readModel.update({ id: model.id }, model)
            .then((response) &#x3D;&gt; {
                console.log(&#x27;update LeadCare at query side&#x27;);
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    async checkDuplicate(query): Promise&lt;boolean&gt; {

        return await this.readModel.aggregate(
            [
                { $match: { name: query.name } }
            ]).allowDiskUse(true)
            .exec()
            .then((result) &#x3D;&gt; {
                if (result &amp;&amp; result.length &gt; 0) {
                    return true;
                }
                return false;
            });
    }

    async checkDuplicateUpdate(query): Promise&lt;boolean&gt; {

        return await this.readModel.aggregate(
            [
                {
                    $match: {
                        $and: [
                            { name: query.name },
                            { id: { $ne: query.id } }
                        ]
                    }
                }
            ]).allowDiskUse(true)
            .exec()
            .then((result) &#x3D;&gt; {
                if (result &amp;&amp; result.length &gt; 0) {
                    return true;
                }
                return false;
            })
            .catch((error) &#x3D;&gt; {
                throw error;
            });
    }

    async assignLeadCareForEmployee(model) {
        return await this.readModel.updateOne(
            { id: model.id },
            {
                $set: {
                    processBy: model.processBy,
                    timeOut: model.timeOut,
                    serverTime: model.serverTime,
                    utcTime: model.utcTime
                }
            },
        )
            .exec()
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    // Update status
    async updateStatus(model) {
        return await this.readModel.updateMany(
            { id: { $in: model.ids } },
            { $set: { status: model.status } },
            { multi: true }
        )
            .exec()
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    async findLeadCaresProcessBy(loggedUser, type) {
        const match: any &#x3D; {};
        if (type) {
            match.type &#x3D; type;
        }

         // get employee 
         const employee &#x3D; await this.employeeRepository.findOne( { &quot;id&quot;: loggedUser.id} );
         if (!employee) {
             return [];
         }
        return await this.readModel.aggregate([
            { $match: { match, 
                &#x27;leadCare.processBy&#x27;: {
                    $exists: true, $nin: [null, &quot;&quot;],
                    &#x27;$in&#x27;: employee.staffIds
                    }
                } 
            },
            // {
            //     $lookup: {
            //         from: &#x27;employees&#x27;,
            //         let: { processBy: &#x27;$processBy&#x27; },
            //         pipeline: [
            //             {
            //                 $match:
            //                 {
            //                     $expr:
            //                     {
            //                         $and:
            //                             [
            //                                 { $in: [&#x27;$$processBy&#x27;, &#x27;$staffIds&#x27;] },
            //                                 { $eq: [loggedUser.id, &#x27;$id&#x27;] },
            //                             ]
            //                     }
            //                 }
            //             },
            //         ],
            //         as: &#x27;data&#x27;
            //     }
            // },
            // { $unwind: &#x27;$data&#x27; },


            // {
            //     $project: {
            //         status: &#x27;$$ROOT.status&#x27;,
            //         description: &#x27;$$ROOT.description&#x27;,
            //         active: &#x27;$$ROOT.active&#x27;,
            //         modifiedBy: &#x27;$$ROOT.modifiedBy&#x27;,
            //         employeeTakeCare: &#x27;$$ROOT.employeeTakeCare&#x27;,
            //         customer: &#x27;$$ROOT.customer&#x27;,
            //         pos: &#x27;$$ROOT.pos&#x27;,
            //         ticketId: &#x27;$$ROOT.ticketId&#x27;,
            //         resource: &#x27;$$ROOT.resource&#x27;,
            //         id: &#x27;$$ROOT.id&#x27;,
            //         createDate: &#x27;$$ROOT.createDate&#x27;,
            //         // employee: &#x27;$data&#x27; // if you want to get employee
            //     }
            // },
            { $sort: { createDate: 1 } },
            {
                $project: {
                    info: &#x27;$$ROOT&#x27;,
                    isOwner: {
                        $cond: {
                            if: { $eq: [loggedUser.id, &#x27;$processBy&#x27;] },
                            then: &#x27;owners&#x27;,
                            else: &#x27;others&#x27;
                        }
                    }
                }
            },
            {
                $group:
                {
                    _id: &#x27;$isOwner&#x27;,
                    list: { $push: &#x27;$info&#x27; }
                }
            }
        ]).allowDiskUse(true)
            .exec()
            .then(rs &#x3D;&gt; {
                const result &#x3D; {};
                rs.forEach(e &#x3D;&gt; {
                    result[e._id] &#x3D; e.list;
                });
                return result;
            })
            .catch(err &#x3D;&gt; {
                return err;
            });
    }
    /**
     *
     * @param loggedUser user
     * @param {other || own }processType
     * @param query
     */
    async listLeadCaresProcessBy(loggedUser: any, query: any) {
        const processType &#x3D; query.processType || &#x27;other&#x27;;
        const limit &#x3D; Number(query.pageSize) || 10;
        const skip &#x3D; (Number(query.page) || 1) * limit - limit;
        const q: any &#x3D; {
            processBy: processType &#x3D;&#x3D;&#x3D; &#x27;other&#x27; ? { $nin: [loggedUser.id, null] } : loggedUser.id,
            lifeCycleStatus: { $nin: [LifeCycleStatusEnum.REMOVED, LifeCycleStatusEnum.PRIMARY_PROCESSING] },
            timeOut: { &quot;$gt&quot; : Date.now() } 

        };
        if (query.type){
            q.type &#x3D; query.type;
        } else {
            q.type &#x3D; { $ne : CommonConst.TYPE.PRIMARY}
        }
        let sort: any &#x3D; {
            createdDate: -1
        };
        if (!_.isEmpty(query.sort)) {
            sort &#x3D; this.transformSort(query.sort);
        }
        return await this.readModel.find(q)
            .sort(sort)
            .skip(skip)
            .limit(limit)
            .exec();
    }
    async count(query: any &#x3D; {}) {
        return this.readModel.countDocuments(query).exec();
    }
    async isValidBeforePull(employeeId, type: string) {
        const match: any &#x3D; { 
            processBy: employeeId, 
            lifeCycleStatus: { $nin: [LifeCycleStatusEnum.PENDING, LifeCycleStatusEnum.REMOVED, LifeCycleStatusEnum.COMPLETED, LifeCycleStatusEnum.PRIMARY_PROCESSING] },
            timeOut: { &quot;$gt&quot; : Date.now() }
        };
        if (type) {
            match.type &#x3D; type;
        } else {
            match.type &#x3D; { $ne: CommonConst.TYPE.PRIMARY}
        }
        return await this.readModel.find(match)
            .exec()
            .then(rs &#x3D;&gt; {
                if (rs &amp;&amp; rs.length &gt; 0) return false;
                return true;
            })
            .catch(err &#x3D;&gt; {
                return err;
            });
    }

    async findLeadCaresToAssign(employeeId: string, posId: string, exchangeId: string, type: string, shared: boolean &#x3D; false) {
        const match: any &#x3D; {
            processBy: null,
            $or: [{&#x27;pos.id&#x27;: posId},
                    {&#x27;pos.id&#x27;: exchangeId}
                ],
            // &#x27;processedHistory.processBy&#x27;: { $nin: [employeeId] },
            // &#x27;processedHistory.isReject&#x27;: { $ne: true },
            lifeCycleStatus: LifeCycleStatusEnum.IN_POOL,
        };
        if (shared) {
            match.$or.push({&#x27;pos.id&#x27;: &#x27;dxs-shared&#x27;});
        }
        if (type) {
            match.type &#x3D; type;
        } else {
            match.type &#x3D; { $ne: CommonConst.TYPE.ADVISING };
        }

        return await this.readModel.aggregate([
            {
                $match: match
            },
            { $sort: { createdDate: 1 } },
            { $limit: 1 }   // Update: chỉ cho phép xử lý yêu cầu mỗi lần lấy
        ]).allowDiskUse(true)
            .exec()
            .then(rs &#x3D;&gt; {
                return rs;
            })
            .catch(err &#x3D;&gt; {
                return err;
            });
    }

    async processingLeadCare(id: String) {
        let lifeCycleStatus &#x3D; LifeCycleStatusEnum.PROCESSING;
        const model &#x3D; await this.readModel.findOne({id});
        if (model.type &#x3D;&#x3D;&#x3D; CommonConst.TYPE.PRIMARY) {
            lifeCycleStatus &#x3D; LifeCycleStatusEnum.PRIMARY_PROCESSING;
        }
        return await this.readModel.updateOne(
            { id },
            {
                $set: {
                    updatedDate: Date.now(),
                    lifeCycleStatus
                }
            }
        )
            .exec()
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    async unprocessingLeadCare(model) {
        return await this.readModel.updateOne(
            { id: model.id },
            {
                $set: {
                    updatedDate: Date.now(),
                    lifeCycleStatus: LifeCycleStatusEnum.ASSIGNED, note: model.note
                }
            }
        )
            .exec()
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    async delete(id: string) {
        return await this.readModel.deleteOne({ id })
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }
    async deleteMany() {
        return await this.readModel.deleteMany({})
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    async assignLeadCare(model) {
        return await this.readModel.updateOne(
            { id: model.id },
            {
                $set: {
                    updatedDate: Date.now(),
                    processBy: model.processBy,
                    timeOut: model.timeOut,
                    lifeCycleStatus: model.lifeCycleStatus,
                    timezoneclient: model.timezoneclient,
                    assignedDate: Date.now()
                }
            },
        )
            .exec()
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }
    async closeTicketLeadCare(model) {
        return await this.readModel.updateOne(
            { id: model.id },
            {
                $set: {
                    updatedDate: Date.now(),
                    lifeCycleStatus: model.lifeCycleStatus,
                    timezoneclient: model.timezoneclient,
                    exploitHistory:model.exploitHistory,
                    status:model.status,
                    exploitStatus:model.exploitStatus
                }
            },
        )
            .exec()
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    async isReadyToAssign(leadCareId: string) {
        return await this.readModel.find({ id: leadCareId, processBy: null })
            .exec()
            .then(result &#x3D;&gt; {
                if (isNullOrUndefined(result)) return false;

                return true;
            });
    }

    async isValidMarkProcessing(employeeId: string, leadCareId) {
        return await this.readModel.find({ processBy: employeeId, lifeCycleStatus: LifeCycleStatusEnum.PROCESSING, id: leadCareId })
            .exec()
            .then(result &#x3D;&gt; {
                if (isNullOrUndefined(result) || result.length &#x3D;&#x3D;&#x3D; 0)
                    return true;
                return false;
            });
    }



    // Reassign
    async findLeadCaresAssignedEmployee(employeeId: string) {
        return await this.readModel.find({ processBy: employeeId })
            .exec()
            .then(rs &#x3D;&gt; {
                return rs;
            })
            .catch(err &#x3D;&gt; {
                return err;
            });
    }

    async findLeadCaresAssigned() {
        return await this.readModel.find({ processBy: { $ne: null }, lifeCycleStatus: { $nin: [LifeCycleStatusEnum.REMOVED, LifeCycleStatusEnum.PRIMARY_PROCESSING] } })
            .exec()
            .then(rs &#x3D;&gt; {
                return rs;
            })
            .catch(err &#x3D;&gt; {
                return err;
            });
    }

    async reassignLeadCare(model: any) {
        return await this.readModel.updateOne(
            { id: model.id },
            {
                $set:
                {
                    updatedDate: Date.now(),
                    modifiedBy: model.modifiedBy,
                    processBy: model.processBy,
                    timeOut: null,
                    lifeCycleStatus: model.lifeCycleStatus,
                    pos: model.pos,
                    processedHistory: model.processedHistory
                }
            }
        )
            .exec()
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    async failLeadCare(model: any) {
        return await this.readModel.updateOne(
            { id: model.id },
            {
                $set:
                {
                    updatedDate: Date.now(),
                    modifiedBy: model.modifiedBy,
                    processBy: model.processBy,
                    timeOut: null,
                    lifeCycleStatus: model.lifeCycleStatus,
                    pos: model.pos,
                    processedHistory: model.processedHistory
                }
            }
        )
            .exec()
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }
    async updateOne(model: any) {
        return await this.readModel.updateOne(
            { id: model.id },
            {
                $set: model
            }
        )
            .exec()
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    async updateByQuery(query: any, updateQuery) {
        return await this.readModel.updateOne(
            query,
            updateQuery
        )
            .exec()
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    async expiredLeadCare(model: any) {
        return await this.readModel.updateOne(
            { id: model.id },
            {
                $set:
                {
                    updatedDate: Date.now(),
                    modifiedBy: model.modifiedBy,
                    processBy: model.processBy,
                    timeOut: null,
                    lifeCycleStatus: model.lifeCycleStatus,
                    processedHistory: model.processedHistory
                }
            }
        )
            .exec()
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    // Pending
    async pendingLeadCare(model) {
        return await this.readModel.updateOne(
            { id: model.id },
            {
                $set: {
                    updatedDate: Date.now(),
                    lifeCycleStatus: LifeCycleStatusEnum.PENDING,
                    note: model.note
                }
            },
        )
            .exec()
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    async countLifeCycleStatusByEmployee(employeeId: String, lifeCycleStatus: string) {
        return await this.readModel.find(
            {
                processBy: employeeId,
                lifeCycleStatus,
            })
            .countDocuments()
            .then(rs &#x3D;&gt; {
                return rs;
            })
            .catch(ex &#x3D;&gt; {
                return ex;
            });
    }

    async countLifeCycleStatusForLeadCares(leadCareIds, lifeCycleStatus) {
        return await this.readModel.find(
            {
                id: { $in: leadCareIds },
                lifeCycleStatus,
            })
            .countDocuments()
            .then(rs &#x3D;&gt; {
                return rs;
            })
            .catch(ex &#x3D;&gt; {
                return ex;
            });
    }
    async countAllTickets() {
        return this.readModel.countDocuments().exec()
            .then(result &#x3D;&gt; {
                return { &#x27;totalTickets&#x27;: result };
            });
    }

    async countProcessTicketsDemandByUser(query) {
        return await this.readModel.find(
            {
                $and: [
                    query,
                    {
                        $or: [
                            { type: &#x27;BUY&#x27; },
                            { type: &#x27;RENT&#x27; },
                        ]
                    }
                ]
            },
        ).countDocuments()
            .exec()
            .then(demand &#x3D;&gt; {
                return demand;
            });
    }
    async countProcessTicketsConsignmentByUser(query) {
        return await this.readModel.find(
            {
                $and: [
                    query,
                    {
                        $or: [
                            { type: &#x27;SELL&#x27; },
                            { type: &#x27;LEASE&#x27; }
                        ]
                    }
                ]
            },
        ).countDocuments()
            .exec()
            .then(consignment &#x3D;&gt; {
                return consignment;
            });
    }
    async countLeadCaresProcessBy(loggedUser: any, query: any &#x3D; {}): Promise&lt;number&gt; {
        const processType &#x3D; query.processType || &#x27;other&#x27;;
        const q &#x3D; {
            processBy: processType &#x3D;&#x3D;&#x3D; &#x27;other&#x27; ? { $nin: [loggedUser.id, null] } : loggedUser.id,
            lifeCycleStatus: { $ne: LifeCycleStatusEnum.REMOVED },
            timeOut: { &quot;$gt&quot; : Date.now() } 
        };
        return await this.readModel.countDocuments(q).exec();
    }
    /**
     *
     * @param page
     * @param pageSize
     * @param query
     */
    async listAllCommon(user: any, page: number &#x3D; 1, pageSize: number &#x3D; 10, query: any &#x3D; {}): Promise&lt;ILeadCareDocument[]&gt; {
        let sort: any &#x3D; {
            createdDate: -1
        };
        const match: any &#x3D; {};
        const agg &#x3D; [];
        match.$and &#x3D; [];

        match.$and.push({lifeCycleStatus: { $ne: LifeCycleStatusEnum.REMOVED }});

        if (!user.roles.includes(PermissionConst.LEAD_REPORT_VIEW_ALL_CARE)) {
            if (user &amp;&amp; user.id) {
                if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_EMP_CARE)) {
                    const emp &#x3D; await this.employeeRepository.findOne({id: user.id});
                    match.$and.push({processBy: {$nin: [null, &#x27;&#x27;]}}, {processBy: {$in: emp.staffIds}});
                } else if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_IMPORTER_CARE)) {
                    match.$and.push({&#x27;importedBy.id&#x27;: user.id});
                } else {
                    return [];
                }
            } else {
               return [];
            }
        }

        if (!_.isEmpty(query.resource)) {
            match.source &#x3D; query.resource;
        } else {
            match.source &#x3D; {
                $nin: [
                    SourceEnum.DangTin,
                    SourceEnum.YeuCau
            ]};
        }

        if (!_.isEmpty(query.type)) {
            match.type &#x3D; { $in: query.type.split(&#x27;,&#x27;) };
        }
        if (!_.isEmpty(query.posId)) {
            match[&#x27;pos.id&#x27;] &#x3D; query.posId;
        }

        if (!_.isEmpty(query.exchangeId)) {
            match.$and.push({$or: [
                {&#x27;pos.id&#x27;: query.exchangeId},
                {&#x27;pos.parentId&#x27;: query.exchangeId}
            ]});
        }

        if (!_.isEmpty(query.assignedDateFrom)) {
            match.$and.push({assignedDate: {$gte: new Date(parseInt(query.assignedDateFrom, 10))}});
        }

        if (!_.isEmpty(query.assignedDateTo)) {
            match.$and.push({assignedDate: {$lte: new Date(parseInt(query.assignedDateTo, 10))}});
        }

        if (!_.isEmpty(query[&#x27;createdFrom&#x27;]) || !_.isEmpty(query[&#x27;createdTo&#x27;])) {
            match[&#x27;createdDate&#x27;] &#x3D; {};
            if (!_.isEmpty(query[&#x27;createdFrom&#x27;])) {
                match[&#x27;createdDate&#x27;][&#x27;$gte&#x27;] &#x3D; new Date(parseInt(query[&#x27;createdFrom&#x27;]));
            }
            if (!_.isEmpty(query[&#x27;createdTo&#x27;])) {
                match[&#x27;createdDate&#x27;][&#x27;$lte&#x27;] &#x3D; new Date(parseInt(query[&#x27;createdTo&#x27;]));
            }
        }
        if (!_.isEmpty(query.lifeCycleStatus)) {
            match.lifeCycleStatus &#x3D; { $in: query.lifeCycleStatus.split(&#x27;,&#x27;) };
        } else {
            agg.push(
                {
                    $lookup: {
                        from: &#x27;employees&#x27;,
                        localField: &#x27;processBy&#x27;,
                        foreignField: &#x27;id&#x27;,
                        as: &#x27;employeeTakeCare&#x27;
                    }
                });
        }
        let matchKeywords: any &#x3D; {};
        if (!_.isEmpty(query.q)) {
            matchKeywords[&#x27;$or&#x27;] &#x3D; [
                { $and: [{ &#x27;name&#x27;: { $regex: query.q, $options: &#x27;i&#x27; } }, { $or: [{ type: &#x27;BUY&#x27; }, { type: &#x27;RENT&#x27; }] }] },
                { &#x27;pos.name&#x27;: { $regex: query.q, $options: &#x27;i&#x27; } },
                { $and: [{ &#x27;property.address&#x27;: { $regex: query.q, $options: &#x27;i&#x27; } }, { $or: [{ type: &#x27;SELL&#x27; }, { type: &#x27;LEASE&#x27; }] }] },
                { $and: [{ &#x27;category.name&#x27;: { $regex: query.q, $options: &#x27;i&#x27; } }, { $or: [{ type: &#x27;SELL&#x27; }, { type: &#x27;LEASE&#x27; }] }] },
            ];
        }
        if (!_.isEmpty(query.sort)) {
            sort &#x3D; this.transformSort(query.sort) || {
                createdDate: -1
            };
        }
        
        agg.push({
            $addFields: {
                callHistoryCount: {
                    $cond: [{ $isArray: &#x27;$callHistory&#x27; }, {
                        $size: {
                            $filter: {
                                input: &quot;$callHistory&quot;,
                                as: &quot;e&quot;,
                                cond: { $eq: [&quot;$$e.isCalled&quot;, true] }
                            }
                        }
                    }, 0]
                },
                callHistoryMinuteCount: {
                    $cond: [{ $isArray: &#x27;$callHistory&#x27; }, {
                        $sum: &quot;$callHistory.answerTime&quot;
                    }, 0]
                }
            }
        });
        agg.push({
            $match: match
        },
        {
            $match: matchKeywords
        },
        {
            $sort: sort
        },
        {
            $facet: {
                rows: [{ $skip: Math.floor((pageSize * page) - pageSize) }, { $limit: pageSize }],
                totalCount: [
                    {
                        $count: &#x27;count&#x27;
                    }
                ]
            }
        });

        return await this.readModel.aggregate(agg).allowDiskUse(true)
            .exec();
    }
    /**
     * Lấy danh sách yêu cầu tư vấn dịch vụ
     * @param page
     * @param pageSize
     * @param query
     */
    async listAllAdvising(page: number &#x3D; 1, pageSize: number &#x3D; 10, query): Promise&lt;ILeadCareDocument[]&gt; {
        let sort: any &#x3D; {
            createdDate: - 1
        };
        if (!_.isEmpty(query.sort)) {
            sort &#x3D; this.transformSort(query.sort) || {
                createdDate: - 1
            };
        }
        return await this.readModel.aggregate([
            {
                $lookup: {
                    from: &#x27;employees&#x27;,
                    localField: &#x27;processBy&#x27;,
                    foreignField: &#x27;id&#x27;,
                    as: &#x27;employeeTakeCare&#x27;
                }
            },
            {
                $match: {
                    type: CommonConst.TYPE.ADVISING
                }
            },
            { $sort : sort },
            { $skip: (page * pageSize) - pageSize },
            { $limit: pageSize }
        ])
            .allowDiskUse(true)
            .exec();
    }

    /**
     *
     * @param {Array&lt;String&gt;} customerId
     */
    findLeadCareByCustomerId(customerId: string[] &#x3D; [], mapping: boolean &#x3D; false) {
        return this.readModel.find({ customerId: { $in: customerId, $exists: true, $nin: [null, &#x27;&#x27;] } }).exec()
            .then((res) &#x3D;&gt; {
                if (mapping) {
                    return _.groupBy(res, &#x27;customerId&#x27;);
                }
                return res;
            });
    }
    protected transformSort(paramSort?: String) {
        let sort: any &#x3D; paramSort;
        if (_.isString(sort)) {
            sort &#x3D; sort.split(&#x27;,&#x27;);
        }
        if (Array.isArray(sort)) {
            let sortObj &#x3D; {};
            sort.forEach(s &#x3D;&gt; {
                if (s.startsWith(&#x27;-&#x27;))
                    sortObj[s.slice(1)] &#x3D; -1;
                else
                    sortObj[s] &#x3D; 1;
            });
            return sortObj;
        }

        return sort;
    }
    async filter(query: any &#x3D; {}, isPaging: boolean &#x3D; false) {
        const limit &#x3D; parseInt(query[&#x27;pageSize&#x27;]) || 10;
        const skip &#x3D; (parseInt(query[&#x27;page&#x27;]) || 1) * limit - limit;
        const { page, pageSize, ...q } &#x3D; query;
        let sort: any &#x3D; {
            createdDate: -1
        };
        if (!_.isEmpty(query.sort)) {
            sort &#x3D; this.transformSort(query.sort);
        }
        const aggregate &#x3D; [
            {
                $match: q
            },
            {
                $lookup: {
                    from: &#x27;customers&#x27;,
                    localField: &#x27;customerId&#x27;,    // field in the orders collection
                    foreignField: &#x27;id&#x27;,  // field in the items collection
                    as: &#x27;customers&#x27;
                }
            },
            {
                $lookup: {
                    from: &#x27;employees&#x27;,
                    localField: &#x27;processBy&#x27;,    // field in the orders collection
                    foreignField: &#x27;id&#x27;,  // field in the items collection
                    as: &#x27;employees&#x27;
                }
            },
            {
                $addFields: {
                    customer: {
                        $ifNull: [&#x27;$customer&#x27;, { $arrayElemAt: [&#x27;$customers&#x27;, 0] }]
                    },
                    employeeTakeCare: { $arrayElemAt: [&#x27;$employees&#x27;, 0] }
                }
            },
            {
                $project: {
                    customers: 0,
                    employees: 0
                }
            }
        ];
        if (isPaging) {
            return await this.readModel.aggregate(aggregate)
                .sort(sort)
                .skip(skip)
                .limit(limit)
                .exec();
        } else {
            return await this.readModel.aggregate(aggregate)
                .sort(sort)
                .exec();
        }
    }
    async filterReport(user: any &#x3D; {}, query: any &#x3D; {}, posInPool: any[] &#x3D; [], isPaging: boolean &#x3D; false) {
        const limit &#x3D; parseInt(query[&#x27;pageSize&#x27;]) || 10;
        const skip &#x3D; (parseInt(query[&#x27;page&#x27;]) || 1) * limit - limit;
        const { page, pageSize, sort &#x3D; &#x27;&#x27;, ...q } &#x3D; query;
        let sortObject: any &#x3D; {
            createdDate: -1
        };
        if (!_.isEmpty(query.sort)) {
            sortObject &#x3D; this.transformSort(query.sort);
        }
        const aggregate &#x3D; [
            {
                $match: {
                    type: { $in: [TransactionTypeEnum.buy, TransactionTypeEnum.lease, TransactionTypeEnum.rent, TransactionTypeEnum.sell] },
                    lifeCycleStatus: { $in: [LifeCycleStatusEnum.ASSIGNED, LifeCycleStatusEnum.PROCESSING], },
                    processBy: { $exists: true, $nin: [null, &#x27;&#x27;] }
                }
            },
            {
                $lookup: {
                    from: &#x27;customers&#x27;,
                    let: { customerId: &#x27;$customerId&#x27; },
                    pipeline: [
                        {
                            $match: {
                                $expr:
                                {
                                    $and:
                                        [
                                            { $eq: [&#x27;$$customerId&#x27;, &#x27;$id&#x27;] },
                                        ]
                                }


                            }
                        },
                        {
                            $addFields: {
                                &#x27;personalInfo.phone&#x27;: {
                                    $cond: { if: { $ne: [&#x27;$modifiedBy&#x27;, user.id] }, then: &#x27;******&#x27;, else: &#x27;$personalInfo.phone&#x27; },
                                },
                                &#x27;phone&#x27;: {
                                    $cond: { if: { $ne: [&#x27;$modifiedBy&#x27;, user.id] }, then: &#x27;******&#x27;, else: &#x27;$phone&#x27; },
                                },
                            }
                        }
                    ],
                    as: &#x27;customers&#x27;
                }
            },
            {
                $lookup: {
                    from: &#x27;employees&#x27;,
                    let: { employeeId: &#x27;$processBy&#x27; },
                    pipeline: [
                        {
                            $match: {
                                $expr:
                                {
                                    $and:
                                        [
                                            { $in: [&#x27;$$employeeId&#x27;, &#x27;$staffIds&#x27;] },
                                            { $eq: [user.id, &#x27;$id&#x27;] },
                                        ]
                                }


                            }
                        },
                    ],
                    as: &#x27;employeeTakeCares&#x27;
                }
            },
            { $unwind: &#x27;$employeeTakeCares&#x27; },
            {
                $lookup: {
                    from: &#x27;employees&#x27;,
                    let: { employeeId: &#x27;$processBy&#x27; },
                    pipeline: [
                        {
                            $match: {
                                $expr:
                                {
                                    $and:
                                        [
                                            { $eq: [&#x27;$$employeeId&#x27;, &#x27;$id&#x27;] },
                                        ]
                                }


                            }
                        },
                    ],
                    as: &#x27;employeeTakeCare&#x27;
                }
            },
            { $unwind: &#x27;$employeeTakeCare&#x27; },
            {
                $addFields: {
                    customer: {
                        $ifNull: [&#x27;$customer&#x27;, { $arrayElemAt: [&#x27;$customers&#x27;, 0] }]
                    },
                }
            },
            {
                $project: {
                    customers: 0,
                    employees: 0,
                    phone: 0,
                    employeeTakeCaress: 0,
                    employeeTakeCares: 0
                }
            },
            {
                $match: q
            },
        ];
        if (isPaging) {
            return await this.readModel.aggregate(aggregate)
                .sort(sortObject)
                .skip(skip)
                .limit(limit)
                .allowDiskUse(true)
                .exec();
        } else {
            return await this.readModel.aggregate(aggregate)
                .sort(sortObject)
                .allowDiskUse(true)
                .exec();
        }
    }

    countReport(user: any &#x3D; {}, query: any &#x3D; {}, posInPool: string[] &#x3D; [], ) {
        const { page, pageSize, ...q } &#x3D; query;
        const aggregate &#x3D; [
            {
                $match: {
                    type: { $in: [TransactionTypeEnum.buy, TransactionTypeEnum.lease, TransactionTypeEnum.rent, TransactionTypeEnum.sell] },
                    pos: { $exists: true, $nin: [null, &#x27;&#x27;] },
                    $or: [{
                        lifeCycleStatus: { $in: [LifeCycleStatusEnum.ASSIGNED, LifeCycleStatusEnum.PROCESSING] },
                        processBy: { $exists: true, $nin: [null, &#x27;&#x27;] }
                    }, {
                        &#x27;lifeCycleStatus&#x27;: LifeCycleStatusEnum.IN_POOL,
                        &#x27;pos.id&#x27;: { $exists: true, $nin: [null, &#x27;&#x27;], $in: posInPool }
                    }]

                }
            },
            {
                $lookup: {
                    from: &#x27;customers&#x27;,
                    let: { customerId: &#x27;$customerId&#x27; },
                    pipeline: [
                        {
                            $match: {
                                $expr:
                                {
                                    $and:
                                        [
                                            { $eq: [&#x27;$$customerId&#x27;, &#x27;$id&#x27;] },
                                        ]
                                }


                            }
                        },
                    ],
                    as: &#x27;customers&#x27;
                }
            },
            {
                $lookup: {
                    from: &#x27;employees&#x27;,
                    let: { employeeId: &#x27;$processBy&#x27;, posId: &#x27;$pos.id&#x27; },
                    pipeline: [
                        {
                            $match: {
                                $expr:
                                {
                                    $and:
                                        [
                                            { $in: [&#x27;$$employeeId&#x27;, &#x27;$staffIds&#x27;] },
                                            { $eq: [user.id, &#x27;$id&#x27;] },
                                        ]
                                }


                            }
                        },
                    ],
                    as: &#x27;employeeTakeCares&#x27;
                }
            },
            { $unwind: &#x27;$employeeTakeCares&#x27; },
            {
                $lookup: {
                    from: &#x27;employees&#x27;,
                    let: { employeeId: &#x27;$processBy&#x27; },
                    pipeline: [
                        {
                            $match: {
                                $expr:
                                {
                                    $and:
                                        [
                                            { $eq: [&#x27;$$employeeId&#x27;, &#x27;$id&#x27;] },
                                        ]
                                }


                            }
                        },
                    ],
                    as: &#x27;employeeTakeCare&#x27;
                }
            },
            { $unwind: &#x27;$employeeTakeCare&#x27; },
            {
                $addFields: {
                    customer: {
                        $ifNull: [&#x27;$customer&#x27;, { $arrayElemAt: [&#x27;$customers&#x27;, 0] }]
                    },
                }
            },
            {
                $project: {
                    customers: 0,
                    employees: 0,
                    phone: 0,
                }
            },
            {
                $match: q
            },
        ];
        return this.readModel.aggregate(aggregate)
            .allowDiskUse(true)
            .exec();
    }
    countReportInPool(user: any &#x3D; {}, query: any &#x3D; {}, posInPool: string[] &#x3D; []) {
        const { page, pageSize, ...q } &#x3D; query;
        const aggregate &#x3D; [
            {
                $match: {
                    type: { $in: [TransactionTypeEnum.buy, TransactionTypeEnum.lease, TransactionTypeEnum.rent, TransactionTypeEnum.sell] },
                    pos: { $exists: true, $nin: [null, &#x27;&#x27;] },
                    $or: [{
                        &#x27;lifeCycleStatus&#x27;: LifeCycleStatusEnum.IN_POOL,
                        &#x27;pos.id&#x27;: { $exists: true, $nin: [null, &#x27;&#x27;], $in: posInPool }
                    }]

                }
            },
            {
                $lookup: {
                    from: &#x27;customers&#x27;,
                    let: { customerId: &#x27;$customerId&#x27; },
                    pipeline: [
                        {
                            $match: {
                                $expr:
                                {
                                    $and:
                                        [
                                            { $eq: [&#x27;$$customerId&#x27;, &#x27;$id&#x27;] },
                                        ]
                                }


                            }
                        },
                    ],
                    as: &#x27;customers&#x27;
                }
            },
            {
                $project: {
                    customers: 0,
                    employees: 0,
                    phone: 0,
                }
            },
            {
                $match: q
            },
        ];
        return this.readModel.aggregate(aggregate)
            .allowDiskUse(true)
            .exec();
    }
    async countLeadCareReadyForAssigningByPOS(posId: string, type: string) {
        const match: any &#x3D; {
            &#x27;processBy&#x27;: null,
            &#x27;pos.id&#x27;: posId,
            &#x27;lifeCycleStatus&#x27;: LifeCycleStatusEnum.IN_POOL,
        };
        if (type) {
            match.type &#x3D; type;
        } else {
            match.type &#x3D; { $ne: CommonConst.TYPE.ADVISING };
        }
        return await this.readModel.find(match)
            .countDocuments()
            .then(rs &#x3D;&gt; {
                return rs;
            })
            .catch(ex &#x3D;&gt; {
                return ex;
            });
    }

    async countLeadCareReadyForAssigningByEmp(employee: any, type: string) {
        const match: any &#x3D; {
            processBy: null,
            $or: [{&#x27;pos.id&#x27;: employee.pos.id},
                    {&#x27;pos.id&#x27;: employee.pos.parentId}
                ],
            lifeCycleStatus: LifeCycleStatusEnum.IN_POOL,
        };
        if (employee.pos.taxNumber &#x3D;&#x3D;&#x3D; &#x27;3602545493&#x27;) { // Hard code DXS Tax No to get leadCare from shared pool
            match.$or.push({&#x27;pos.id&#x27;: &#x27;dxs-shared&#x27;});
        }
        if (type) {
            match.type &#x3D; type;
        } else {
            match.type &#x3D; { $ne: CommonConst.TYPE.ADVISING };
        }
        return await this.readModel.find(match)
            .countDocuments()
            .then(rs &#x3D;&gt; {
                return rs;
            })
            .catch(ex &#x3D;&gt; {
                return ex;
            });
    }

    findLeadCareByEmployeeId(employeeId: string[] &#x3D; []) {
        return this.readModel.find({
            processBy: { $in: employeeId, $exists: true, $nin: [null, &#x27;&#x27;] }, type: { $in: [TransactionTypeEnum.buy, TransactionTypeEnum.lease, TransactionTypeEnum.rent, TransactionTypeEnum.sell] },
        }).exec();
    }

    async countPrimaryLeadCare(user: any, query: any) {
        const page: number &#x3D; parseInt(query[&#x27;page&#x27;]) || 1;
        const pageSize: number &#x3D; parseInt(query[&#x27;pageSize&#x27;]) || 10;
        const aggregate: any &#x3D; [];
        const match: any &#x3D; {};

        match.$and &#x3D; [];
        match.$and.push({ type: CommonConst.TYPE.PRIMARY });

        if (!user.roles.includes(PermissionConst.LEAD_REPORT_VIEW_ALL_CARE)) {
            if (user &amp;&amp; user.id) {
                if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_EMP_CARE)) {
                    const emp &#x3D; await this.employeeRepository.findOne({id: user.id});
                    match.$and.push({processBy: {$nin: [null, &#x27;&#x27;]}}, {processBy: {$in: emp.staffIds}});
                } else if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_IMPORTER_CARE)) {
                    match.$and.push({&#x27;importedBy.id&#x27;: user.id});
                } else {
                    return [];
                }
            } else {
               return [];
            }
        }

        if (!_.isEmpty(query.posId)) {
            match[&#x27;pos.id&#x27;] &#x3D; query.posId;
        } else if (!_.isEmpty(query.exchangeId)) {
            match.$and.push({$or: [
                {&#x27;pos.id&#x27;: query.exchangeId},
                {&#x27;pos.parentId&#x27;: query.exchangeId}
            ]});
        }

        if (!_.isEmpty(query.assignedDateFrom)) {
            match.$and.push({assignedDate: {$gte: new Date(parseInt(query.assignedDateFrom, 10))}});
        }

        if (!_.isEmpty(query.assignedDateTo)) {
            match.$and.push({assignedDate: {$lte: new Date(parseInt(query.assignedDateTo, 10))}});
        }

        if (!_.isEmpty(query.resource)) {
            match.source &#x3D; query.resource;
        } else {
            match.source &#x3D; {
                $nin: [
                    SourceEnum.DangTin,
                    SourceEnum.YeuCau
            ]};
        }

        if (!_.isEmpty(query[&#x27;createdFrom&#x27;]) || !_.isEmpty(query[&#x27;createdTo&#x27;])) {
            match[&#x27;createdDate&#x27;] &#x3D; {};
            if (!_.isEmpty(query[&#x27;createdFrom&#x27;])) {
                match[&#x27;createdDate&#x27;][&#x27;$gte&#x27;] &#x3D; new Date(parseInt(query[&#x27;createdFrom&#x27;]));
            }
            if (!_.isEmpty(query[&#x27;createdTo&#x27;])) {
                match[&#x27;createdDate&#x27;][&#x27;$lte&#x27;] &#x3D; new Date(parseInt(query[&#x27;createdTo&#x27;]));
            }
        }
        aggregate.push({
            $match: match
        });
        aggregate.push({
            $addFields: {
                callHistoryCount: {
                    $cond: [{ $isArray: &#x27;$callHistory&#x27; }, {
                        $size: {
                            $filter: {
                                input: &quot;$callHistory&quot;,
                                as: &quot;e&quot;,
                                cond: { $eq: [&quot;$$e.isCalled&quot;, true] }
                            }
                        }
                    }, 0]
                },
                callHistoryMinuteCount: {
                    $cond: [{ $isArray: &#x27;$callHistory&#x27; }, {
                        $sum: &quot;$callHistory.answerTime&quot;
                    }, 0]
                }
            }
        });
        aggregate.push({
            $group: {
                _id: &#x27;$pos.id&#x27;,
                posName: { $first: &#x27;$pos.name&#x27; },
                countAll: { $sum: 1 },
                countProcessing: { $sum: { $cond: [{ $eq: [&#x27;$lifeCycleStatus&#x27;, &#x27;primary_processing&#x27;] }, 1, 0] } },
                countRemoved: { $sum: { $cond: [{ $eq: [&#x27;$lifeCycleStatus&#x27;, &#x27;removed&#x27;] }, 1, 0] } },
                countInValid: {
                    $sum: {
                        $cond: [{
                            $and: [
                                { $eq: [&#x27;$lifeCycleStatus&#x27;, &#x27;primary_processing&#x27;] },
                                { $eq: [&#x27;$updatedName&#x27;, &#x27;&#x27;] },
                                { $eq: [&#x27;$updatedEmail&#x27;, &#x27;&#x27;] },
                                { $eq: [&#x27;$updatedPhone&#x27;, &#x27;&#x27;] },
                                { $eq: [&#x27;$isInNeed&#x27;, &#x27;&#x27;] },
                                { $eq: [&#x27;$otherReason&#x27;, &#x27;&#x27;] },
                                { $eq: [&#x27;$note&#x27;, &#x27;&#x27;] },
                                { $eq: [&#x27;$needLoan&#x27;, false] },
                                { $eq: [&#x27;$isAppointment&#x27;, false] },
                                { $eq: [&#x27;$isVisited&#x27;, false] },
                            ]
                        }, 1, 0]
                    }
                },
                countCall: {
                    $sum: &#x27;$callHistoryCount&#x27;
                },
                countCallMinute: {
                    $sum: &#x27;$callHistoryMinuteCount&#x27;
                },
                latestUpdate: { $max: &#x27;$updatedDate&#x27; },
            }
        });
        aggregate.push({
            $facet: {
                rows: [{ $skip: Math.floor((pageSize * page) - pageSize) }, { $limit: pageSize }],
                totalCount: [
                    {
                        $count: &#x27;count&#x27;
                    }
                ]
            }
        });

        return this.readModel.aggregate(aggregate).allowDiskUse(true).exec();
    }

    async getLeadCareByStatus(user, status: ExploitCareEnum, query: any, page: number, pageSize: number) {
        const offset &#x3D; (page &#x3D;&#x3D;&#x3D; 1) ? 0 : ((page - 1) * pageSize);

        const conditions: any &#x3D; {
            exploitStatus: (status &#x3D;&#x3D;&#x3D; ExploitCareEnum.ASSIGN) ? { $in: [ ExploitCareEnum.ASSIGN, ExploitCareEnum.REASSIGN ] } : status,
            &#x27;forBQL&#x27;: { $ne: true }
        };
        const employee &#x3D; await this.employeeRepository.findOne({id: user.id});
        if (!user.roles.includes(PermissionConst.LEAD_REPORT_QUERY_ALL_CARE)) {
          if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_PROJECT_CARE)) {
            const projectIds &#x3D; await this.propertyClient
            .sendDataPromise({ id: user.id }, CmdPatternConst.PROJECT.GET_PROJECT_BY_CUSTOMER_SERVICE);
            conditions[&#x27;project.id&#x27;] &#x3D; { $in: projectIds }
          } else if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_EMP_CARE)) {
              conditions[&#x27;takeCare.id&#x27;] &#x3D; { $in: employee.staffIds };
          } else if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_IMPORTER_CARE)) {
              conditions[&#x27;importedBy.id&#x27;] &#x3D; user.id;
          } else {
              return [null, 0];
          }
        } else {
          query[&#x27;importedBy.id&#x27;] &#x3D; { $in: employee?.staffIds || [] };
        }

        if (query.pos) conditions[&#x27;pos.id&#x27;] &#x3D; query.pos;
        if (query.takeCareId) conditions[&#x27;takeCare.id&#x27;] &#x3D; query.takeCareId;

        if (query.startDate || query.endDate) conditions.updatedDate &#x3D; {};
        if (query.startDate) conditions.updatedDate[&#x27;$gte&#x27;] &#x3D; new Date(parseInt(query.startDate));
        if (query.endDate) conditions.updatedDate[&#x27;$lte&#x27;] &#x3D; new Date(parseInt(query.endDate));

        return Promise.all([
            this.readModel
                .find(conditions, { _id: 1, id: 1, code: 1, exploitStatus: 1, pos: 1, takeCare: 1, updatedDate: 1 })
                .sort({ updatedDate: 1 })
                .limit(pageSize)
                .skip(offset)
                .exec(),

                this.readModel.countDocuments(conditions),
            ]);
    }

    async getLeadCaresByStatusForExport(user, status: ExploitCareEnum, query: any) {
        const leadCares &#x3D; [];
        const limit &#x3D; 10;
        let page &#x3D; 1;
        let serial &#x3D; 0;
        
        const conditions: any &#x3D; {
            exploitStatus: (status &#x3D;&#x3D;&#x3D; ExploitCareEnum.ASSIGN) ? { $in: [ ExploitCareEnum.ASSIGN, ExploitCareEnum.REASSIGN ] } : status,
        };
        const employee &#x3D; await this.employeeRepository.findOne({id: user.id});
        if (!user.roles.includes(PermissionConst.LEAD_REPORT_VIEW_ALL_CARE)) {
          if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_PROJECT_CARE)) {
            const projectIds &#x3D; await this.propertyClient
            .sendDataPromise({ id: user.id }, CmdPatternConst.PROJECT.GET_PROJECT_BY_CUSTOMER_SERVICE);
            conditions[&#x27;project.id&#x27;] &#x3D; { $in: projectIds }
          } else if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_EMP_CARE)) {
              conditions[&#x27;takeCare.id&#x27;] &#x3D; { $in: employee.staffIds };
          } else if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_IMPORTER_CARE)) {
              conditions[&#x27;importedBy.id&#x27;] &#x3D; user.id;
          } else {
              return [null, 0];
          }
        } else {
          query[&#x27;importedBy.id&#x27;] &#x3D; { $in: employee?.staffIds || [] };
        }
        if (query.pos) conditions[&#x27;pos.id&#x27;] &#x3D; query.pos;
        if (query.takeCareId) conditions[&#x27;takeCare.id&#x27;] &#x3D; query.takeCareId;

        if (query.startDate || query.endDate) conditions.updatedDate &#x3D; {};
        if (query.startDate) conditions.updatedDate[&#x27;$gte&#x27;] &#x3D; new Date(parseInt(query.startDate));
        if (query.endDate) conditions.updatedDate[&#x27;$lte&#x27;] &#x3D; new Date(parseInt(query.endDate));

        const total &#x3D; await this.readModel.countDocuments(conditions);
        const totalPages &#x3D; Math.ceil(total / limit);

        while (page &lt;&#x3D; totalPages) {
            const offset &#x3D; (page &#x3D;&#x3D;&#x3D; 1) ? 0 : ((page - 1) * limit);

            leadCares.push(
                ...await this.readModel
                    .aggregate([
                        { $match: conditions },
                        {
                            $project: {
                                _id: 0,
                                code: 1,
                                exploitStatus: 1,
                                pos: &#x27;$pos.name&#x27;,
                                takeCare: &#x27;$takeCare.name&#x27;,
                                updatedDate: 1,
                                surveys: 1,
                            }
                        },
                        { $skip: offset },
                        { $limit: limit },
                    ])
                    .allowDiskUse(true)
                    .exec()
            );

            page++;
        }

        return leadCares.map(item &#x3D;&gt; {
            serial++;

            return {
                serial,
                ...item,
            };
        });
    }

    async createMany(records: ILeadCare[]) {
      return Bluebird.map(
          records,
          async (item) &#x3D;&gt; {
              const doc &#x3D; await this.readModel.findById(item.id);
              if (doc) {
                  await doc.updateOne(item);
              } else {
                  await this.readModel.create(item);
              }
          },
          { concurrency: 30 }
      );
    }

    async findMany(filter, project &#x3D; {}, sort &#x3D; {}, limit &#x3D; null) {
        return this.readModel.find(filter, project).sort(sort).limit(limit);
    }


    async findAllPrimaryBQL(user, query: any , projectBqlMember:any): Promise&lt;any&gt; {
        const match: any &#x3D; {
            &#x27;forBQL&#x27;: true
        };
        const matchKeywords: any &#x3D; {};
        let sort: any &#x3D; {
            code: 1
        };
        // match.type &#x3D; CommonConst.TYPE.PRIMARY;
        match.$and &#x3D; []
    
        if(query.projectId){
            let usr &#x3D; projectBqlMember.find(item &#x3D;&gt;{
                return item.id &#x3D;&#x3D;&#x3D; user.id
            })

            if(usr){
                if(!user.roles.includes(PermissionConst.LEAD_CARE_GET_BY_ADMIN) &amp;&amp; !user.roles.includes(PermissionConst.LEAD_CARE_BQL_GET_ALL)) {
                    // Lấy của user đó
                    match.$and.push({ &#x27;takeCare.id&#x27;: usr.id });
                } else if (!user.roles.includes(PermissionConst.LEAD_CARE_GET_BY_ADMIN) &amp;&amp; user.roles.includes(PermissionConst.LEAD_CARE_BQL_GET_ALL)) {
                    // Lấy trang thái Ban quản lý + của user đó
                    match.$and.push({$or: [
                        { &#x27;repoType&#x27;: LeadRepoCareEnum.BQL },
                        { &#x27;takeCare.id&#x27;: usr.id }
                    ]});
                }
            } else {
                return []
            }
            match.$and.push({&#x27;project.id&#x27;: query.projectId});
        } else {
            match.$and &#x3D; [{ &#x27;project.id&#x27;: { $in: projectBqlMember } }]
        }

        match.$and.push({type: CommonConst.TYPE.PRIMARY});

        if (query) {
            if (!_.isEmpty(query.query)) {
                matchKeywords.$or &#x3D; [
                    { &#x27;name&#x27;: { $regex: query.query, $options: &#x27;i&#x27; } },
                    { &#x27;phone&#x27;: { $regex: query.query, $options: &#x27;i&#x27; } },
                    { &#x27;code&#x27;: { $regex: query.query, $options: &#x27;i&#x27; } },
                    { &#x27;updatedName&#x27;: { $regex: query.query, $options: &#x27;i&#x27; } },
                    { &#x27;updatedPhone&#x27;: { $regex: query.query, $options: &#x27;i&#x27; } },
                    { &#x27;note&#x27;: { $regex: query.query, $options: &#x27;i&#x27; } },
                ];
            }
            if (!_.isEmpty(query[&#x27;resource&#x27;])) {
                match[&#x27;resource&#x27;] &#x3D; query[&#x27;resource&#x27;];
            }

            if (!_.isEmpty(query.employeeId)) {
                match.$and.push({processBy: query.employeeId});
            } else if (!_.isEmpty(query.posId)) {
                match.$and.push({&#x27;pos.id&#x27;: query.posId});
            } else if (!_.isEmpty(query.exchangeId)) {
                match.$and.push({$or: [
                    {&#x27;pos.id&#x27;: query.exchangeId},
                    {&#x27;pos.parentId&#x27;: query.exchangeId}
                ]});
            }

            if (!_.isEmpty(query.assignedDateFrom)) {
                match.$and.push({assignedDate: {$gte: new Date(parseInt(query.assignedDateFrom, 10))}});
            }

            if (!_.isEmpty(query.assignedDateTo)) {
                match.$and.push({assignedDate: {$lte: new Date(parseInt(query.assignedDateTo, 10))}});
            }

            if (!_.isEmpty(query.sort)) {
                sort &#x3D; this.transformSort(query.sort) || {
                    code: 1
                };
            }

            if(query.exploitStatus) {
                const exploitStatus &#x3D;  query.exploitStatus.split(&quot;,&quot;)
                match.$and.push({exploitStatus :{ $in: exploitStatus }})
            }
            if(query.repoCode) {
              match.$and.push({repoCode : query.repoCode})
            }
            if(query.type) {
              match.$and.push({repoType : query.type})
            }
        }

        const projection &#x3D; {
            &quot;id&quot;:1,
            &quot;createdBy&quot;:1,
            &quot;modifiedDate&quot;:1,
            &quot;modifiedBy&quot;:1,
            &quot;title&quot;:1,
            &quot;customerId&quot;:1,
            &quot;name&quot;:1,
            &quot;address&quot;:1,
            &quot;phone&quot;:1,
            &quot;description&quot;:1,
            &quot;createdDate&quot;:1,
            &quot;updatedDate&quot;:1,
            &quot;email&quot;:1,
            &quot;notes&quot;:1,
            &quot;customer&quot;:1,
            &quot;processedDate&quot;:1,
            &quot;code&quot;:1,
            &quot;assignedDate&quot;:1,
            &quot;importedBy&quot;:1,
            &quot;exploitStatus&quot;:1,
            &quot;takeCare&quot;:1,
            &quot;project&quot;:1,
            &quot;note&quot;:1,
            &quot;repoType&quot;:1,
            &quot;repoCode&quot;:1,
            &quot;customData&quot;:1,
            &quot;dateEndWork&quot;:1,
            &quot;idRepoConfig&quot;:1,
            &quot;nameRepoConfig&quot;:1,
            &quot;rateValue&quot;: 1,
            &quot;rateDescription&quot;: 1,
            &quot;canSurvey&quot;: 1,
            &quot;target&quot;: 1,
            &quot;submitSurvey&quot;: 1,
            &quot;reason&quot;: 1,
            &quot;surveys&quot;: 1,
            &quot;surveyAnswers&quot;: 1,
            &quot;isRequireSurvey&quot;: 1,
        }

        const aggregate: any[] &#x3D; [
            {
                $match: matchKeywords
            },
            {
                $match: match
            },
            {
                $project: projection
            },
            { $sort: sort },
        ]
        if (!_.isEmpty(query[&#x27;page&#x27;]) || !_.isEmpty(query[&#x27;pageSize&#x27;])) {
            const page: number &#x3D; parseInt(query[&#x27;page&#x27;]) || 1;
            const pageSize: number &#x3D; parseInt(query[&#x27;pageSize&#x27;]) || 10;
            aggregate.push({
                $facet: {
                    rows: [{ $skip: Math.floor((pageSize * page) - pageSize) }, { $limit: pageSize }],
                    totalCount: [
                        {
                            $count: &#x27;count&#x27;
                        }
                    ]
                }
            });
        }
        // console.log(JSON.stringify(aggregate));
        return await this.readModel.aggregate(aggregate)
            .allowDiskUse(true)
            .exec();
    }

    async findLeadCareByIdBql(id: string) {
        return await this.readModel.aggregate(
            [
                {
                    $match: { 
                        id: id,
                        forBQL: true
                    }
                },
                {
                    $lookup: {
                        from: &#x27;lead-repo-cares&#x27;,
                        localField: &#x27;repoId&#x27;,
                        foreignField: &#x27;id&#x27;,
                        as: &#x27;configData&#x27;
                    }
                }
            ]).allowDiskUse(true)
            .exec()
            .then((response) &#x3D;&gt; {
                if (response.length &gt; 0) {
                    if  (response[0].configData.length){
                        response[0].configData &#x3D; response[0].configData[0].configs.find((item) &#x3D;&gt; item.code &#x3D;&#x3D;&#x3D; response[0].repoConfigCode);
                    } else {
                        response[0].configData &#x3D; null;
                    }
                    return response[0];
                }
                return null;
            })
            .catch((exp) &#x3D;&gt; {
                return exp;
            });
    }

    async countAll(query: any, actionName &#x3D; &quot;countAll&quot;) {
        this.loggerService.logLocal(
          this.context,
          clc.yellow(&#x60;[${actionName}]&#x60;),
          query
        );
        delete query.isPaging;
        delete query.page;
        delete query.pageSize;
        return await this.readModel.countDocuments(query).exec();
      }

    async saveAll(models: any[]): Promise&lt;any&gt; {
        return await this.readModel.bulkWrite(
          models.map((model) &#x3D;&gt; {
            return {
              updateOne: {
                filter: { id: model._id },   // Detect Update by id
                update: { $set: {
                    rateValue: model.rateValue,
                    rateDescription: model.rateDescription
                }}
              }
            };
          })
        );
    }
}
</code></pre>
    </div>

</div>







                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'QueryRepository-1.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
