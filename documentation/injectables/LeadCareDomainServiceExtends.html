<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-lead documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-lead documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">







<ol class="breadcrumb">
  <li>Injectables</li>
  <li>LeadCareDomainServiceExtends</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/leadCare.domain/service.extend.ts</code>
        </p>




            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Readonly</span>
                                <a href="#context">context</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Readonly</span>
                                <a href="#response">response</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createBQLServiceRequest">createBQLServiceRequest</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createServiceRequest">createServiceRequest</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#deliveredToAll">deliveredToAll</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#deliverNormalLeadCare">deliverNormalLeadCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#executeCommand">executeCommand</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#getExpireTime">getExpireTime</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#importLeadCareFromExcel">importLeadCareFromExcel</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#initQueueData">initQueueData</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#isRepoCodeVaild">isRepoCodeVaild</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#isRepoValid">isRepoValid</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#leadCareDelivery">leadCareDelivery</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#parseQueueData">parseQueueData</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#preProcessBQLServiceRequest">preProcessBQLServiceRequest</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#preProcessLeadCare">preProcessLeadCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#preProcessServiceRequest">preProcessServiceRequest</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#renewNormalLeadCare">renewNormalLeadCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#updateConfigData">updateConfigData</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#updateExploitHistory">updateExploitHistory</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateEmployee">validateEmployee</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#validateExpire">validateExpire</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#validateLeadCare">validateLeadCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateRepo">validateRepo</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section>
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(commandBus: CommandBus, loggerService: <a href="../injectables/MsxLoggerService.html">MsxLoggerService</a>, leadRepoCareQueryRepo: <a href="../injectables/LeadRepoCareQueryRepository.html">LeadRepoCareQueryRepository</a>, employeeRepo: <a href="../injectables/EmployeeQueryRepository.html">EmployeeQueryRepository</a>, leadCareQueryRepo: <a href="../injectables/QueryRepository.html">QueryRepository</a>, genCodeSrv: <a href="../injectables/CodeGenerateService.html">CodeGenerateService</a>, leadRepoCareDomainSrv: <a href="../injectables/LeadRepoCareDomainService.html">LeadRepoCareDomainService</a>, historyImportSrc: <a href="../injectables/HistoryImportService.html">HistoryImportService</a>, primaryContractClient: <a href="../injectables/PrimaryContractClient.html">PrimaryContractClient</a>, careClient: <a href="../injectables/CareClient.html">CareClient</a>, propertyClient: <a href="../injectables/PropertyClient.html">PropertyClient</a>, mailerClient: <a href="../injectables/MailerClient.html">MailerClient</a>, employeeClient: <a href="../injectables/EmployeeClient.html">EmployeeClient</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="52" class="link-to-prism">src/modules/leadCare.domain/service.extend.ts:52</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>commandBus</td>
                                                  
                                                        <td>
                                                                    <code>CommandBus</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>loggerService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/MsxLoggerService.html" target="_self" >MsxLoggerService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>leadRepoCareQueryRepo</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/LeadRepoCareQueryRepository.html" target="_self" >LeadRepoCareQueryRepository</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>employeeRepo</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/EmployeeQueryRepository.html" target="_self" >EmployeeQueryRepository</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>leadCareQueryRepo</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/QueryRepository.html" target="_self" >QueryRepository</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>genCodeSrv</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/CodeGenerateService.html" target="_self" >CodeGenerateService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>leadRepoCareDomainSrv</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/LeadRepoCareDomainService.html" target="_self" >LeadRepoCareDomainService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>historyImportSrc</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/HistoryImportService.html" target="_self" >HistoryImportService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>primaryContractClient</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/PrimaryContractClient.html" target="_self" >PrimaryContractClient</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>careClient</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/CareClient.html" target="_self" >CareClient</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>propertyClient</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/PropertyClient.html" target="_self" >PropertyClient</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>mailerClient</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/MailerClient.html" target="_self" >MailerClient</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>employeeClient</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/EmployeeClient.html" target="_self" >EmployeeClient</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section>
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createBQLServiceRequest"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            createBQLServiceRequest
                        </b>
                        <a href="#createBQLServiceRequest"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createBQLServiceRequest(user, dto: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, timezoneClient?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="330"
                            class="link-to-prism">src/modules/leadCare.domain/service.extend.ts:330</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>timezoneClient</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createServiceRequest"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            createServiceRequest
                        </b>
                        <a href="#createServiceRequest"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createServiceRequest(user, dto: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, timezoneClient?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="220"
                            class="link-to-prism">src/modules/leadCare.domain/service.extend.ts:220</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>timezoneClient</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deliveredToAll"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            deliveredToAll
                        </b>
                        <a href="#deliveredToAll"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deliveredToAll(posQueue: <a href="../classes/Queue.html">Queue<PosQueueData></a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="631"
                            class="link-to-prism">src/modules/leadCare.domain/service.extend.ts:631</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>posQueue</td>
                                    <td>
                                                <code><a href="../classes/Queue.html" target="_self" >Queue&lt;PosQueueData&gt;</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deliverNormalLeadCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            deliverNormalLeadCare
                        </b>
                        <a href="#deliverNormalLeadCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deliverNormalLeadCare(recordNumber)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="399"
                            class="link-to-prism">src/modules/leadCare.domain/service.extend.ts:399</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>recordNumber</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="executeCommand"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            executeCommand
                        </b>
                        <a href="#executeCommand"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>executeCommand(action: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, commandId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, item: CommandModel | CommandModel[], additionalData?: T)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="757"
                            class="link-to-prism">src/modules/leadCare.domain/service.extend.ts:757</a></div>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Type parameters :</b>
                    <ul class="type-parameters">
                        <li>T</li>
                    </ul>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>action</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>commandId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>item</td>
                                    <td>
                                            <code>CommandModel | CommandModel[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>additionalData</td>
                                    <td>
                                            <code>T</code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getExpireTime"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            getExpireTime
                        </b>
                        <a href="#getExpireTime"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getExpireTime(start: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date" target="_blank">Date</a>, duration: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="604"
                            class="link-to-prism">src/modules/leadCare.domain/service.extend.ts:604</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>start</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date" target="_blank" >Date</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>duration</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="importLeadCareFromExcel"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            importLeadCareFromExcel
                        </b>
                        <a href="#importLeadCareFromExcel"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>importLeadCareFromExcel(files: any[], options: <a href="../classes/ImportLeadCareAsExcelDto.html">ImportLeadCareAsExcelDto</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, timezoneClient?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user?: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="70"
                            class="link-to-prism">src/modules/leadCare.domain/service.extend.ts:70</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>files</td>
                                    <td>
                                            <code>any[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>options</td>
                                    <td>
                                                <code><a href="../classes/ImportLeadCareAsExcelDto.html" target="_self" >ImportLeadCareAsExcelDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>timezoneClient</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="initQueueData"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            initQueueData
                        </b>
                        <a href="#initQueueData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>initQueueData(config: <a href="../classes/LeadRepoCareConfig.html">LeadRepoCareConfig</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="637"
                            class="link-to-prism">src/modules/leadCare.domain/service.extend.ts:637</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>config</td>
                                    <td>
                                                <code><a href="../classes/LeadRepoCareConfig.html" target="_self" >LeadRepoCareConfig</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;Map&lt;string, any[]&gt;&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isRepoCodeVaild"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            isRepoCodeVaild
                        </b>
                        <a href="#isRepoCodeVaild"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>isRepoCodeVaild(repoCode: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, projectId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="927"
                            class="link-to-prism">src/modules/leadCare.domain/service.extend.ts:927</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>repoCode</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>projectId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isRepoValid"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            isRepoValid
                        </b>
                        <a href="#isRepoValid"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>isRepoValid(type: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, projectId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, target: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="693"
                            class="link-to-prism">src/modules/leadCare.domain/service.extend.ts:693</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>type</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>projectId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>target</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="leadCareDelivery"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            leadCareDelivery
                        </b>
                        <a href="#leadCareDelivery"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>leadCareDelivery(data: ILeadCareDocument[], config: <a href="../classes/LeadRepoCareConfig.html">LeadRepoCareConfig</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="522"
                            class="link-to-prism">src/modules/leadCare.domain/service.extend.ts:522</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>data</td>
                                    <td>
                                            <code>ILeadCareDocument[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>config</td>
                                    <td>
                                                <code><a href="../classes/LeadRepoCareConfig.html" target="_self" >LeadRepoCareConfig</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="parseQueueData"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            parseQueueData
                        </b>
                        <a href="#parseQueueData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>parseQueueData(posQueue: <a href="../classes/Queue.html">Queue<PosQueueData></a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="624"
                            class="link-to-prism">src/modules/leadCare.domain/service.extend.ts:624</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>posQueue</td>
                                    <td>
                                                <code><a href="../classes/Queue.html" target="_self" >Queue&lt;PosQueueData&gt;</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="preProcessBQLServiceRequest"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            preProcessBQLServiceRequest
                        </b>
                        <a href="#preProcessBQLServiceRequest"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>preProcessBQLServiceRequest(leadCare, timezoneClient, source)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="889"
                            class="link-to-prism">src/modules/leadCare.domain/service.extend.ts:889</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>leadCare</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>timezoneClient</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>source</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="preProcessLeadCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            preProcessLeadCare
                        </b>
                        <a href="#preProcessLeadCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>preProcessLeadCare(leadCare, repository: <a href="../classes/LeadRepoCare.html">LeadRepoCare</a>, config: <a href="../classes/LeadRepoCareConfig.html">LeadRepoCareConfig</a>, user, timezoneClient, source)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="823"
                            class="link-to-prism">src/modules/leadCare.domain/service.extend.ts:823</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>leadCare</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>repository</td>
                                    <td>
                                                <code><a href="../classes/LeadRepoCare.html" target="_self" >LeadRepoCare</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>config</td>
                                    <td>
                                                <code><a href="../classes/LeadRepoCareConfig.html" target="_self" >LeadRepoCareConfig</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>timezoneClient</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>source</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="preProcessServiceRequest"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            preProcessServiceRequest
                        </b>
                        <a href="#preProcessServiceRequest"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>preProcessServiceRequest(leadCare, repository: <a href="../classes/LeadRepoCare.html">LeadRepoCare</a>, config: <a href="../classes/LeadRepoCareConfig.html">LeadRepoCareConfig</a>, user, timezoneClient, source)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="857"
                            class="link-to-prism">src/modules/leadCare.domain/service.extend.ts:857</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>leadCare</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>repository</td>
                                    <td>
                                                <code><a href="../classes/LeadRepoCare.html" target="_self" >LeadRepoCare</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>config</td>
                                    <td>
                                                <code><a href="../classes/LeadRepoCareConfig.html" target="_self" >LeadRepoCareConfig</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>timezoneClient</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>source</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="renewNormalLeadCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            renewNormalLeadCare
                        </b>
                        <a href="#renewNormalLeadCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>renewNormalLeadCare(recordNumber)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="465"
                            class="link-to-prism">src/modules/leadCare.domain/service.extend.ts:465</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>recordNumber</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateConfigData"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            updateConfigData
                        </b>
                        <a href="#updateConfigData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateConfigData(src: LeadRepoCareConfig[], code: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, config: <a href="../classes/LeadRepoCareConfig.html">LeadRepoCareConfig</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="917"
                            class="link-to-prism">src/modules/leadCare.domain/service.extend.ts:917</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>src</td>
                                    <td>
                                            <code>LeadRepoCareConfig[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>code</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>config</td>
                                    <td>
                                                <code><a href="../classes/LeadRepoCareConfig.html" target="_self" >LeadRepoCareConfig</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateExploitHistory"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            updateExploitHistory
                        </b>
                        <a href="#updateExploitHistory"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateExploitHistory(history: IExploitHistory[], status: <a href="../undefineds/ExploitCareEnum.html">ExploitCareEnum</a>, takeCareId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="779"
                            class="link-to-prism">src/modules/leadCare.domain/service.extend.ts:779</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>history</td>
                                    <td>
                                            <code>IExploitHistory[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>[]</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>status</td>
                                    <td>
                                                <code><a href="../miscellaneous/enumerations.html#ExploitCareEnum" target="_self" >ExploitCareEnum</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>takeCareId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{ history: {}; newHistory: any; }</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateEmployee"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            validateEmployee
                        </b>
                        <a href="#validateEmployee"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateEmployee(employee, orgChartGroup: Map<string | string[]>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="804"
                            class="link-to-prism">src/modules/leadCare.domain/service.extend.ts:804</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>employee</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>orgChartGroup</td>
                                    <td>
                                            <code>Map&lt;string | string[]&gt;</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateExpire"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            validateExpire
                        </b>
                        <a href="#validateExpire"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateExpire(history: IExploitHistory[], duration: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="607"
                            class="link-to-prism">src/modules/leadCare.domain/service.extend.ts:607</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>history</td>
                                    <td>
                                            <code>IExploitHistory[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>duration</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateLeadCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            validateLeadCare
                        </b>
                        <a href="#validateLeadCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateLeadCare(leadCare)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="794"
                            class="link-to-prism">src/modules/leadCare.domain/service.extend.ts:794</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>leadCare</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>&quot;Số điện thoại sai&quot; | &quot;Email sai&quot; | &quot;&quot;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateRepo"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            validateRepo
                        </b>
                        <a href="#validateRepo"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateRepo(repoId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, repoConfigCode: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="666"
                            class="link-to-prism">src/modules/leadCare.domain/service.extend.ts:666</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>repoId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>repoConfigCode</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>
            <section>
    
        <h3 id="inputs">
            Properties
        </h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="context"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Private</span>
                                <span class="modifier">Readonly</span>
                            context</b>
                            <a href="#context"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>LeadCareDomainServiceExtends.name</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="51" class="link-to-prism">src/modules/leadCare.domain/service.extend.ts:51</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="response"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Private</span>
                                <span class="modifier">Readonly</span>
                            response</b>
                            <a href="#response"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{ success: true }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="52" class="link-to-prism">src/modules/leadCare.domain/service.extend.ts:52</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {BadRequestException, Injectable, UnauthorizedException} from &#x27;@nestjs/common&#x27;;
import { CommandBus } from &#x27;@nestjs/cqrs&#x27;;
import * as Bluebird from &#x27;bluebird&#x27;;
import { flatMapDeep, isEmpty, maxBy } from &#x27;lodash&#x27;;
import { isNullOrUndefined } from &#x27;util&#x27;;
import uuid &#x3D; require(&#x27;uuid&#x27;);
import { CodeGenerateService } from &#x27;../code-generate/service&#x27;;
import { EmployeeQueryRepository } from &#x27;../employee/repository/query.repository&#x27;;
import { HistoryImportService } from &#x27;../history-import/service&#x27;;
import { ILeadCareDocument } from &#x27;../leadCare.queryside/interfaces/document.interface&#x27;;
import { QueryRepository } from &#x27;../leadCare.queryside/repository/query.repository&#x27;;
import { LeadRepoCareDomainService } from &#x27;../leadRepoCare.domain/service&#x27;;
import { LeadRepoCareQueryRepository } from &#x27;../leadRepoCare.queryside/repositories/query.repository&#x27;;
import { MsxLoggerService } from &#x27;../logger/logger.service&#x27;;
import { Queue } from &#x27;../shared/classes/class-queue&#x27;;
import { CommonConst } from &#x27;../shared/constant/common.const&#x27;;
import { ErrorConst } from &#x27;../shared/constant/error.const&#x27;;
import { Action } from &#x27;../shared/enum/action.enum&#x27;;
import { ExploitCareEnum } from &#x27;../shared/enum/exploit.enum&#x27;;
import { LifeCycleStatusEnum } from &#x27;../shared/enum/life-cycle-status.enum&#x27;;
import {CareSourceEnum, SourceEnum} from &#x27;../shared/enum/source.enum&#x27;;
import { StatusEnum } from &#x27;../shared/enum/status.enum&#x27;;
import { CommandModel } from &#x27;../shared/eventStream/models/command.model&#x27;;
import {
  LeadRepoCare,
    LeadRepoCareConfig,
} from &#x27;../shared/models/leadRepoCare/model&#x27;;
import {
    ILeadCare, IExploitHistory
} from &#x27;../shared/services/leadCare/interfaces/leadCare.interface&#x27;;
import { ImportLeadCareCommand } from &#x27;./commands/impl/importLead.cmd&#x27;;
import {CreateServiceRequestDto, ImportLeadCareAsExcelDto} from &#x27;./dto/leadCare.dto&#x27;;
import {LeadRepoCareEnum} from &quot;../shared/enum/type.enum&quot;;
import {CmdPatternConst} from &quot;../shared/constant/cmd-pattern.const&quot;;
import {PrimaryContractClient} from &quot;../mgs-sender/primary-contract.client&quot;;
import {CareClient} from &quot;../mgs-sender/care.client&quot;;
import {VerifyAccountStatusEnum} from &quot;../shared/enum/care-customer.enum&quot;;
const XLSX &#x3D; require(&#x27;xlsx&#x27;);
import * as moment from &#x27;moment&#x27;;
import {PropertyClient} from &quot;../mgs-sender/property.client&quot;;
import { MailerClient } from &#x27;../mgs-sender/mailer.client&#x27;;
import { EmployeeClient } from &#x27;../mgs-sender/employee.client&#x27;;

interface PosQueueData {
    id: string;
    employeeQueue: Queue&lt;Object&gt;;
}

@Injectable()
export class LeadCareDomainServiceExtends {
    private readonly context &#x3D; LeadCareDomainServiceExtends.name;
    private readonly response &#x3D; { success: true };

    constructor(
        private readonly commandBus: CommandBus,
        private readonly loggerService: MsxLoggerService,
        private readonly leadRepoCareQueryRepo: LeadRepoCareQueryRepository,
        private readonly employeeRepo: EmployeeQueryRepository,
        private readonly leadCareQueryRepo: QueryRepository,
        private readonly genCodeSrv: CodeGenerateService,
        private readonly leadRepoCareDomainSrv: LeadRepoCareDomainService, // private readonly notificationClient: NotificationClient
        private readonly historyImportSrc: HistoryImportService,
        private readonly primaryContractClient: PrimaryContractClient,
        private readonly careClient: CareClient,
        private readonly propertyClient: PropertyClient,
        private readonly mailerClient: MailerClient,
        private readonly employeeClient: EmployeeClient,
        ) {}

    async importLeadCareFromExcel(
        files: any[],
        options: ImportLeadCareAsExcelDto,
        actionName: string,
        timezoneClient?: string,
        user?: any
    ) {
        const { repoConfigCode, repoId, source } &#x3D; options;
        const workbook &#x3D; XLSX.read(files[0].buffer, { type: &#x27;buffer&#x27; });
        const sheets &#x3D; workbook.SheetNames;
        const data: any[] &#x3D; await XLSX.utils.sheet_to_json(
            workbook.Sheets[sheets[0]],
            {
                range: 1,
            }
        );

        const { repository, config } &#x3D; await this.validateRepo(repoId, repoConfigCode);

        if (!source) throw new BadRequestException(&#x27;source is required!&#x27;);
        if (!(&lt;any&gt;Object).values(CareSourceEnum).includes(source)) throw new BadRequestException(&#x27;source is not exists!&#x27;);

        const orgChartGroup &#x3D; new Map();
        if (config.orgCharts?.length) {
          config.orgCharts.forEach(item &#x3D;&gt; {
            orgChartGroup.set({ id: item.id, name: item.name }, item.staffIds);
          });
        }

        const hotLeadCares &#x3D; [];
        const normalLeadCares &#x3D; [];
        const failedLeadCares &#x3D; [];
        const assignedLeadCares &#x3D; [];

        await Bluebird.mapSeries(data, async (item, idx) &#x3D;&gt; {
          const validateLeadCare &#x3D; this.validateLeadCare(item);
          if (validateLeadCare) {
              return failedLeadCares.push({ line: idx + 1, error: validateLeadCare });
          }
          const { assignee } &#x3D; item;
          const leadCare &#x3D; await this.preProcessLeadCare(
            item,
            repository,
            config,
            user,
            timezoneClient,
            source,
        );

         

          if (assignee) {
              const employee &#x3D; await this.employeeRepo.findOne({ code: assignee });
              const {error, pos} &#x3D; await this.validateEmployee(employee, orgChartGroup);
              if (error) {
                  return failedLeadCares.push({
                      line: idx + 1,
                      error,
                  });
              }
              const {history, newHistory} &#x3D; this.updateExploitHistory(
                  leadCare.exploitHistory,
                  ExploitCareEnum.ASSIGN,
                  employee.id
              );
              Object.assign(leadCare, {
                  takeCare: {
                      id: employee.id,
                      name: employee.name,
                      email: employee.email,
                      phone: employee.phone,
                  },
                  assignDuration: config.assignDuration,
                  expireTime: this.getExpireTime(
                    newHistory?.updatedAt,
                    config.assignDuration
                  ),
                  exploitStatus: ExploitCareEnum.ASSIGN,
                  exploitHistory: history,
                  pos
              });
              assignedLeadCares.push(leadCare);
          } else {
              Object.assign(leadCare, {
                  takeCare: {},
              });
              if (leadCare.isHot) {
                  hotLeadCares.push(leadCare);
              } else {
                  normalLeadCares.push(leadCare);
              }
          }
        });
                
        const { newConfig, result } &#x3D; await this.leadCareDelivery(
            hotLeadCares,
            repository.configHot as LeadRepoCareConfig
        );

        const records &#x3D; [...normalLeadCares, ...assignedLeadCares];

        if (result.length) {
          const importCommandId &#x3D; uuid.v4();
          await this.executeCommand(
            Action.IMPORT_LEAD_CARE,
            actionName,
            importCommandId,
            result as any[],
            newConfig.notification || {}
          );

          await this.leadRepoCareDomainSrv.updateLeadRepoCareMain(
            {
              id: repository.id,
              name: repository.name,
              type: repository.type
            },
            actionName,
            user?.id,
            timezoneClient
          );
        }

        if (records.length) {
          const importCommandId &#x3D; uuid.v4();
          await this.executeCommand(
            Action.IMPORT_LEAD_CARE,
            actionName,
            importCommandId,
            records,
            config.notification || {}
          );
        }

        const importHistoryRecord &#x3D; {
          fileName: files[0].originalname,
          processBy: user,
          success: [...normalLeadCares, ...hotLeadCares, ...assignedLeadCares].length,
          fail: failedLeadCares.length,
          type: CommonConst.TYPE.PRIMARY,
          createdDate: new Date(),
          updatedDate: new Date(),
          description: failedLeadCares
        };

        await this.historyImportSrc.create(importHistoryRecord);

        return this.response;
    }

    async createServiceRequest(
        user,
        dto: any,
        actionName: string,
        timezoneClient?: string,
    ) {
        //fix DXGAP-967
        /*if (!user) {
          throw new BadRequestException({
            errors: ErrorConst.CommonError(
              ErrorConst.UNAUTHORIZED
            ),
          });
        } else {
          const customer &#x3D; await this.careClient.sendDataPromise({ userId: user.id }, CmdPatternConst.CARE.GET_CUSTOMER_BY_ID);

          if (!customer || customer.verifyAccount !&#x3D;&#x3D; VerifyAccountStatusEnum.APPROVED) {
            throw new UnauthorizedException({ errors: ErrorConst.CommonError(ErrorConst.UNAUTHORIZED) });
          }
        }*/

        if(dto.repoCode){
          const { repository, config } &#x3D; await this.isRepoCodeVaild(dto.repoCode, dto.projectId)
          if(repository.type &#x3D;&#x3D;&#x3D; LeadRepoCareEnum.BQL){
            const nameConfig &#x3D; repository.configs.find(({code}) &#x3D;&gt;code &#x3D;&#x3D;&#x3D; dto.repoCode);
            dto.idRepoConfig &#x3D; nameConfig.id;
            dto.nameRepoConfig &#x3D; nameConfig.name;
            dto.canSurvey &#x3D; nameConfig.canSurvey;
            dto.surveys &#x3D; nameConfig.surveys;
             return this.createBQLServiceRequest(user, dto, actionName, timezoneClient);
          }
        }

        const { projectId, repoType, title, description, customData, target } &#x3D; dto;

        if (dto.customData &amp;&amp; dto.customData.contractId &amp;&amp; dto.repoType &#x3D;&#x3D;&#x3D; LeadRepoCareEnum.TRANSFER) {
          let primaryContract: any &#x3D; await this.primaryContractClient.sendDataPromise({
            id: dto.customData.contractId
          }, CmdPatternConst.PRIMARY_CONTRACT.PRIMARY_CONTRACT_ONE_BY_QUERY);
          if (primaryContract &amp;&amp; primaryContract.releaseStartDate &amp;&amp; primaryContract.releaseEndDate) {
            let releaseStartDate &#x3D; moment(primaryContract.releaseStartDate).format(&#x27;YYYY-MM-DD&#x27;);
            let releaseEndDate &#x3D; moment(primaryContract.releaseEndDate).format(&#x27;YYYY-MM-DD&#x27;);
            let currentDate &#x3D; moment().format(&#x27;YYYY-MM-DD&#x27;);
            if (moment(currentDate).isBefore(releaseEndDate) &amp;&amp; moment(currentDate).isAfter(releaseStartDate)) {
              throw new BadRequestException({
                errors: ErrorConst.CommonError(
                  ErrorConst.TRANSFER_NOT_ALLOWED),
              });
            }
          }
        }

        const { repository, config } &#x3D; await this.isRepoValid(repoType, projectId, target, user);

        const source &#x3D; CareSourceEnum.Care;
        const item &#x3D; {
          email: dto.email,
          name: dto.name,
          phone: dto.phone,
          customData,
          title,
          description,
          customerId: dto.customerId,
          repoType,
          target
        };

        const leadCare &#x3D; await this.preProcessServiceRequest(
          item,
          repository,
          config,
          user,
          timezoneClient,
          source
        );

        if (leadCare) {
          // đổi trạng thái hợp đồng
          if(leadCare.customData &amp;&amp; leadCare.customData.contractId) {
            switch (leadCare.repoType) {
              case  LeadRepoCareEnum.TRANSFER:
                this.primaryContractClient.sendData({
                  id: leadCare.customData.contractId,
                  status: true
                }, CmdPatternConst.PRIMARY_CONTRACT.PRIMARY_CONTRACT_UPDATE_TRANSFER_CONFIRM);
                break;
              case  LeadRepoCareEnum.PROJECT:
                this.primaryContractClient.sendData({
                  id: leadCare.customData.contractId,
                  status: true
                }, CmdPatternConst.PRIMARY_CONTRACT.PRIMARY_CONTRACT_UPDATE_DEPOSIT_CONFIRM);
                break;
              default:
                break;
            }
          }

          const importCommandId &#x3D; uuid.v4();
          await this.executeCommand(
            Action.IMPORT_LEAD_CARE,
            actionName,
            importCommandId,
            [leadCare] as any[],
            {}
          );
        }

        return this.response;
    }

    async createBQLServiceRequest(
      user,
      dto: any,
      actionName: string,
      timezoneClient?: string,
    ) {
      const { projectId, repoType, repoCode, title, description, customData } &#x3D; dto;

      const source &#x3D; CareSourceEnum.Care;

      const item &#x3D; {
        email: dto.email,
        name: dto.name,
        phone: dto.phone,
        customData,
        title,
        description,
        customerId: dto.customerId,
        repoType: LeadRepoCareEnum.BQL,
        repoCode,
        forBQL: true,
        idRepoConfig: dto.idRepoConfig,
        nameRepoConfig: dto.nameRepoConfig,
        target: dto.target,
        canSurvey: dto.canSurvey,
        surveys: dto.surveys,
      };

      const leadCare &#x3D; await this.preProcessBQLServiceRequest(
        item,
        timezoneClient,
        source
      );

      const project &#x3D; await this.propertyClient.sendDataPromise({ id: projectId }, CmdPatternConst.PROJECT.GET_PROJECT_BY_ID)

      if (!project) {
        throw new BadRequestException({
          errors: ErrorConst.CommonError(
            ErrorConst.NOT_FOUND,
            &#x27;Dự án&#x27;
          ),
        });
      }
      leadCare.project &#x3D; {
        id: project.id,
        name: project.name
      };

      // tự động assign về trưởng BQL

      // tạo công việc

      if (leadCare) {
        const importCommandId &#x3D; uuid.v4();
        await this.executeCommand(
          Action.IMPORT_LEAD_CARE,
          actionName,
          importCommandId,
          [leadCare] as any[],
          {}
        );
      }

      return this.response;
    }



    async deliverNormalLeadCare(recordNumber) {
        this.loggerService.log(this.context, &#x27;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;Starting deliver normal leadCares...&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x27;);
        const where &#x3D; {
          &#x27;configs.exploitTime.from&#x27; :{ $lte : new Date()},
          &#x27;configs.exploitTime.to&#x27; :{ $gte : new Date()}
        }
        const repos &#x3D; await this.leadRepoCareQueryRepo.findMany({
          where: where as any
        });
        if (!repos?.length) return;
        await Bluebird.map(repos, async (r) &#x3D;&gt; {
            const { configs } &#x3D; r;
            const activeConfigs &#x3D; configs.filter(item &#x3D;&gt; item.active);
            if (!activeConfigs?.length) return;
            let shouldUpdate &#x3D; false;
            await Bluebird.mapSeries(activeConfigs, async (c) &#x3D;&gt; {
                const { exploitTime } &#x3D; c;
                const now &#x3D; new Date();
                if (
                    now &lt; new Date(exploitTime.from) ||
                    now &gt; new Date(exploitTime.to)
                ) {
                    return;
                }
                const leadCares &#x3D; await this.leadCareQueryRepo.findMany({
                    repoId: r.id,
                    repoConfigCode: c.code,
                    isHot: false,
                    exploitStatus: { $in: [ExploitCareEnum.NEW, ExploitCareEnum.RENEW] },
                }, {}, {}, recordNumber);

                const { result, newConfig } &#x3D; await this.leadCareDelivery(
                    leadCares as any[],
                    c
                );

                if (!result.length) return;

              // exe 100 records
              const executeNum &#x3D; Math.floor(result.length / 100) + Math.floor(result.length % 100);
              for (let i &#x3D; 0; i &lt; executeNum; i++) {
                const executeResult &#x3D; result.slice((i * 100), (i * 100) + 100);
                const importCommandId &#x3D; uuid.v4();
                await this.executeCommand(
                  Action.IMPORT_LEAD_CARE,
                  &#x27;Deliver normal leadCare&#x27;,
                  importCommandId,
                  // result as any,
                  executeResult as any,
                  newConfig.notification || {}
                );
              }

                this.updateConfigData(configs, c.code, newConfig);
                if (!shouldUpdate) {
                  shouldUpdate &#x3D; true;
                }
                this.loggerService.log(this.context, &#x60;Delivered ${result.length} leadCares&#x60;);
            });
            if (shouldUpdate) {
              await this.leadRepoCareDomainSrv.updateLeadRepoCareConfigs(r.id, configs, &#x27;update configs&#x27;, null ,null);
          	}
            
        });
    }

    async renewNormalLeadCare(recordNumber) {
        this.loggerService.log(this.context, &#x27;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;Starting renew normal leadCares...&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x27;);
        const assigningLeadCare &#x3D; await this.leadCareQueryRepo.findMany({
            // &#x27;takeCare.id&#x27;: { $exists: true },
            isHot: false,
            repoType: {$ne: LeadRepoCareEnum.TRANSFER},
            exploitStatus: { $in: [ExploitCareEnum.REASSIGN, ExploitCareEnum.ASSIGN, ExploitCareEnum.PROCESSING, ExploitCareEnum.SURVEY] },
            expireTime: {
              $lt: new Date(),
            },
            assignDuration: { $ne: -1 },
        }, {}, {}, recordNumber);

        if (!assigningLeadCare?.length) {
            return;
        }

        const bulkData: ILeadCareDocument[] &#x3D; [];

        assigningLeadCare.forEach((leadCare) &#x3D;&gt; {
            const { exploitHistory, exploitStatus, assignDuration } &#x3D; leadCare;
            if (
                this.validateExpire(
                    exploitHistory,
                    assignDuration
                )
            ) {
              const {history} &#x3D; this.updateExploitHistory(
                  exploitHistory,
                  ExploitCareEnum.RENEW
                );

                Object.assign(leadCare, {
                  exploitStatus: ExploitCareEnum.RENEW,
                  exploitHistory: history,
                  takeCare: {},
                });

                bulkData.push(leadCare);
            }
        });

        if (!bulkData?.length) {
            return;
        }

        const importCommandId &#x3D; uuid.v4();
        await this.executeCommand(
            Action.IMPORT_LEAD_CARE,
            &#x27;Renew normal leadCare&#x27;,
            importCommandId,
            bulkData as any[]
        );

        this.loggerService.log(this.context, &#x60;Renewed ${bulkData.length} normal leadCares expired !!!&#x60;);
    }

    private async leadCareDelivery(data: ILeadCareDocument[], config: LeadRepoCareConfig) {
        const result: ILeadCare[] &#x3D; [];
        if (!data?.length) {
            return {
                result,
                newConfig: config,
            };
        }
        const initQueueData &#x3D; await this.initQueueData(config);
        const { orgChartQueue &#x3D; [], assignDuration, orgCharts } &#x3D; config;

        // init pos queue
        const posQueue &#x3D; new Queue&lt;PosQueueData&gt;([]);
        initQueueData.forEach((value, key) &#x3D;&gt; {
            const existedQueue &#x3D; orgChartQueue.find((item) &#x3D;&gt; item.id &#x3D;&#x3D;&#x3D; key);

            if (existedQueue) {
                posQueue.enqueue({
                    id: existedQueue.id,
                    employeeQueue: new Queue(existedQueue.employeeQueue || []),
                });
            } else {
                posQueue.enqueue({
                    id: key,
                    employeeQueue: new Queue(value),
                });
            }
        });

        const dataQueue &#x3D; new Queue(data, data.length);

        while (!dataQueue.isEmpty()) {
            // leadCare -&gt; pos -&gt; employee
            const orgChart &#x3D; posQueue.dequeue() as PosQueueData;
            const { employeeQueue, id } &#x3D; orgChart;
            if (employeeQueue.isEmpty()) {
              if (this.deliveredToAll(posQueue)) {
                  posQueue.data.forEach((item) &#x3D;&gt; {
                      item.employeeQueue.reset(initQueueData.get(item.id));
                  });
                  employeeQueue.reset(initQueueData.get(id)); 
              }
              if (!this.deliveredToAll(posQueue)) {
                posQueue.enqueue(orgChart);
                continue;
              }
             
            }
            const leadCare &#x3D; dataQueue.dequeue() as ILeadCare;
            const posName &#x3D; orgCharts.find((item) &#x3D;&gt; item.id &#x3D;&#x3D;&#x3D; id)?.name;
            const employee &#x3D; employeeQueue.dequeue() as any;
            if (leadCare.takeCare?.id !&#x3D;&#x3D; employee.id) {
                const exploitStatus &#x3D;
                    leadCare.exploitStatus &#x3D;&#x3D;&#x3D; ExploitCareEnum.NEW
                        ? ExploitCareEnum.ASSIGN
                        : ExploitCareEnum.REASSIGN;
                const {history, newHistory} &#x3D; this.updateExploitHistory(
                  leadCare.exploitHistory,
                  exploitStatus,
                  employee.id
                );

                Object.assign(leadCare, {
                    exploitStatus,
                    exploitHistory: history,
                    assignDuration,
                    takeCare: employee,
                    pos: { id, name: posName },
                    expireTime: this.getExpireTime(
                      newHistory?.updatedAt,
                      config.assignDuration
                  ),
                });
                result.push(leadCare);
            }
            posQueue.enqueue(orgChart);
        }

        Object.assign(config, { orgChartQueue: this.parseQueueData(posQueue) });

        return { result, newConfig: config };
    }
    private getExpireTime(start: Date, duration: number) {
      return new Date(moment(start).valueOf() + duration * 60000);
    }
    private validateExpire(
        history: IExploitHistory[],
        duration: number
    ) {
        if (duration &#x3D;&#x3D;&#x3D; -1) return false;
        const targetHistory &#x3D; history.filter((item) &#x3D;&gt;
            [ExploitCareEnum.ASSIGN, ExploitCareEnum.REASSIGN].includes(item.status)
        );
        if (!targetHistory.length) return false;

        const time &#x3D; targetHistory.pop();
        return (
            new Date().getTime() &gt;
            (new Date(time.updatedAt).getTime() + duration * 60000)
        );
    }

    private parseQueueData(posQueue: Queue&lt;PosQueueData&gt;) {
        return posQueue.data.map((item) &#x3D;&gt; ({
            id: item.id,
            employeeQueue: item.employeeQueue.data,
        }));
    }

    private deliveredToAll(posQueue: Queue&lt;PosQueueData&gt;) {
        return !posQueue.data.some((item) &#x3D;&gt; {
            return !item.employeeQueue.isEmpty();
        });
    }

    private async initQueueData(
        config: LeadRepoCareConfig
    ): Promise&lt;Map&lt;string, any[]&gt;&gt; {
        const initData &#x3D; new Map&lt;string, any[]&gt;();
        const {orgCharts} &#x3D; config;

        await Bluebird.mapSeries(orgCharts, async org &#x3D;&gt; {
          const {staffIds} &#x3D; org;
          if (!staffIds?.length) {
            initData.set(org.id, []);
          } else {
            const employees &#x3D; await this.employeeRepo.findManyWithSort({
              id: { $in: staffIds },
            });
            initData.set(
              org.id,
              employees.map((e) &#x3D;&gt; ({
                id: e.id,
                name: e.name,
                email: e.email,
                phone: e.phone,
              }))
            );
          }
        });

        return initData;
    }

    private async validateRepo(repoId: string, repoConfigCode: string) {
        const repository &#x3D; await this.leadRepoCareQueryRepo.findById(repoId, true);

        if (!repository) {
            throw new BadRequestException({
                errors: ErrorConst.CommonError(
                    ErrorConst.NOT_FOUND,
                    &#x27;LeadCare Repository&#x27;
                ),
            });
        }

        const config &#x3D; repository.configs.find(
            (item) &#x3D;&gt; item.code &#x3D;&#x3D;&#x3D; repoConfigCode
        );
        if (!config) {
            throw new BadRequestException({
                errors: ErrorConst.CommonError(
                    ErrorConst.NOT_FOUND,
                    &#x27;LeadCare Repository Config&#x27;
                ),
            });
        }

        return { repository, config };
    }

    private async isRepoValid(type: string, projectId: string, target: string, user: any) {
        const elemMatch: any &#x3D; {
          active: true,
          projectId: projectId,
          target
        };
        if (user) {
          elemMatch.forLoggedUser &#x3D; true;
        } else {
          elemMatch.forNotLoggedUser &#x3D; true;
        }
        const query &#x3D; {
          type: type,
          configs: {
            $elemMatch: elemMatch
          }
        };
        const repository &#x3D; await this.leadRepoCareQueryRepo.findOne(query, true, {createdDate: -1});

        if (!repository) {
            throw new BadRequestException({
                errors: ErrorConst.CommonError(
                    ErrorConst.NOT_FOUND,
                    &#x27;Loại yêu cầu&#x27;
                ),
            });
        }

        repository.configs &#x3D; repository.configs.reverse();
        const config &#x3D; repository.configs.find(
            (item) &#x3D;&gt; item.active &#x3D;&#x3D;&#x3D; true &amp;&amp; item.projectId &#x3D;&#x3D;&#x3D; projectId &amp;&amp; item.target &#x3D;&#x3D;&#x3D; target &amp;&amp; (
              (user &amp;&amp; item.forLoggedUser) || (!user &amp;&amp; item.forNotLoggedUser)
            )
        );
        if (!config) {
            throw new BadRequestException({
                errors: ErrorConst.CommonError(
                    ErrorConst.NOT_FOUND,
                    &#x27;Loại yêu cầu&#x27;
                ),
            });
        }

        if (repository.type &#x3D;&#x3D;&#x3D; LeadRepoCareEnum.TRANSFER) {
          // Ngừng chuyển nhượng
          if (config.isStopTransfer) {
            throw new BadRequestException({
              errors: ErrorConst.CommonError(
                  ErrorConst.CANT_TRANSFER,
                  config.project ? config.project.name : &#x27;&#x27;
              ),
            });
          } else {
            // Gửi mail chuyển nhượng
            const employees &#x3D; await this.employeeClient.sendDataPromise({&#x27;pos.id&#x27;: {$in: config.orgChartIds}},CmdPatternConst.SERVICE.EMPLOYEE.GET_EMPLOYEE_BY_QUERY)
            await this.mailerClient.sendDataPromise({
              models: employees,
              projectName: config.project ? config.project.name : &#x27;&#x27;
            }, CmdPatternConst.MAILER.SEND_EMAIL_LEAD_TRANSFER);
          }
        }
        return { repository, config };
    }

    private async executeCommand&lt;T&gt;(
        action: string,
        actionName: string,
        commandId: string,
        item: CommandModel | CommandModel[],
        additionalData?: T
    ) {
        switch (action) {
            case Action.IMPORT_LEAD_CARE:
                return this.commandBus.execute(
                    new ImportLeadCareCommand(
                        actionName,
                        commandId,
                        item as CommandModel[],
                        additionalData
                    )
                );
            default:
                break;
        }
    }

    private updateExploitHistory(
        history: IExploitHistory[] &#x3D; [],
        status: ExploitCareEnum,
        takeCareId?: string
    ) {
        const newHistory: IExploitHistory &#x3D; {
          status, updatedAt: new Date(), updatedBy: takeCareId, takeCareId
        };
        if (history.length &gt; 500) {
          history &#x3D; history.slice(history.length - 500);
        }
        history.push(newHistory);
        return {history, newHistory};
    }

    private validateLeadCare(leadCare) {
      if (isNullOrUndefined(leadCare.phone) || !leadCare.phone.toString().match(CommonConst.REGEX_VN_PHONE)){
          return &#x27;Số điện thoại sai&#x27;;
      }
      if (leadCare.email &amp;&amp; !leadCare.email.toString().match(CommonConst.REGEX_EMAIL)){
          return &#x27;Email sai&#x27;;
      }
      return &#x27;&#x27;;
  }

  private async validateEmployee(employee, orgChartGroup: Map&lt;string, string[]&gt;) {
    const pos: any &#x3D; {};
    orgChartGroup.forEach((v, k) &#x3D;&gt; {
        if (v.includes(employee?.id)) {
            Object.assign(pos, k);
        }
    });

    if (!isEmpty(pos)) {
        return {
          pos
        };
    }

    return {
        error: &#x60;Nhân viên ${employee?.code} không tồn tại trên sàn chỉ định&#x60;,
    };
  }

  private async preProcessLeadCare(leadCare, repository: LeadRepoCare, config: LeadRepoCareConfig, user, timezoneClient, source) {
    const code &#x3D; await this.genCodeSrv.generateCode(&#x27;SR-&#x27;);
    const project &#x3D; !leadCare.isHot ? config.project : {};
    const result &#x3D; {
        ...leadCare,
        repoId: repository.id,
        repoConfigCode: config.code,
        importedBy: {
            id: user?.id,
            name: user?.name,
        },
        isHot: leadCare.isHot ?? false,
        id: uuid.v4(),
        type: CommonConst.TYPE.PRIMARY,
        status: StatusEnum.GREEN,
        lifeCycleStatus: LifeCycleStatusEnum.IN_POOL,
        timezoneClient,
        project,
        code,
        source,
    };
    const {history, newHistory} &#x3D; this.updateExploitHistory(
      result.exploitHistory,
      ExploitCareEnum.NEW
    );
    Object.assign(result, {
      _id: result.id,
      exploitStatus: ExploitCareEnum.NEW,
      exploitHistory: history,
    });

    return result;
  }

  private async preProcessServiceRequest(leadCare, repository: LeadRepoCare, config: LeadRepoCareConfig, user, timezoneClient, source) {
    const code &#x3D; await this.genCodeSrv.generateCode(&#x27;SR-&#x27;);
    const project &#x3D; config.project;
    const result &#x3D; {
      ...leadCare,
      repoId: repository.id,
      repoConfigCode: config.code,
      importedBy: {},
      isHot: false,
      id: uuid.v4(),
      type: CommonConst.TYPE.PRIMARY,
      status: StatusEnum.GREEN,
      lifeCycleStatus: LifeCycleStatusEnum.IN_POOL,
      timezoneClient,
      project,
      code,
      source,
      takeCare: {}
    };
    const {history, newHistory} &#x3D; this.updateExploitHistory(
      result.exploitHistory,
      ExploitCareEnum.NEW
    );
    Object.assign(result, {
      _id: result.id,
      exploitStatus: ExploitCareEnum.NEW,
      exploitHistory: history,
    });

    return result;
  }

  private async preProcessBQLServiceRequest(leadCare, timezoneClient, source) {
    const code &#x3D; await this.genCodeSrv.generateCode(&#x27;SR-&#x27;);
    const result &#x3D; {
      ...leadCare,
      importedBy: {},
      isHot: false,
      id: uuid.v4(),
      type: CommonConst.TYPE.PRIMARY,
      status: StatusEnum.GREEN,
      lifeCycleStatus: LifeCycleStatusEnum.IN_POOL,
      timezoneClient,
      code,
      source,
      takeCare: {}
    };
    const {history} &#x3D; this.updateExploitHistory(
      result.exploitHistory,
      ExploitCareEnum.NEW
    );
    Object.assign(result, {
      _id: result.id,
      exploitStatus: ExploitCareEnum.NEW,
      exploitHistory: history,
    });

    return result;
  }

  private updateConfigData(src: LeadRepoCareConfig[], code: string, config: LeadRepoCareConfig) {
    const index &#x3D; src.findIndex(item &#x3D;&gt; item.code &#x3D;&#x3D;&#x3D; code);
    if (!code) {
      src &#x3D; src.concat(config);
    }
    if (code) {
      src &#x3D; [...src.slice(0, index), config,...src.slice(index + 1)];
    }
  }

  private async isRepoCodeVaild(repoCode: string, projectId: string) {
        const query &#x3D; {
          configs: {
            $elemMatch: {
              code: repoCode,
              projectId: projectId,
              forLoggedUser: true
            }
          }
        };
        const repository &#x3D; await this.leadRepoCareQueryRepo.findOne(query, true, {createdDate: -1});

        if (!repository) {
            throw new BadRequestException({
                errors: ErrorConst.CommonError(
                    ErrorConst.NOT_FOUND,
                    &#x27;Loại yêu cầu&#x27;
                ),
            });
        }
        repository.configs &#x3D; repository.configs.reverse();
        const config &#x3D; repository.configs.find(
            (item) &#x3D;&gt; item.active &#x3D;&#x3D;&#x3D; true &amp;&amp; item.projectId &#x3D;&#x3D;&#x3D; projectId &amp;&amp; item.forLoggedUser
        );
        if (!config) {
            throw new BadRequestException({
                errors: ErrorConst.CommonError(
                    ErrorConst.NOT_FOUND,
                    &#x27;Loại yêu cầu&#x27;
                ),
            });
        }

        return { repository, config };
    }
}
</code></pre>
    </div>

</div>







                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'LeadCareDomainServiceExtends.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
