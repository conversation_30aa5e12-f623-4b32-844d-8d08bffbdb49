<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-lead documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-lead documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">







<ol class="breadcrumb">
  <li>Injectables</li>
  <li>LeadRepoQueryRepository</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/leadRepo.queryside/repositories/query.repository.ts</code>
        </p>


            <p class="comment">
                <h3>Extends</h3>
            </p>
            <p class="comment">
                        <code><a href="../injectables/BaseRepository.html" target="_self" >BaseRepository</a></code>
            </p>


            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Protected</span>
                                    <span class="modifier">Readonly</span>
                                <a href="#repository">repository</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#find">find</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#aggregate">aggregate</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#bulkUpdate">bulkUpdate</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#count">count</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#create">create</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findById">findById</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findByQuery">findByQuery</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findMany">findMany</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findOne">findOne</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getLeadRepoCareByProject">getLeadRepoCareByProject</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#parseSearchFilter">parseSearchFilter</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#removeById">removeById</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateById">updateById</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section>
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(repository: <a href="../classes/LeadRepo.html">Model<ILeadRepoQueryDocument></a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="12" class="link-to-prism">src/modules/leadRepo.queryside/repositories/query.repository.ts:12</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>repository</td>
                                                  
                                                        <td>
                                                                        <code><a href="../classes/LeadRepo.html" target="_self" >Model&lt;ILeadRepoQueryDocument&gt;</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section>
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="find"></a>
                    <span class="name">
                        <b>
                            find
                        </b>
                        <a href="#find"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>find(query)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="20"
                            class="link-to-prism">src/modules/leadRepo.queryside/repositories/query.repository.ts:20</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="aggregate"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            aggregate
                        </b>
                        <a href="#aggregate"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>aggregate(aggs: [Record])</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Inherited from         <code><a href="../injectables/BaseRepository.html" target="_self" >BaseRepository</a></code>
</div>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <div class="io-line">Defined in         <code><a href="../injectables/BaseRepository.html#source" target="_self" >BaseRepository:49</a></code>
</div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>aggs</td>
                                    <td>
                                            <code>[Record]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="bulkUpdate"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            bulkUpdate
                        </b>
                        <a href="#bulkUpdate"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>bulkUpdate(records: T[])</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Inherited from         <code><a href="../injectables/BaseRepository.html" target="_self" >BaseRepository</a></code>
</div>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <div class="io-line">Defined in         <code><a href="../injectables/BaseRepository.html#source" target="_self" >BaseRepository:20</a></code>
</div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>records</td>
                                    <td>
                                            <code>T[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>[]</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="count"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            count
                        </b>
                        <a href="#count"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>count(where: <a href="../undefineds/WhereFilter.html">WhereFilter<TFilter></a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Inherited from         <code><a href="../injectables/BaseRepository.html" target="_self" >BaseRepository</a></code>
</div>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <div class="io-line">Defined in         <code><a href="../injectables/BaseRepository.html#source" target="_self" >BaseRepository:53</a></code>
</div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>where</td>
                                    <td>
                                                <code><a href="../miscellaneous/typealiases.html#WhereFilter" target="_self" >WhereFilter&lt;TFilter&gt;</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;number&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="create"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            create
                        </b>
                        <a href="#create"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>create(record: TInput)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Inherited from         <code><a href="../injectables/BaseRepository.html" target="_self" >BaseRepository</a></code>
</div>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <div class="io-line">Defined in         <code><a href="../injectables/BaseRepository.html#source" target="_self" >BaseRepository:113</a></code>
</div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>record</td>
                                    <td>
                                            <code>TInput</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;T&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findById"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findById
                        </b>
                        <a href="#findById"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findById(_id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, lean)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Inherited from         <code><a href="../injectables/BaseRepository.html" target="_self" >BaseRepository</a></code>
</div>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <div class="io-line">Defined in         <code><a href="../injectables/BaseRepository.html#source" target="_self" >BaseRepository:107</a></code>
</div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>_id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>lean</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>false</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;T&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findByQuery"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findByQuery
                        </b>
                        <a href="#findByQuery"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findByQuery(where)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Inherited from         <code><a href="../injectables/BaseRepository.html" target="_self" >BaseRepository</a></code>
</div>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <div class="io-line">Defined in         <code><a href="../injectables/BaseRepository.html#source" target="_self" >BaseRepository:57</a></code>
</div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>where</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findMany"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findMany
                        </b>
                        <a href="#findMany"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findMany(filter: <a href="../interfaces/FindManyFilter.html">FindManyFilter<TFilter></a>, lean)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Inherited from         <code><a href="../injectables/BaseRepository.html" target="_self" >BaseRepository</a></code>
</div>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <div class="io-line">Defined in         <code><a href="../injectables/BaseRepository.html#source" target="_self" >BaseRepository:80</a></code>
</div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>filter</td>
                                    <td>
                                                <code><a href="../interfaces/FindManyFilter.html" target="_self" >FindManyFilter&lt;TFilter&gt;</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>lean</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>false</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;T[]&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findOne"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findOne
                        </b>
                        <a href="#findOne"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findOne(filter: <a href="../interfaces/FindOneFilter.html">FindOneFilter<TFilter></a>, lean)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Inherited from         <code><a href="../injectables/BaseRepository.html" target="_self" >BaseRepository</a></code>
</div>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <div class="io-line">Defined in         <code><a href="../injectables/BaseRepository.html#source" target="_self" >BaseRepository:98</a></code>
</div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>filter</td>
                                    <td>
                                                <code><a href="../interfaces/FindOneFilter.html" target="_self" >FindOneFilter&lt;TFilter&gt;</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>lean</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>false</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;T&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getLeadRepoCareByProject"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getLeadRepoCareByProject
                        </b>
                        <a href="#getLeadRepoCareByProject"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getLeadRepoCareByProject(projectId)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Inherited from         <code><a href="../injectables/BaseRepository.html" target="_self" >BaseRepository</a></code>
</div>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <div class="io-line">Defined in         <code><a href="../injectables/BaseRepository.html#source" target="_self" >BaseRepository:60</a></code>
</div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>projectId</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="parseSearchFilter"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            parseSearchFilter
                        </b>
                        <a href="#parseSearchFilter"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>parseSearchFilter(where: <a href="../undefineds/WhereFilter.html">WhereFilter<TFilter></a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Inherited from         <code><a href="../injectables/BaseRepository.html" target="_self" >BaseRepository</a></code>
</div>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <div class="io-line">Defined in         <code><a href="../injectables/BaseRepository.html#source" target="_self" >BaseRepository:156</a></code>
</div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>where</td>
                                    <td>
                                                <code><a href="../miscellaneous/typealiases.html#WhereFilter" target="_self" >WhereFilter&lt;TFilter&gt;</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="removeById"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            removeById
                        </b>
                        <a href="#removeById"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>removeById(_id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Inherited from         <code><a href="../injectables/BaseRepository.html" target="_self" >BaseRepository</a></code>
</div>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <div class="io-line">Defined in         <code><a href="../injectables/BaseRepository.html#source" target="_self" >BaseRepository:142</a></code>
</div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>_id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateById"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateById
                        </b>
                        <a href="#updateById"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateById(_id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, record: TUpdate)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Inherited from         <code><a href="../injectables/BaseRepository.html" target="_self" >BaseRepository</a></code>
</div>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <div class="io-line">Defined in         <code><a href="../injectables/BaseRepository.html#source" target="_self" >BaseRepository:126</a></code>
</div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>_id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>record</td>
                                    <td>
                                            <code>TUpdate</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>
            <section>
    
        <h3 id="inputs">
            Properties
        </h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="repository"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Protected</span>
                                <span class="modifier">Readonly</span>
                            repository</b>
                            <a href="#repository"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>    <code>Model&lt;T&gt;</code>

                        </td>
                    </tr>
                            <tr>
                                <td class="col-md-4">
                                    <div class="io-line">Inherited from         <code><a href="../injectables/BaseRepository.html" target="_self" >BaseRepository</a></code>
</div>
                                </td>
                            </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in         <code><a href="../injectables/BaseRepository.html#source" target="_self" >BaseRepository:18</a></code>
</div>
                            </td>
                        </tr>


            </tbody>
        </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Inject, Injectable } from &quot;@nestjs/common&quot;;
import { Model } from &quot;mongoose&quot;;
import { CommonConst } from &quot;../../shared/constant/common.const&quot;;
import { LeadRepo } from &quot;../../shared/models/leadRepo/model&quot;;
import { BaseRepository } from &quot;../../shared/services/baseRepository/repository.base&quot;;
import { ILeadRepoQueryDocument } from &quot;../interfaces/document.interface&quot;;

@Injectable()
export class LeadRepoQueryRepository extends BaseRepository&lt;
  ILeadRepoQueryDocument,
  LeadRepo
&gt; {
  constructor(
    @Inject(CommonConst.QUERY_MODEL_TOKEN)
    protected readonly repository: Model&lt;ILeadRepoQueryDocument&gt;
  ) {
    super();
  }

  find(query) {
    return this.repository.find(query);
  }
}
</code></pre>
    </div>

</div>







                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'LeadRepoQueryRepository.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
