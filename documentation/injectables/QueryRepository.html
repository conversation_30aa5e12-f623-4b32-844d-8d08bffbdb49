<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-lead documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-lead documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">







<ol class="breadcrumb">
  <li>Injectables</li>
  <li>QueryRepository</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/lead.queryside/repository/query.repository.ts</code>
        </p>




            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#assignLead">assignLead</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#assignLeadForEmployee">assignLeadForEmployee</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#checkDuplicate">checkDuplicate</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#checkDuplicateUpdate">checkDuplicateUpdate</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#count">count</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#countAllTickets">countAllTickets</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#countExploitationByEmployee">countExploitationByEmployee</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#countLeadReadyForAssigningByEmp">countLeadReadyForAssigningByEmp</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#countLeadReadyForAssigningByPOS">countLeadReadyForAssigningByPOS</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#countLeadsProcessBy">countLeadsProcessBy</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#countLifeCycleStatusByEmployee">countLifeCycleStatusByEmployee</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#countLifeCycleStatusForLeads">countLifeCycleStatusForLeads</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#countPrimaryLead">countPrimaryLead</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#countProcessTicketsConsignmentByUser">countProcessTicketsConsignmentByUser</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#countProcessTicketsDemandByUser">countProcessTicketsDemandByUser</a>
                            </li>
                            <li>
                                <a href="#countReport">countReport</a>
                            </li>
                            <li>
                                <a href="#countReportInPool">countReportInPool</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#create">create</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createMany">createMany</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#delete">delete</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#deleteMany">deleteMany</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#expiredLead">expiredLead</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#failLead">failLead</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#fetchLeadsWithAggs">fetchLeadsWithAggs</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#filter">filter</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#filterReport">filterReport</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAggregateModelById">findAggregateModelById</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAll">findAll</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAllAdvising">findAllAdvising</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAllCommon">findAllCommon</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAllPrimary">findAllPrimary</a>
                            </li>
                            <li>
                                <a href="#findLeadByCustomerId">findLeadByCustomerId</a>
                            </li>
                            <li>
                                <a href="#findLeadByEmployeeId">findLeadByEmployeeId</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findLeadById">findLeadById</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findLeadsAssigned">findLeadsAssigned</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findLeadsAssignedEmployee">findLeadsAssignedEmployee</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findLeadsProcessBy">findLeadsProcessBy</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findLeadsToAssign">findLeadsToAssign</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findMany">findMany</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findOne">findOne</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getAllByPos">getAllByPos</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getByCustomer">getByCustomer</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getLeadByStatus">getLeadByStatus</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getLeadsByStatusForExport">getLeadsByStatusForExport</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getLeadsToDeliver">getLeadsToDeliver</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getTodayExploited">getTodayExploited</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#isReadyToAssign">isReadyToAssign</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#isValidBeforePull">isValidBeforePull</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#isValidMarkProcessing">isValidMarkProcessing</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#listAllAdvising">listAllAdvising</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#listAllCommon">listAllCommon</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#listLeadsProcessBy">listLeadsProcessBy</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#pendingLead">pendingLead</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#processingLead">processingLead</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#reassignLead">reassignLead</a>
                            </li>
                            <li>
                                    <span class="modifier">Protected</span>
                                <a href="#transformSort">transformSort</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#unprocessingLead">unprocessingLead</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#update">update</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateMany">updateMany</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateOne">updateOne</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateStatus">updateStatus</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section>
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(readModel: Model<ILeadDocument>, employeeRepository: <a href="../injectables/EmployeeQueryRepository.html">EmployeeQueryRepository</a>, orgchartClient: <a href="../injectables/OrgchartClient.html">OrgchartClient</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="23" class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:23</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>readModel</td>
                                                  
                                                        <td>
                                                                    <code>Model&lt;ILeadDocument&gt;</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>employeeRepository</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/EmployeeQueryRepository.html" target="_self" >EmployeeQueryRepository</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>orgchartClient</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/OrgchartClient.html" target="_self" >OrgchartClient</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section>
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="assignLead"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            assignLead
                        </b>
                        <a href="#assignLead"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>assignLead(model)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="687"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:687</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>model</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="assignLeadForEmployee"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            assignLeadForEmployee
                        </b>
                        <a href="#assignLeadForEmployee"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>assignLeadForEmployee(model)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="408"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:408</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>model</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="checkDuplicate"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            checkDuplicate
                        </b>
                        <a href="#checkDuplicate"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>checkDuplicate(query)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="368"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:368</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;boolean&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="checkDuplicateUpdate"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            checkDuplicateUpdate
                        </b>
                        <a href="#checkDuplicateUpdate"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>checkDuplicateUpdate(query)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="383"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:383</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;boolean&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="count"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            count
                        </b>
                        <a href="#count"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>count(query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="569"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:569</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="countAllTickets"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            countAllTickets
                        </b>
                        <a href="#countAllTickets"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>countAllTickets()</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="890"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:890</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="countExploitationByEmployee"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            countExploitationByEmployee
                        </b>
                        <a href="#countExploitationByEmployee"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>countExploitationByEmployee(where: Record<string | any>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="2033"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:2033</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>where</td>
                                    <td>
                                            <code>Record&lt;string | any&gt;</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="countLeadReadyForAssigningByEmp"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            countLeadReadyForAssigningByEmp
                        </b>
                        <a href="#countLeadReadyForAssigningByEmp"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>countLeadReadyForAssigningByEmp(employee: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, type: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1525"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:1525</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>employee</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>type</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="countLeadReadyForAssigningByPOS"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            countLeadReadyForAssigningByPOS
                        </b>
                        <a href="#countLeadReadyForAssigningByPOS"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>countLeadReadyForAssigningByPOS(posId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, type: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1504"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:1504</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>posId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>type</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="countLeadsProcessBy"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            countLeadsProcessBy
                        </b>
                        <a href="#countLeadsProcessBy"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>countLeadsProcessBy(loggedUser: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="935"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:935</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>loggedUser</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;number&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="countLifeCycleStatusByEmployee"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            countLifeCycleStatusByEmployee
                        </b>
                        <a href="#countLifeCycleStatusByEmployee"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>countLifeCycleStatusByEmployee(employeeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String" target="_blank">String</a>, lifeCycleStatus: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="861"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:861</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>employeeId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String" target="_blank" >String</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>lifeCycleStatus</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="countLifeCycleStatusForLeads"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            countLifeCycleStatusForLeads
                        </b>
                        <a href="#countLifeCycleStatusForLeads"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>countLifeCycleStatusForLeads(leadIds, lifeCycleStatus)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="876"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:876</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>leadIds</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>lifeCycleStatus</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="countPrimaryLead"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            countPrimaryLead
                        </b>
                        <a href="#countPrimaryLead"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>countPrimaryLead(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1557"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:1557</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="countProcessTicketsConsignmentByUser"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            countProcessTicketsConsignmentByUser
                        </b>
                        <a href="#countProcessTicketsConsignmentByUser"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>countProcessTicketsConsignmentByUser(query)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="916"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:916</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="countProcessTicketsDemandByUser"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            countProcessTicketsDemandByUser
                        </b>
                        <a href="#countProcessTicketsDemandByUser"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>countProcessTicketsDemandByUser(query)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="897"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:897</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="countReport"></a>
                    <span class="name">
                        <b>
                            countReport
                        </b>
                        <a href="#countReport"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>countReport(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, posInPool: string[])</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1345"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:1345</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>posInPool</td>
                                    <td>
                                            <code>string[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>[]</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="countReportInPool"></a>
                    <span class="name">
                        <b>
                            countReportInPool
                        </b>
                        <a href="#countReportInPool"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>countReportInPool(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, posInPool: string[])</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1453"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:1453</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>posInPool</td>
                                    <td>
                                            <code>string[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>[]</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="create"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            create
                        </b>
                        <a href="#create"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>create(readmodel)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="347"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:347</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>readmodel</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;ILeadDocument&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createMany"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            createMany
                        </b>
                        <a href="#createMany"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createMany(records: <a href="../interfaces/ILead.html">ILead[]</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1959"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:1959</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>records</td>
                                    <td>
                                                <code><a href="../interfaces/ILead.html" target="_self" >ILead[]</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="delete"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            delete
                        </b>
                        <a href="#delete"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>delete(id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="670"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:670</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteMany"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            deleteMany
                        </b>
                        <a href="#deleteMany"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteMany()</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="678"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:678</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="expiredLead"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            expiredLead
                        </b>
                        <a href="#expiredLead"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>expiredLead(model: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="817"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:817</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>model</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="failLead"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            failLead
                        </b>
                        <a href="#failLead"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>failLead(model: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="779"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:779</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>model</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="fetchLeadsWithAggs"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            fetchLeadsWithAggs
                        </b>
                        <a href="#fetchLeadsWithAggs"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>fetchLeadsWithAggs(filters)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="2088"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:2088</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>filters</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="filter"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            filter
                        </b>
                        <a href="#filter"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>filter(query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, isPaging: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1156"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:1156</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>isPaging</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>false</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="filterReport"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            filterReport
                        </b>
                        <a href="#filterReport"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>filterReport(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, posInPool: any[], isPaging: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1213"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:1213</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>posInPool</td>
                                    <td>
                                            <code>any[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>[]</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>isPaging</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>false</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAggregateModelById"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findAggregateModelById
                        </b>
                        <a href="#findAggregateModelById"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAggregateModelById(id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="335"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:335</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;QueryAggregateModel&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAll"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findAll
                        </b>
                        <a href="#findAll"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAll()</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="33"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:33</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;ILeadDocument[]&gt;</code>

                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAllAdvising"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findAllAdvising
                        </b>
                        <a href="#findAllAdvising"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAllAdvising()</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="71"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:71</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;ILeadDocument[]&gt;</code>

                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAllCommon"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findAllCommon
                        </b>
                        <a href="#findAllCommon"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAllCommon(query)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="42"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:42</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;ILeadDocument[]&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAllPrimary"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findAllPrimary
                        </b>
                        <a href="#findAllPrimary"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAllPrimary(user, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="92"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:92</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;any&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findLeadByCustomerId"></a>
                    <span class="name">
                        <b>
                            findLeadByCustomerId
                        </b>
                        <a href="#findLeadByCustomerId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findLeadByCustomerId(customerId: string[], mapping: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1129"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:1129</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>customerId</td>
                                    <td>
                                            <code>string[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>[]</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>mapping</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>false</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findLeadByEmployeeId"></a>
                    <span class="name">
                        <b>
                            findLeadByEmployeeId
                        </b>
                        <a href="#findLeadByEmployeeId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findLeadByEmployeeId(employeeId: string[])</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1551"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:1551</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>employeeId</td>
                                    <td>
                                            <code>string[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>[]</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findLeadById"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findLeadById
                        </b>
                        <a href="#findLeadById"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findLeadById(id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="302"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:302</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;ILeadDocument&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findLeadsAssigned"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findLeadsAssigned
                        </b>
                        <a href="#findLeadsAssigned"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findLeadsAssigned()</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="744"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:744</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findLeadsAssignedEmployee"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findLeadsAssignedEmployee
                        </b>
                        <a href="#findLeadsAssignedEmployee"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findLeadsAssignedEmployee(employeeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="733"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:733</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>employeeId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findLeadsProcessBy"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findLeadsProcessBy
                        </b>
                        <a href="#findLeadsProcessBy"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findLeadsProcessBy(loggedUser, type)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="443"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:443</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>loggedUser</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>type</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findLeadsToAssign"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findLeadsToAssign
                        </b>
                        <a href="#findLeadsToAssign"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findLeadsToAssign(employeeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, posId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, exchangeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, type: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, shared: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="594"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:594</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>employeeId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>posId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>exchangeId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>type</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>shared</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>false</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findMany"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findMany
                        </b>
                        <a href="#findMany"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findMany(filter, projection: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank">object</a>, limit: null)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="2029"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:2029</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>filter</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>projection</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>limit</td>
                                    <td>
                                            <code>null</code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>null</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findOne"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findOne
                        </b>
                        <a href="#findOne"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findOne(query)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="293"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:293</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;ILeadDocument&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getAllByPos"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getAllByPos
                        </b>
                        <a href="#getAllByPos"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getAllByPos(posId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String" target="_blank">String</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="278"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:278</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>posId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String" target="_blank" >String</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;ILeadDocument[]&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getByCustomer"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getByCustomer
                        </b>
                        <a href="#getByCustomer"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getByCustomer(customerId)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="269"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:269</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>customerId</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getLeadByStatus"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getLeadByStatus
                        </b>
                        <a href="#getLeadByStatus"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getLeadByStatus(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, status: <a href="../undefineds/ExploitEnum.html">ExploitEnum</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, page: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>, pageSize: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1689"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:1689</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>status</td>
                                    <td>
                                                <code><a href="../miscellaneous/enumerations.html#ExploitEnum" target="_self" >ExploitEnum</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>page</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>pageSize</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;any&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getLeadsByStatusForExport"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getLeadsByStatusForExport
                        </b>
                        <a href="#getLeadsByStatusForExport"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getLeadsByStatusForExport(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, status: <a href="../undefineds/ExploitEnum.html">ExploitEnum</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1811"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:1811</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>status</td>
                                    <td>
                                                <code><a href="../miscellaneous/enumerations.html#ExploitEnum" target="_self" >ExploitEnum</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getLeadsToDeliver"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getLeadsToDeliver
                        </b>
                        <a href="#getLeadsToDeliver"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getLeadsToDeliver(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, page: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>, pageSize: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="2196"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:2196</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>page</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>1</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>pageSize</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>10</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;ILeadDocument[]&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTodayExploited"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getTodayExploited
                        </b>
                        <a href="#getTodayExploited"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getTodayExploited(userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="2068"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:2068</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>userId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isReadyToAssign"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            isReadyToAssign
                        </b>
                        <a href="#isReadyToAssign"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>isReadyToAssign(leadId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="710"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:710</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>leadId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isValidBeforePull"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            isValidBeforePull
                        </b>
                        <a href="#isValidBeforePull"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>isValidBeforePull(employeeId, type: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="572"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:572</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>employeeId</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>type</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isValidMarkProcessing"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            isValidMarkProcessing
                        </b>
                        <a href="#isValidMarkProcessing"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>isValidMarkProcessing(employeeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, leadId)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="720"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:720</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>employeeId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>leadId</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="listAllAdvising"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            listAllAdvising
                        </b>
                        <a href="#listAllAdvising"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>listAllAdvising(page: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>, pageSize: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>, query)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1094"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:1094</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-description"><p>Lấy danh sách yêu cầu tư vấn dịch vụ</p>
</div>

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>page</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>1</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>pageSize</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>10</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;ILeadDocument[]&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="listAllCommon"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            listAllCommon
                        </b>
                        <a href="#listAllCommon"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>listAllCommon(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, page: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>, pageSize: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="950"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:950</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>page</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>1</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>pageSize</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>10</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;ILeadDocument[]&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="listLeadsProcessBy"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            listLeadsProcessBy
                        </b>
                        <a href="#listLeadsProcessBy"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>listLeadsProcessBy(loggedUser: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="542"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:542</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Description</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>loggedUser</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                    <td>
                                        <p>user</p>

                                    </td>
                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                    <td>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="pendingLead"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            pendingLead
                        </b>
                        <a href="#pendingLead"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>pendingLead(model)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="842"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:842</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>model</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="processingLead"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            processingLead
                        </b>
                        <a href="#processingLead"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>processingLead(id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String" target="_blank">String</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="629"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:629</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String" target="_blank" >String</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="reassignLead"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            reassignLead
                        </b>
                        <a href="#reassignLead"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>reassignLead(model: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="755"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:755</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>model</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="transformSort"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Protected</span>
                            transformSort
                        </b>
                        <a href="#transformSort"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>transformSort(paramSort?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String" target="_blank">String</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1138"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:1138</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>paramSort</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String" target="_blank" >String</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="unprocessingLead"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            unprocessingLead
                        </b>
                        <a href="#unprocessingLead"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>unprocessingLead(model)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="652"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:652</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>model</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="update"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            update
                        </b>
                        <a href="#update"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>update(model)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="357"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:357</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>model</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;ILeadDocument&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateMany"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateMany
                        </b>
                        <a href="#updateMany"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateMany(records: any[])</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1990"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:1990</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>records</td>
                                    <td>
                                            <code>any[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateOne"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateOne
                        </b>
                        <a href="#updateOne"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateOne(model: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="802"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:802</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>model</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateStatus"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateStatus
                        </b>
                        <a href="#updateStatus"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateStatus(model)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="429"
                            class="link-to-prism">src/modules/lead.queryside/repository/query.repository.ts:429</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>model</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Model } from &#x27;mongoose&#x27;;
import { Inject, Injectable } from &#x27;@nestjs/common&#x27;;
import { QueryAggregateModel } from &#x27;../models/query-aggregate.model&#x27;;
import { ILeadDocument } from &#x27;../interfaces/document.interface&#x27;;
import _ &#x3D; require(&#x27;lodash&#x27;);
import { LifeCycleStatusEnum } from &#x27;../../shared/enum/life-cycle-status.enum&#x27;;
import { SourceEnum } from &#x27;../../shared/enum/source.enum&#x27;;
import { isNullOrUndefined } from &#x27;util&#x27;;
import { CommonConst } from &#x27;../../../modules/shared/constant/common.const&#x27;;
import { TransactionTypeEnum } from &#x27;../../../modules/shared/enum/transaction-type.enum&#x27;;
import { EmployeeQueryRepository } from &#x27;../../employee/repository/query.repository&#x27;;
import { PermissionConst } from &#x27;../../../modules/shared/constant/permission.const&#x27;;
import { ExploitEnum } from &#x27;../../shared/enum/exploit.enum&#x27;;
import { ILead } from &#x27;../../shared/services/lead/interfaces/lead.interface&#x27;;
import * as Bluebird from &#x27;bluebird&#x27;;
import * as moment from &#x27;moment&#x27;;
import { OrgchartClient } from &#x27;../../mgs-sender/orgchart.client&#x27;;
import { CmdPatternConst } from &#x27;../../shared/constant/cmd-pattern.const&#x27;;

const validReportStatus &#x3D; [ExploitEnum.DONE, ExploitEnum.CANCEL];

@Injectable()
export class QueryRepository {


    constructor(
        @Inject(CommonConst.QUERY_MODEL_TOKEN)
        private readonly readModel: Model&lt;ILeadDocument&gt;,
        private readonly employeeRepository: EmployeeQueryRepository,
        private readonly orgchartClient: OrgchartClient,
    ) { }

    async findAll(): Promise&lt;ILeadDocument[]&gt; {

        return await this.readModel.find()
            .exec()
            .then(result &#x3D;&gt; {
                return result;
            });
    }

    async findAllCommon(query): Promise&lt;ILeadDocument[]&gt; {
        const match: any &#x3D; {
            source : {
                $nin: [
                    SourceEnum.DangTin,
                    SourceEnum.YeuCau
            ]},
            type: query.type
        };
        if (!_.isEmpty(query.keywords)) {
            match[&#x27;name&#x27;] &#x3D; { $regex: new RegExp(query.keywords), $options: &#x27;i&#x27; };
        }
        return await this.readModel.aggregate([
            {
                $match: match
            },
            {
                $lookup: {
                    from: &#x27;employees&#x27;,
                    localField: &#x27;processBy&#x27;,
                    foreignField: &#x27;id&#x27;,
                    as: &#x27;employeeTakeCare&#x27;
                }
            }

        ]).allowDiskUse(true)
            .exec();
    }

    async findAllAdvising(): Promise&lt;ILeadDocument[]&gt; {
        return await this.readModel.aggregate([

            {
                $match: {
                    type: CommonConst.TYPE.ADVISING
                }
            },
            {
                $lookup: {
                    from: &#x27;employees&#x27;,
                    localField: &#x27;processBy&#x27;,
                    foreignField: &#x27;id&#x27;,
                    as: &#x27;employeeTakeCare&#x27;
                }
            }
        ])
            .allowDiskUse(true)
            .exec();
    }

    async findAllPrimary(user, query: any): Promise&lt;any&gt; {
        const match: any &#x3D; {};
        const matchKeywords: any &#x3D; {};
        let sort: any &#x3D; {
            code: 1
        };
        // match.type &#x3D; CommonConst.TYPE.PRIMARY;
        match.$and &#x3D; [];
        match.$and.push({type: CommonConst.TYPE.PRIMARY});
        let lstPosId: any[] &#x3D; [];
        let employeeLogIn: any;

        if (user.roles.includes(PermissionConst.LEAD_GET_ALL_SUPER_ADMIN) || user.roles.includes(PermissionConst.LEAD_GET_ALL_COMPANY) || user.roles.includes(PermissionConst.LEAD_GET_ALL_UNIT)) {
            const emp &#x3D; await this.employeeRepository.findOne({id: user.id});
            employeeLogIn &#x3D; emp;
            if (!query || (query &amp;&amp; !query.posId &amp;&amp; !query.exchangeId)) {
                if (!user.roles.includes(PermissionConst.LEAD_GET_ALL_SUPER_ADMIN)) {
                    let typePosUser: any;
                    if (employeeLogIn &amp;&amp; employeeLogIn.pos &amp;&amp; employeeLogIn.pos.type) {
                        typePosUser &#x3D; employeeLogIn.pos.type;
                    }
                    if (user.roles.includes(PermissionConst.LEAD_GET_ALL_COMPANY) 
                        || (user.roles.includes(PermissionConst.LEAD_GET_ALL_UNIT) &amp;&amp; typePosUser &amp;&amp; typePosUser &#x3D;&#x3D;&#x3D; &#x27;ORG&#x27;)) {
                        // user được gán permission quản lý cấp công ty hoặc user được gán permission quản lý cấp đơn vị và thuộc cấp cao
                        // call orgchart get lst pos
                        if (employeeLogIn.pos &amp;&amp; employeeLogIn.pos.id) {
                            lstPosId &#x3D; await this.orgchartClient.sendDataPromise(employeeLogIn.pos.id, CmdPatternConst.LISTENER.ORGCHART_GET_LST_POS_ID_BY_USER);
                            match.$and.push({$or: [
                                { &#x27;pos.id&#x27;: { $in: lstPosId } },
                                { &#x27;pos.parentId&#x27;: { $in: lstPosId } }
                            ]});
                        }
                    } else {
                        // user được gán permission quản lý cấp đơn vị và không thuộc cấp cao
                        if (typePosUser) {
                            if (typePosUser &#x3D;&#x3D;&#x3D; &#x27;SAN&#x27;) {
                                // user thuộc sàn giao dịch
                                match.$and.push({$or: [
                                    { &#x27;pos.id&#x27;: employeeLogIn.pos.id },
                                    { &#x27;pos.parentId&#x27;: employeeLogIn.pos.id }
                                ]});
                            } else {
                                // user thuộc team/ POS
                                match.$and.push({&#x27;pos.id&#x27;: employeeLogIn.pos.id});
                            }
                        }
                    }
    
                }
            }
        } else {
            if (!user.roles.includes(PermissionConst.LEAD_REPORT_VIEW_ALL)) {
                if (user &amp;&amp; user.id) {
                    if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_EMP)) {
                        const emp &#x3D; await this.employeeRepository.findOne({id: user.id});
                        employeeLogIn &#x3D; emp;
                        const $or &#x3D; [
                          {processBy: {$nin: [null, &#x27;&#x27;]}}, {processBy: {$in: emp.staffIds}},
                          {&#x27;takeCare.id&#x27;: {$nin: [null, &#x27;&#x27;]}}, {&#x27;takeCare.id&#x27;: {$in: emp.staffIds}}
                        ]
                        match.$and.push({$or});
                    } else if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_IMPORTER)) {
                        match.$and.push({&#x27;importedBy.id&#x27;: user.id});
                    } else {
                        const $or &#x3D; [
                          {processBy: user.id},
                          {&#x27;takeCare.id&#x27;: user.id}
                        ]
                        match.$and.push({$or});
                    }
                } else {
                   return [];
                }
            } else {
                const emp &#x3D; await this.employeeRepository.findOne({id: user.id});
                employeeLogIn &#x3D; emp;
                match.$and.push({&#x27;importedBy.id&#x27;: {$in: emp.staffIds}});
            }
        }

        if (query) {
            if (!_.isEmpty(query.query)) {
                // matchKeywords.$or &#x3D; [
                //     { &#x27;name&#x27;: { $regex: query.query, $options: &#x27;i&#x27; } },
                //     { &#x27;phone&#x27;: { $regex: query.query, $options: &#x27;i&#x27; } },
                //     { &#x27;code&#x27;: { $regex: query.query, $options: &#x27;i&#x27; } },
                //     { &#x27;updatedName&#x27;: { $regex: query.query, $options: &#x27;i&#x27; } },
                //     { &#x27;updatedPhone&#x27;: { $regex: query.query, $options: &#x27;i&#x27; } },
                //     { &#x27;note&#x27;: { $regex: query.query, $options: &#x27;i&#x27; } },
                // ];

                matchKeywords.$or &#x3D; [
                    { &#x27;employee.name&#x27;: { $regex: query.query, $options: &#x27;i&#x27; } },
                    { &#x27;takeCare.name&#x27;: { $regex: query.query, $options: &#x27;i&#x27; } },
                    { &#x27;employee.phone&#x27;: { $regex: query.query, $options: &#x27;i&#x27; } },
                    { &#x27;takeCare.phone&#x27;: { $regex: query.query, $options: &#x27;i&#x27; } },
                    { &#x27;pos.name&#x27;: { $regex: query.query, $options: &#x27;i&#x27; }},
                    {&#x27;source&#x27;: { $regex: query.query, $options: &#x27;i&#x27; }}
                ];
            }
            if (!_.isEmpty(query[&#x27;resource&#x27;])) {
                match[&#x27;source&#x27;] &#x3D; query[&#x27;resource&#x27;];
            }

            if (!_.isEmpty(query.employeeId)) {
                match.$and.push({$or: [
                    {processBy: query.employeeId},
                    {&#x27;takeCare.id&#x27;: query.employeeId},
                ]});
            } else if (!_.isEmpty(query.posId)) {
                match.$and.push({&#x27;pos.id&#x27;: query.posId});
            } else if (!_.isEmpty(query.exchangeId)) {
                match.$and.push({$or: [
                    {&#x27;pos.id&#x27;: query.exchangeId},
                    {&#x27;pos.parentId&#x27;: query.exchangeId}
                ]});
            }
            if (query.leadRepository) match.repoId &#x3D; query.leadRepository;
            if (query.leadRepositoryConfig) {
                if (query.leadRepositoryConfig &#x3D;&#x3D;&#x3D; &#x27;hot&#x27;) match.isHot &#x3D; true;
                else match.repoConfigCode &#x3D; query.leadRepositoryConfig;
            }

            if (!_.isEmpty(query.assignedDateFrom)) {
                match.$and.push({assignedDate: {$gte: new Date(parseInt(query.assignedDateFrom, 10))}});
            }

            if (!_.isEmpty(query.assignedDateTo)) {
                match.$and.push({assignedDate: {$lte: new Date(parseInt(query.assignedDateTo, 10))}});
            }

            if (!_.isEmpty(query.sort)) {
                sort &#x3D; this.transformSort(query.sort) || {
                    code: 1
                };
            }

            // if (!_.isEmpty(query[&#x27;createdFrom&#x27;]) || !_.isEmpty(query[&#x27;createdTo&#x27;])) {
            //     match[&#x27;createdDate&#x27;] &#x3D; {};
            //     if (!_.isEmpty(query[&#x27;createdFrom&#x27;])) {
            //         match[&#x27;createdDate&#x27;][&#x27;$gte&#x27;] &#x3D; new Date(parseInt(query[&#x27;createdFrom&#x27;]));
            //     }
            //     if (!_.isEmpty(query[&#x27;createdTo&#x27;])) {
            //         match[&#x27;createdDate&#x27;][&#x27;$lte&#x27;] &#x3D; new Date(parseInt(query[&#x27;createdTo&#x27;]));
            //     }
            // }

        }

        const aggregate: any[] &#x3D; [
            {
                $match: matchKeywords
            },
            {
                $match: match
            },
            { $sort: sort },
        ]
        if (!_.isEmpty(query[&#x27;page&#x27;]) || !_.isEmpty(query[&#x27;pageSize&#x27;])) {
            const page: number &#x3D; parseInt(query[&#x27;page&#x27;]) || 1;
            const pageSize: number &#x3D; parseInt(query[&#x27;pageSize&#x27;]) || 10;
            aggregate.push({
                $facet: {
                    rows: [{ $skip: Math.floor((pageSize * page) - pageSize) }, { $limit: pageSize }],
                    totalCount: [
                        {
                            $count: &#x27;count&#x27;
                        }
                    ]
                }
            });
        }
        // console.log(JSON.stringify(aggregate));
        return await this.readModel.aggregate(aggregate)
            .allowDiskUse(true)
            .exec();
    }
    async getByCustomer(customerId) {
        return await this.readModel.find({ customerId })
            .sort({createdDate: -1})
            .exec()
            .then(result &#x3D;&gt; {
                return result;
            });
    }

    async getAllByPos(posId: String): Promise&lt;ILeadDocument[]&gt; {

        return await this.readModel.aggregate([
            { $match: { &#x27;pos.id&#x27;: posId } },
            { $sort: { createDate: 1 } }
        ]).allowDiskUse(true)
            .exec()
            .then(rs &#x3D;&gt; {
                return rs;
            })
            .catch(err &#x3D;&gt; {
                return err;
            });
    }

    async findOne(query): Promise&lt;ILeadDocument&gt; {

        return await this.readModel.findOne(query)
            .exec()
            .then(result &#x3D;&gt; {
                return result;
            });
    }

    async findLeadById(id: string): Promise&lt;ILeadDocument&gt; {
        return await this.readModel.aggregate(
            [
                {
                    $match: { id }
                },
                {
                    $lookup: {
                        from: &#x60;${CommonConst.LEAD_REPO_COLLECTION}s&#x60;,
                        localField: &#x27;repoId&#x27;,
                        foreignField: &#x27;id&#x27;,
                        as: &#x27;configData&#x27;
                    }
                }
            ]).allowDiskUse(true)
            .exec()
            .then((response) &#x3D;&gt; {
                if (response.length &gt; 0) {
                    if  (response[0].configData.length){
                        response[0].configData &#x3D; response[0].configData[0].configs.find((item) &#x3D;&gt; item.code &#x3D;&#x3D;&#x3D; response[0].repoConfigCode);
                        response[0].visiblePhone &#x3D; response[0].configData?.visiblePhone;
                    } else {
                        response[0].configData &#x3D; null;
                    }
                    return response[0];
                }
                return null;
            })
            .catch((exp) &#x3D;&gt; {
                return exp;
            });
    }

    async findAggregateModelById(id: string): Promise&lt;QueryAggregateModel&gt; {
        return await this.readModel.findOne({ id })
            .exec()
            .then((response) &#x3D;&gt; {
                console.log(&#x27;findAggregateModelById lead query side&#x27;);
                return new QueryAggregateModel(id);
            })
            .catch((exp) &#x3D;&gt; {
                return exp;
            });
    }

    async create(readmodel): Promise&lt;ILeadDocument&gt; {
        return await this.readModel.create(readmodel)
            .then((response) &#x3D;&gt; {
                console.log(&#x27;createEvent Lead at query side&#x27;);
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    async update(model): Promise&lt;ILeadDocument&gt; {
        model.updatedDate &#x3D; Date.now();
        return await this.readModel.update({ id: model.id }, model)
            .then((response) &#x3D;&gt; {
                console.log(&#x27;update Lead at query side&#x27;);
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    async checkDuplicate(query): Promise&lt;boolean&gt; {

        return await this.readModel.aggregate(
            [
                { $match: { name: query.name } }
            ]).allowDiskUse(true)
            .exec()
            .then((result) &#x3D;&gt; {
                if (result &amp;&amp; result.length &gt; 0) {
                    return true;
                }
                return false;
            });
    }

    async checkDuplicateUpdate(query): Promise&lt;boolean&gt; {

        return await this.readModel.aggregate(
            [
                {
                    $match: {
                        $and: [
                            { name: query.name },
                            { id: { $ne: query.id } }
                        ]
                    }
                }
            ]).allowDiskUse(true)
            .exec()
            .then((result) &#x3D;&gt; {
                if (result &amp;&amp; result.length &gt; 0) {
                    return true;
                }
                return false;
            })
            .catch((error) &#x3D;&gt; {
                throw error;
            });
    }

    async assignLeadForEmployee(model) {
        return await this.readModel.updateOne(
            { id: model.id },
            {
                $set: {
                    processBy: model.processBy,
                    timeOut: model.timeOut,
                    serverTime: model.serverTime,
                    utcTime: model.utcTime
                }
            },
        )
            .exec()
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    // Update status
    async updateStatus(model) {
        return await this.readModel.updateMany(
            { id: { $in: model.ids } },
            { $set: { status: model.status } },
            { multi: true }
        )
            .exec()
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    async findLeadsProcessBy(loggedUser, type) {
        const match: any &#x3D; {};
        if (type) {
            match.type &#x3D; type;
        }

         // get employee 
         const employee &#x3D; await this.employeeRepository.findOne( { &quot;id&quot;: loggedUser.id} );
         if (!employee) {
             return [];
         }
        return await this.readModel.aggregate([
            { $match: { match, 
                &#x27;lead.processBy&#x27;: {
                    $exists: true, $nin: [null, &quot;&quot;],
                    &#x27;$in&#x27;: employee.staffIds
                    }
                } 
            },
            // {
            //     $lookup: {
            //         from: &#x27;employees&#x27;,
            //         let: { processBy: &#x27;$processBy&#x27; },
            //         pipeline: [
            //             {
            //                 $match:
            //                 {
            //                     $expr:
            //                     {
            //                         $and:
            //                             [
            //                                 { $in: [&#x27;$$processBy&#x27;, &#x27;$staffIds&#x27;] },
            //                                 { $eq: [loggedUser.id, &#x27;$id&#x27;] },
            //                             ]
            //                     }
            //                 }
            //             },
            //         ],
            //         as: &#x27;data&#x27;
            //     }
            // },
            // { $unwind: &#x27;$data&#x27; },


            // {
            //     $project: {
            //         status: &#x27;$$ROOT.status&#x27;,
            //         description: &#x27;$$ROOT.description&#x27;,
            //         active: &#x27;$$ROOT.active&#x27;,
            //         modifiedBy: &#x27;$$ROOT.modifiedBy&#x27;,
            //         employeeTakeCare: &#x27;$$ROOT.employeeTakeCare&#x27;,
            //         customer: &#x27;$$ROOT.customer&#x27;,
            //         pos: &#x27;$$ROOT.pos&#x27;,
            //         ticketId: &#x27;$$ROOT.ticketId&#x27;,
            //         resource: &#x27;$$ROOT.resource&#x27;,
            //         id: &#x27;$$ROOT.id&#x27;,
            //         createDate: &#x27;$$ROOT.createDate&#x27;,
            //         // employee: &#x27;$data&#x27; // if you want to get employee
            //     }
            // },
            { $sort: { createDate: 1 } },
            {
                $project: {
                    info: &#x27;$$ROOT&#x27;,
                    isOwner: {
                        $cond: {
                            if: { $eq: [loggedUser.id, &#x27;$processBy&#x27;] },
                            then: &#x27;owners&#x27;,
                            else: &#x27;others&#x27;
                        }
                    }
                }
            },
            {
                $group:
                {
                    _id: &#x27;$isOwner&#x27;,
                    list: { $push: &#x27;$info&#x27; }
                }
            }
        ]).allowDiskUse(true)
            .exec()
            .then(rs &#x3D;&gt; {
                const result &#x3D; {};
                rs.forEach(e &#x3D;&gt; {
                    result[e._id] &#x3D; e.list;
                });
                return result;
            })
            .catch(err &#x3D;&gt; {
                return err;
            });
    }
    /**
     *
     * @param loggedUser user
     * @param {other || own }processType
     * @param query
     */
    async listLeadsProcessBy(loggedUser: any, query: any) {
        const processType &#x3D; query.processType || &#x27;other&#x27;;
        const limit &#x3D; Number(query.pageSize) || 10;
        const skip &#x3D; (Number(query.page) || 1) * limit - limit;
        const q: any &#x3D; {
            processBy: processType &#x3D;&#x3D;&#x3D; &#x27;other&#x27; ? { $nin: [loggedUser.id, null] } : loggedUser.id,
            lifeCycleStatus: { $nin: [LifeCycleStatusEnum.REMOVED, LifeCycleStatusEnum.PRIMARY_PROCESSING] },
            timeOut: { &quot;$gt&quot; : Date.now() } 

        };
        if (query.type){
            q.type &#x3D; query.type;
        } else {
            q.type &#x3D; { $ne : CommonConst.TYPE.PRIMARY}
        }
        let sort: any &#x3D; {
            createdDate: -1
        };
        if (!_.isEmpty(query.sort)) {
            sort &#x3D; this.transformSort(query.sort);
        }
        return await this.readModel.find(q)
            .sort(sort)
            .skip(skip)
            .limit(limit)
            .exec();
    }
    async count(query: any &#x3D; {}) {
        return this.readModel.countDocuments(query).exec();
    }
    async isValidBeforePull(employeeId, type: string) {
        const match: any &#x3D; { 
            processBy: employeeId, 
            lifeCycleStatus: { $nin: [LifeCycleStatusEnum.PENDING, LifeCycleStatusEnum.REMOVED, LifeCycleStatusEnum.COMPLETED, LifeCycleStatusEnum.PRIMARY_PROCESSING] },
            timeOut: { &quot;$gt&quot; : Date.now() }
        };
        if (type) {
            match.type &#x3D; type;
        } else {
            match.type &#x3D; { $ne: CommonConst.TYPE.PRIMARY}
        }
        return await this.readModel.find(match)
            .exec()
            .then(rs &#x3D;&gt; {
                if (rs &amp;&amp; rs.length &gt; 0) return false;
                return true;
            })
            .catch(err &#x3D;&gt; {
                return err;
            });
    }

    async findLeadsToAssign(employeeId: string, posId: string, exchangeId: string, type: string, shared: boolean &#x3D; false) {
        const match: any &#x3D; {
            processBy: null,
            $or: [{&#x27;pos.id&#x27;: posId},
                    {&#x27;pos.id&#x27;: exchangeId}
                ],
            // &#x27;processedHistory.processBy&#x27;: { $nin: [employeeId] },
            // &#x27;processedHistory.isReject&#x27;: { $ne: true },
            lifeCycleStatus: LifeCycleStatusEnum.IN_POOL,
        };
        if (shared) {
            match.$or.push({&#x27;pos.id&#x27;: &#x27;dxs-shared&#x27;});
        }
        if (type) {
            match.type &#x3D; type;
        } else {
            match.type &#x3D; { $ne: CommonConst.TYPE.ADVISING };
        }

        return await this.readModel.aggregate([
            {
                $match: match
            },
            { $sort: { createdDate: 1 } },
            { $limit: 1 }   // Update: chỉ cho phép xử lý yêu cầu mỗi lần lấy
        ]).allowDiskUse(true)
            .exec()
            .then(rs &#x3D;&gt; {
                return rs;
            })
            .catch(err &#x3D;&gt; {
                return err;
            });
    }

    async processingLead(id: String) {
        let lifeCycleStatus &#x3D; LifeCycleStatusEnum.PROCESSING;
        const model &#x3D; await this.readModel.findOne({id});
        if (model.type &#x3D;&#x3D;&#x3D; CommonConst.TYPE.PRIMARY) {
            lifeCycleStatus &#x3D; LifeCycleStatusEnum.PRIMARY_PROCESSING;
        }
        return await this.readModel.updateOne(
            { id },
            {
                $set: {
                    updatedDate: Date.now(),
                    lifeCycleStatus
                }
            }
        )
            .exec()
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    async unprocessingLead(model) {
        return await this.readModel.updateOne(
            { id: model.id },
            {
                $set: {
                    updatedDate: Date.now(),
                    lifeCycleStatus: LifeCycleStatusEnum.ASSIGNED, note: model.note
                }
            }
        )
            .exec()
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    async delete(id: string) {
        return await this.readModel.deleteOne({ id })
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }
    async deleteMany() {
        return await this.readModel.deleteMany({})
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    async assignLead(model) {
        return await this.readModel.updateOne(
            { id: model.id },
            {
                $set: {
                    updatedDate: Date.now(),
                    processBy: model.processBy,
                    timeOut: model.timeOut,
                    lifeCycleStatus: model.lifeCycleStatus,
                    timezoneclient: model.timezoneclient,
                    assignedDate: Date.now(),
                },
                $inc: { countAssign: 1 }
            },
        )
            .exec()
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    async isReadyToAssign(leadId: string) {
        return await this.readModel.find({ id: leadId, processBy: null })
            .exec()
            .then(result &#x3D;&gt; {
                if (isNullOrUndefined(result)) return false;

                return true;
            });
    }

    async isValidMarkProcessing(employeeId: string, leadId) {
        return await this.readModel.find({ processBy: employeeId, lifeCycleStatus: LifeCycleStatusEnum.PROCESSING, id: leadId })
            .exec()
            .then(result &#x3D;&gt; {
                if (isNullOrUndefined(result) || result.length &#x3D;&#x3D;&#x3D; 0)
                    return true;
                return false;
            });
    }



    // Reassign
    async findLeadsAssignedEmployee(employeeId: string) {
        return await this.readModel.find({ processBy: employeeId })
            .exec()
            .then(rs &#x3D;&gt; {
                return rs;
            })
            .catch(err &#x3D;&gt; {
                return err;
            });
    }

    async findLeadsAssigned() {
        return await this.readModel.find({ processBy: { $ne: null }, lifeCycleStatus: { $nin: [LifeCycleStatusEnum.REMOVED, LifeCycleStatusEnum.PRIMARY_PROCESSING] } })
            .exec()
            .then(rs &#x3D;&gt; {
                return rs;
            })
            .catch(err &#x3D;&gt; {
                return err;
            });
    }

    async reassignLead(model: any) {
        return await this.readModel.updateOne(
            { id: model.id },
            {
                $set:
                {
                    updatedDate: Date.now(),
                    modifiedBy: model.modifiedBy,
                    processBy: model.processBy,
                    timeOut: null,
                    lifeCycleStatus: model.lifeCycleStatus,
                    pos: model.pos,
                    processedHistory: model.processedHistory,
                }
            }
        )
            .exec()
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    async failLead(model: any) {
        return await this.readModel.updateOne(
            { id: model.id },
            {
                $set:
                {
                    updatedDate: Date.now(),
                    modifiedBy: model.modifiedBy,
                    processBy: model.processBy,
                    timeOut: null,
                    lifeCycleStatus: model.lifeCycleStatus,
                    pos: model.pos,
                    processedHistory: model.processedHistory
                }
            }
        )
            .exec()
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }
    async updateOne(model: any) {
        return await this.readModel.updateOne(
            { id: model.id },
            {
                $set: model
            }
        )
            .exec()
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    async expiredLead(model: any) {
        return await this.readModel.updateOne(
            { id: model.id },
            {
                $set:
                {
                    updatedDate: Date.now(),
                    modifiedBy: model.modifiedBy,
                    processBy: model.processBy,
                    timeOut: null,
                    lifeCycleStatus: model.lifeCycleStatus,
                    processedHistory: model.processedHistory,
                },
                $inc: { countAssign: 1 },
            }
        )
            .exec()
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    // Pending
    async pendingLead(model) {
        return await this.readModel.updateOne(
            { id: model.id },
            {
                $set: {
                    updatedDate: Date.now(),
                    lifeCycleStatus: LifeCycleStatusEnum.PENDING,
                    note: model.note
                }
            },
        )
            .exec()
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    async countLifeCycleStatusByEmployee(employeeId: String, lifeCycleStatus: string) {
        return await this.readModel.find(
            {
                processBy: employeeId,
                lifeCycleStatus,
            })
            .countDocuments()
            .then(rs &#x3D;&gt; {
                return rs;
            })
            .catch(ex &#x3D;&gt; {
                return ex;
            });
    }

    async countLifeCycleStatusForLeads(leadIds, lifeCycleStatus) {
        return await this.readModel.find(
            {
                id: { $in: leadIds },
                lifeCycleStatus,
            })
            .countDocuments()
            .then(rs &#x3D;&gt; {
                return rs;
            })
            .catch(ex &#x3D;&gt; {
                return ex;
            });
    }
    async countAllTickets() {
        return this.readModel.countDocuments().exec()
            .then(result &#x3D;&gt; {
                return { &#x27;totalTickets&#x27;: result };
            });
    }

    async countProcessTicketsDemandByUser(query) {
        return await this.readModel.find(
            {
                $and: [
                    query,
                    {
                        $or: [
                            { type: &#x27;BUY&#x27; },
                            { type: &#x27;RENT&#x27; },
                        ]
                    }
                ]
            },
        ).countDocuments()
            .exec()
            .then(demand &#x3D;&gt; {
                return demand;
            });
    }
    async countProcessTicketsConsignmentByUser(query) {
        return await this.readModel.find(
            {
                $and: [
                    query,
                    {
                        $or: [
                            { type: &#x27;SELL&#x27; },
                            { type: &#x27;LEASE&#x27; }
                        ]
                    }
                ]
            },
        ).countDocuments()
            .exec()
            .then(consignment &#x3D;&gt; {
                return consignment;
            });
    }
    async countLeadsProcessBy(loggedUser: any, query: any &#x3D; {}): Promise&lt;number&gt; {
        const processType &#x3D; query.processType || &#x27;other&#x27;;
        const q &#x3D; {
            processBy: processType &#x3D;&#x3D;&#x3D; &#x27;other&#x27; ? { $nin: [loggedUser.id, null] } : loggedUser.id,
            lifeCycleStatus: { $ne: LifeCycleStatusEnum.REMOVED },
            timeOut: { &quot;$gt&quot; : Date.now() } 
        };
        return await this.readModel.countDocuments(q).exec();
    }
    /**
     *
     * @param page
     * @param pageSize
     * @param query
     */
    async listAllCommon(user: any, page: number &#x3D; 1, pageSize: number &#x3D; 10, query: any &#x3D; {}): Promise&lt;ILeadDocument[]&gt; {
        let sort: any &#x3D; {
            createdDate: -1
        };
        const match: any &#x3D; {};
        const agg &#x3D; [];
        match.$and &#x3D; [];

        match.$and.push({lifeCycleStatus: { $ne: LifeCycleStatusEnum.REMOVED }});
        const emp &#x3D; await this.employeeRepository.findOne({id: user.id});
        if (!user.roles.includes(PermissionConst.LEAD_REPORT_VIEW_ALL)) {
            if (user &amp;&amp; user.id) {
                if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_EMP)) {                   
                    match.$and.push({processBy: {$nin: [null, &#x27;&#x27;]}}, {processBy: {$in: emp.staffIds}});
                } else if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_IMPORTER)) {
                    match.$and.push({&#x27;importedBy.id&#x27;: user.id});
                } else {
                    return [];
                }
            } else {
               return [];
            }
        } else {
          match.$and.push({&#x27;importedBy.id&#x27;: {$in: emp.staffIds}});
        }

        if (!_.isEmpty(query.resource)) {
            match.source &#x3D; query.resource;
        } else {
            match.source &#x3D; {
                $nin: [
                    SourceEnum.DangTin,
                    SourceEnum.YeuCau
            ]};
        }

        if (!_.isEmpty(query.type)) {
            match.type &#x3D; { $in: query.type.split(&#x27;,&#x27;) };
        }
        if (!_.isEmpty(query.posId)) {
            match[&#x27;pos.id&#x27;] &#x3D; query.posId;
        }

        if (!_.isEmpty(query.exchangeId)) {
            match.$and.push({$or: [
                {&#x27;pos.id&#x27;: query.exchangeId},
                {&#x27;pos.parentId&#x27;: query.exchangeId}
            ]});
        }

        if (!_.isEmpty(query.assignedDateFrom)) {
            match.$and.push({assignedDate: {$gte: new Date(parseInt(query.assignedDateFrom, 10))}});
        }

        if (!_.isEmpty(query.assignedDateTo)) {
            match.$and.push({assignedDate: {$lte: new Date(parseInt(query.assignedDateTo, 10))}});
        }

        if (!_.isEmpty(query[&#x27;createdFrom&#x27;]) || !_.isEmpty(query[&#x27;createdTo&#x27;])) {
            match[&#x27;createdDate&#x27;] &#x3D; {};
            if (!_.isEmpty(query[&#x27;createdFrom&#x27;])) {
                match[&#x27;createdDate&#x27;][&#x27;$gte&#x27;] &#x3D; new Date(parseInt(query[&#x27;createdFrom&#x27;]));
            }
            if (!_.isEmpty(query[&#x27;createdTo&#x27;])) {
                match[&#x27;createdDate&#x27;][&#x27;$lte&#x27;] &#x3D; new Date(parseInt(query[&#x27;createdTo&#x27;]));
            }
        }
        if (!_.isEmpty(query.lifeCycleStatus)) {
            match.lifeCycleStatus &#x3D; { $in: query.lifeCycleStatus.split(&#x27;,&#x27;) };
        } else {
            agg.push(
                {
                    $lookup: {
                        from: &#x27;employees&#x27;,
                        localField: &#x27;processBy&#x27;,
                        foreignField: &#x27;id&#x27;,
                        as: &#x27;employeeTakeCare&#x27;
                    }
                });
        }
        let matchKeywords: any &#x3D; {};
        if (!_.isEmpty(query.q)) {
            matchKeywords[&#x27;$or&#x27;] &#x3D; [
                { $and: [{ &#x27;name&#x27;: { $regex: query.q, $options: &#x27;i&#x27; } }, { $or: [{ type: &#x27;BUY&#x27; }, { type: &#x27;RENT&#x27; }] }] },
                { &#x27;pos.name&#x27;: { $regex: query.q, $options: &#x27;i&#x27; } },
                { $and: [{ &#x27;property.address&#x27;: { $regex: query.q, $options: &#x27;i&#x27; } }, { $or: [{ type: &#x27;SELL&#x27; }, { type: &#x27;LEASE&#x27; }] }] },
                { $and: [{ &#x27;category.name&#x27;: { $regex: query.q, $options: &#x27;i&#x27; } }, { $or: [{ type: &#x27;SELL&#x27; }, { type: &#x27;LEASE&#x27; }] }] },
            ];
        }
        if (!_.isEmpty(query.sort)) {
            sort &#x3D; this.transformSort(query.sort) || {
                createdDate: -1
            };
        }
        
        agg.push({
            $addFields: {
                callHistoryCount: {
                    $cond: [{ $isArray: &#x27;$callHistory&#x27; }, {
                        $size: {
                            $filter: {
                                input: &quot;$callHistory&quot;,
                                as: &quot;e&quot;,
                                cond: { $eq: [&quot;$$e.isCalled&quot;, true] }
                            }
                        }
                    }, 0]
                },
                callHistoryMinuteCount: {
                    $cond: [{ $isArray: &#x27;$callHistory&#x27; }, {
                        $sum: &quot;$callHistory.answerTime&quot;
                    }, 0]
                }
            }
        });
        agg.push({
            $match: match
        },
        {
            $match: matchKeywords
        },
        {
            $sort: sort
        },
        {
            $facet: {
                rows: [{ $skip: Math.floor((pageSize * page) - pageSize) }, { $limit: pageSize }],
                totalCount: [
                    {
                        $count: &#x27;count&#x27;
                    }
                ]
            }
        });

        return await this.readModel.aggregate(agg).allowDiskUse(true)
            .exec();
    }
    /**
     * Lấy danh sách yêu cầu tư vấn dịch vụ
     * @param page
     * @param pageSize
     * @param query
     */
    async listAllAdvising(page: number &#x3D; 1, pageSize: number &#x3D; 10, query): Promise&lt;ILeadDocument[]&gt; {
        let sort: any &#x3D; {
            createdDate: - 1
        };
        if (!_.isEmpty(query.sort)) {
            sort &#x3D; this.transformSort(query.sort) || {
                createdDate: - 1
            };
        }
        return await this.readModel.aggregate([
            {
                $lookup: {
                    from: &#x27;employees&#x27;,
                    localField: &#x27;processBy&#x27;,
                    foreignField: &#x27;id&#x27;,
                    as: &#x27;employeeTakeCare&#x27;
                }
            },
            {
                $match: {
                    type: CommonConst.TYPE.ADVISING
                }
            },
            { $sort : sort },
            { $skip: (page * pageSize) - pageSize },
            { $limit: pageSize }
        ])
            .allowDiskUse(true)
            .exec();
    }

    /**
     *
     * @param {Array&lt;String&gt;} customerId
     */
    findLeadByCustomerId(customerId: string[] &#x3D; [], mapping: boolean &#x3D; false) {
        return this.readModel.find({ customerId: { $in: customerId, $exists: true, $nin: [null, &#x27;&#x27;] } }).exec()
            .then((res) &#x3D;&gt; {
                if (mapping) {
                    return _.groupBy(res, &#x27;customerId&#x27;);
                }
                return res;
            });
    }
    protected transformSort(paramSort?: String) {
        let sort: any &#x3D; paramSort;
        if (_.isString(sort)) {
            sort &#x3D; sort.split(&#x27;,&#x27;);
        }
        if (Array.isArray(sort)) {
            let sortObj &#x3D; {};
            sort.forEach(s &#x3D;&gt; {
                if (s.startsWith(&#x27;-&#x27;))
                    sortObj[s.slice(1)] &#x3D; -1;
                else
                    sortObj[s] &#x3D; 1;
            });
            return sortObj;
        }

        return sort;
    }
    async filter(query: any &#x3D; {}, isPaging: boolean &#x3D; false) {
        const limit &#x3D; parseInt(query[&#x27;pageSize&#x27;]) || 10;
        const skip &#x3D; (parseInt(query[&#x27;page&#x27;]) || 1) * limit - limit;
        const { page, pageSize, ...q } &#x3D; query;
        let sort: any &#x3D; {
            createdDate: -1
        };
        if (!_.isEmpty(query.sort)) {
            sort &#x3D; this.transformSort(query.sort);
        }
        const aggregate &#x3D; [
            {
                $match: q
            },
            {
                $lookup: {
                    from: &#x27;customers&#x27;,
                    localField: &#x27;customerId&#x27;,    // field in the orders collection
                    foreignField: &#x27;id&#x27;,  // field in the items collection
                    as: &#x27;customers&#x27;
                }
            },
            {
                $lookup: {
                    from: &#x27;employees&#x27;,
                    localField: &#x27;processBy&#x27;,    // field in the orders collection
                    foreignField: &#x27;id&#x27;,  // field in the items collection
                    as: &#x27;employees&#x27;
                }
            },
            {
                $addFields: {
                    customer: {
                        $ifNull: [&#x27;$customer&#x27;, { $arrayElemAt: [&#x27;$customers&#x27;, 0] }]
                    },
                    employeeTakeCare: { $arrayElemAt: [&#x27;$employees&#x27;, 0] }
                }
            },
            {
                $project: {
                    customers: 0,
                    employees: 0
                }
            }
        ];
        if (isPaging) {
            return await this.readModel.aggregate(aggregate)
                .sort(sort)
                .skip(skip)
                .limit(limit)
                .exec();
        } else {
            return await this.readModel.aggregate(aggregate)
                .sort(sort)
                .exec();
        }
    }
    async filterReport(user: any &#x3D; {}, query: any &#x3D; {}, posInPool: any[] &#x3D; [], isPaging: boolean &#x3D; false) {
        const limit &#x3D; parseInt(query[&#x27;pageSize&#x27;]) || 10;
        const skip &#x3D; (parseInt(query[&#x27;page&#x27;]) || 1) * limit - limit;
        const { page, pageSize, sort &#x3D; &#x27;&#x27;, ...q } &#x3D; query;
        let sortObject: any &#x3D; {
            createdDate: -1
        };
        if (!_.isEmpty(query.sort)) {
            sortObject &#x3D; this.transformSort(query.sort);
        }
        const aggregate &#x3D; [
            {
                $match: {
                    type: { $in: [TransactionTypeEnum.buy, TransactionTypeEnum.lease, TransactionTypeEnum.rent, TransactionTypeEnum.sell] },
                    lifeCycleStatus: { $in: [LifeCycleStatusEnum.ASSIGNED, LifeCycleStatusEnum.PROCESSING], },
                    processBy: { $exists: true, $nin: [null, &#x27;&#x27;] }
                }
            },
            {
                $lookup: {
                    from: &#x27;customers&#x27;,
                    let: { customerId: &#x27;$customerId&#x27; },
                    pipeline: [
                        {
                            $match: {
                                $expr:
                                {
                                    $and:
                                        [
                                            { $eq: [&#x27;$$customerId&#x27;, &#x27;$id&#x27;] },
                                        ]
                                }


                            }
                        },
                        {
                            $addFields: {
                                &#x27;personalInfo.phone&#x27;: {
                                    $cond: { if: { $ne: [&#x27;$modifiedBy&#x27;, user.id] }, then: &#x27;******&#x27;, else: &#x27;$personalInfo.phone&#x27; },
                                },
                                &#x27;phone&#x27;: {
                                    $cond: { if: { $ne: [&#x27;$modifiedBy&#x27;, user.id] }, then: &#x27;******&#x27;, else: &#x27;$phone&#x27; },
                                },
                            }
                        }
                    ],
                    as: &#x27;customers&#x27;
                }
            },
            {
                $lookup: {
                    from: &#x27;employees&#x27;,
                    let: { employeeId: &#x27;$processBy&#x27; },
                    pipeline: [
                        {
                            $match: {
                                $expr:
                                {
                                    $and:
                                        [
                                            { $in: [&#x27;$$employeeId&#x27;, &#x27;$staffIds&#x27;] },
                                            { $eq: [user.id, &#x27;$id&#x27;] },
                                        ]
                                }


                            }
                        },
                    ],
                    as: &#x27;employeeTakeCares&#x27;
                }
            },
            { $unwind: &#x27;$employeeTakeCares&#x27; },
            {
                $lookup: {
                    from: &#x27;employees&#x27;,
                    let: { employeeId: &#x27;$processBy&#x27; },
                    pipeline: [
                        {
                            $match: {
                                $expr:
                                {
                                    $and:
                                        [
                                            { $eq: [&#x27;$$employeeId&#x27;, &#x27;$id&#x27;] },
                                        ]
                                }


                            }
                        },
                    ],
                    as: &#x27;employeeTakeCare&#x27;
                }
            },
            { $unwind: &#x27;$employeeTakeCare&#x27; },
            {
                $addFields: {
                    customer: {
                        $ifNull: [&#x27;$customer&#x27;, { $arrayElemAt: [&#x27;$customers&#x27;, 0] }]
                    },
                }
            },
            {
                $project: {
                    customers: 0,
                    employees: 0,
                    phone: 0,
                    employeeTakeCaress: 0,
                    employeeTakeCares: 0
                }
            },
            {
                $match: q
            },
        ];
        if (isPaging) {
            return await this.readModel.aggregate(aggregate)
                .sort(sortObject)
                .skip(skip)
                .limit(limit)
                .allowDiskUse(true)
                .exec();
        } else {
            return await this.readModel.aggregate(aggregate)
                .sort(sortObject)
                .allowDiskUse(true)
                .exec();
        }
    }

    countReport(user: any &#x3D; {}, query: any &#x3D; {}, posInPool: string[] &#x3D; [], ) {
        const { page, pageSize, ...q } &#x3D; query;
        const aggregate &#x3D; [
            {
                $match: {
                    type: { $in: [TransactionTypeEnum.buy, TransactionTypeEnum.lease, TransactionTypeEnum.rent, TransactionTypeEnum.sell] },
                    pos: { $exists: true, $nin: [null, &#x27;&#x27;] },
                    $or: [{
                        lifeCycleStatus: { $in: [LifeCycleStatusEnum.ASSIGNED, LifeCycleStatusEnum.PROCESSING] },
                        processBy: { $exists: true, $nin: [null, &#x27;&#x27;] }
                    }, {
                        &#x27;lifeCycleStatus&#x27;: LifeCycleStatusEnum.IN_POOL,
                        &#x27;pos.id&#x27;: { $exists: true, $nin: [null, &#x27;&#x27;], $in: posInPool }
                    }]

                }
            },
            {
                $lookup: {
                    from: &#x27;customers&#x27;,
                    let: { customerId: &#x27;$customerId&#x27; },
                    pipeline: [
                        {
                            $match: {
                                $expr:
                                {
                                    $and:
                                        [
                                            { $eq: [&#x27;$$customerId&#x27;, &#x27;$id&#x27;] },
                                        ]
                                }


                            }
                        },
                    ],
                    as: &#x27;customers&#x27;
                }
            },
            {
                $lookup: {
                    from: &#x27;employees&#x27;,
                    let: { employeeId: &#x27;$processBy&#x27;, posId: &#x27;$pos.id&#x27; },
                    pipeline: [
                        {
                            $match: {
                                $expr:
                                {
                                    $and:
                                        [
                                            { $in: [&#x27;$$employeeId&#x27;, &#x27;$staffIds&#x27;] },
                                            { $eq: [user.id, &#x27;$id&#x27;] },
                                        ]
                                }


                            }
                        },
                    ],
                    as: &#x27;employeeTakeCares&#x27;
                }
            },
            { $unwind: &#x27;$employeeTakeCares&#x27; },
            {
                $lookup: {
                    from: &#x27;employees&#x27;,
                    let: { employeeId: &#x27;$processBy&#x27; },
                    pipeline: [
                        {
                            $match: {
                                $expr:
                                {
                                    $and:
                                        [
                                            { $eq: [&#x27;$$employeeId&#x27;, &#x27;$id&#x27;] },
                                        ]
                                }


                            }
                        },
                    ],
                    as: &#x27;employeeTakeCare&#x27;
                }
            },
            { $unwind: &#x27;$employeeTakeCare&#x27; },
            {
                $addFields: {
                    customer: {
                        $ifNull: [&#x27;$customer&#x27;, { $arrayElemAt: [&#x27;$customers&#x27;, 0] }]
                    },
                }
            },
            {
                $project: {
                    customers: 0,
                    employees: 0,
                    phone: 0,
                }
            },
            {
                $match: q
            },
        ];
        return this.readModel.aggregate(aggregate)
            .allowDiskUse(true)
            .exec();
    }
    countReportInPool(user: any &#x3D; {}, query: any &#x3D; {}, posInPool: string[] &#x3D; []) {
        const { page, pageSize, ...q } &#x3D; query;
        const aggregate &#x3D; [
            {
                $match: {
                    type: { $in: [TransactionTypeEnum.buy, TransactionTypeEnum.lease, TransactionTypeEnum.rent, TransactionTypeEnum.sell] },
                    pos: { $exists: true, $nin: [null, &#x27;&#x27;] },
                    $or: [{
                        &#x27;lifeCycleStatus&#x27;: LifeCycleStatusEnum.IN_POOL,
                        &#x27;pos.id&#x27;: { $exists: true, $nin: [null, &#x27;&#x27;], $in: posInPool }
                    }]

                }
            },
            {
                $lookup: {
                    from: &#x27;customers&#x27;,
                    let: { customerId: &#x27;$customerId&#x27; },
                    pipeline: [
                        {
                            $match: {
                                $expr:
                                {
                                    $and:
                                        [
                                            { $eq: [&#x27;$$customerId&#x27;, &#x27;$id&#x27;] },
                                        ]
                                }


                            }
                        },
                    ],
                    as: &#x27;customers&#x27;
                }
            },
            {
                $project: {
                    customers: 0,
                    employees: 0,
                    phone: 0,
                }
            },
            {
                $match: q
            },
        ];
        return this.readModel.aggregate(aggregate)
            .allowDiskUse(true)
            .exec();
    }
    async countLeadReadyForAssigningByPOS(posId: string, type: string) {
        const match: any &#x3D; {
            &#x27;processBy&#x27;: null,
            &#x27;pos.id&#x27;: posId,
            &#x27;lifeCycleStatus&#x27;: LifeCycleStatusEnum.IN_POOL,
        };
        if (type) {
            match.type &#x3D; type;
        } else {
            match.type &#x3D; { $ne: CommonConst.TYPE.ADVISING };
        }
        return await this.readModel.find(match)
            .countDocuments()
            .then(rs &#x3D;&gt; {
                return rs;
            })
            .catch(ex &#x3D;&gt; {
                return ex;
            });
    }

    async countLeadReadyForAssigningByEmp(employee: any, type: string) {
        const match: any &#x3D; {
            processBy: null,
            $or: [{&#x27;pos.id&#x27;: employee.pos.id},
                    {&#x27;pos.id&#x27;: employee.pos.parentId}
                ],
            lifeCycleStatus: LifeCycleStatusEnum.IN_POOL,
        };
        if (employee.pos.taxNumber &#x3D;&#x3D;&#x3D; &#x27;3602545493&#x27;) { // Hard code DXS Tax No to get lead from shared pool
            match.$or.push({&#x27;pos.id&#x27;: &#x27;dxs-shared&#x27;});
        }
        if (type) {
            match.type &#x3D; type;
        } else {
            match.type &#x3D; { $ne: CommonConst.TYPE.ADVISING };
        }
        return await this.readModel.find(match)
            .countDocuments()
            .then(rs &#x3D;&gt; {
                return rs;
            })
            .catch(ex &#x3D;&gt; {
                return ex;
            });
    }

    findLeadByEmployeeId(employeeId: string[] &#x3D; []) {
        return this.readModel.find({
            processBy: { $in: employeeId, $exists: true, $nin: [null, &#x27;&#x27;] }, type: { $in: [TransactionTypeEnum.buy, TransactionTypeEnum.lease, TransactionTypeEnum.rent, TransactionTypeEnum.sell] },
        }).exec();
    }

    async countPrimaryLead(user: any, query: any) {
        const page: number &#x3D; parseInt(query[&#x27;page&#x27;]) || 1;
        const pageSize: number &#x3D; parseInt(query[&#x27;pageSize&#x27;]) || 10;
        const aggregate: any &#x3D; [];
        const match: any &#x3D; {};

        match.$and &#x3D; [];
        match.$and.push({ type: CommonConst.TYPE.PRIMARY });
        const emp &#x3D; await this.employeeRepository.findOne({id: user.id});
        if (!user.roles.includes(PermissionConst.LEAD_REPORT_VIEW_ALL)) {
            if (user &amp;&amp; user.id) {
                if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_EMP)) {                    
                    match.$and.push({processBy: {$nin: [null, &#x27;&#x27;]}}, {processBy: {$in: emp.staffIds}});
                } else if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_IMPORTER)) {
                    match.$and.push({&#x27;importedBy.id&#x27;: user.id});
                } else {
                    return [];
                }
            } else {
               return [];
            }
        } else {
          match.$and.push({&#x27;importedBy.id&#x27;: {$in: emp.staffIds}});
        }

        if (!_.isEmpty(query.posId)) {
            match[&#x27;pos.id&#x27;] &#x3D; query.posId;
        } else if (!_.isEmpty(query.exchangeId)) {
            match.$and.push({$or: [
                {&#x27;pos.id&#x27;: query.exchangeId},
                {&#x27;pos.parentId&#x27;: query.exchangeId}
            ]});
        }

        if (!_.isEmpty(query.assignedDateFrom)) {
            match.$and.push({assignedDate: {$gte: new Date(parseInt(query.assignedDateFrom, 10))}});
        }

        if (!_.isEmpty(query.assignedDateTo)) {
            match.$and.push({assignedDate: {$lte: new Date(parseInt(query.assignedDateTo, 10))}});
        }

        if (!_.isEmpty(query.resource)) {
            match.source &#x3D; query.resource;
        } else {
            match.source &#x3D; {
                $nin: [
                    SourceEnum.DangTin,
                    SourceEnum.YeuCau
            ]};
        }

        if (!_.isEmpty(query[&#x27;createdFrom&#x27;]) || !_.isEmpty(query[&#x27;createdTo&#x27;])) {
            match[&#x27;createdDate&#x27;] &#x3D; {};
            if (!_.isEmpty(query[&#x27;createdFrom&#x27;])) {
                match[&#x27;createdDate&#x27;][&#x27;$gte&#x27;] &#x3D; new Date(parseInt(query[&#x27;createdFrom&#x27;]));
            }
            if (!_.isEmpty(query[&#x27;createdTo&#x27;])) {
                match[&#x27;createdDate&#x27;][&#x27;$lte&#x27;] &#x3D; new Date(parseInt(query[&#x27;createdTo&#x27;]));
            }
        }
        aggregate.push({
            $match: match
        });
        aggregate.push({
            $addFields: {
                callHistoryCount: {
                    $cond: [{ $isArray: &#x27;$callHistory&#x27; }, {
                        $size: {
                            $filter: {
                                input: &quot;$callHistory&quot;,
                                as: &quot;e&quot;,
                                cond: { $eq: [&quot;$$e.isCalled&quot;, true] }
                            }
                        }
                    }, 0]
                },
                callHistoryMinuteCount: {
                    $cond: [{ $isArray: &#x27;$callHistory&#x27; }, {
                        $sum: &quot;$callHistory.answerTime&quot;
                    }, 0]
                }
            }
        });
        aggregate.push({
            $group: {
                _id: &#x27;$pos.id&#x27;,
                posName: { $first: &#x27;$pos.name&#x27; },
                countAll: { $sum: 1 },
                countProcessing: { $sum: { $cond: [{ $eq: [&#x27;$lifeCycleStatus&#x27;, &#x27;primary_processing&#x27;] }, 1, 0] } },
                countRemoved: { $sum: { $cond: [{ $eq: [&#x27;$lifeCycleStatus&#x27;, &#x27;removed&#x27;] }, 1, 0] } },
                countInValid: {
                    $sum: {
                        $cond: [{
                            $and: [
                                { $eq: [&#x27;$lifeCycleStatus&#x27;, &#x27;primary_processing&#x27;] },
                                { $eq: [&#x27;$updatedName&#x27;, &#x27;&#x27;] },
                                { $eq: [&#x27;$updatedEmail&#x27;, &#x27;&#x27;] },
                                { $eq: [&#x27;$updatedPhone&#x27;, &#x27;&#x27;] },
                                { $eq: [&#x27;$isInNeed&#x27;, &#x27;&#x27;] },
                                { $eq: [&#x27;$otherReason&#x27;, &#x27;&#x27;] },
                                { $eq: [&#x27;$note&#x27;, &#x27;&#x27;] },
                                { $eq: [&#x27;$needLoan&#x27;, false] },
                                { $eq: [&#x27;$isAppointment&#x27;, false] },
                                { $eq: [&#x27;$isVisited&#x27;, false] },
                            ]
                        }, 1, 0]
                    }
                },
                countCall: {
                    $sum: &#x27;$callHistoryCount&#x27;
                },
                countCallMinute: {
                    $sum: &#x27;$callHistoryMinuteCount&#x27;
                },
                latestUpdate: { $max: &#x27;$updatedDate&#x27; },
            }
        });
        aggregate.push({
            $facet: {
                rows: [{ $skip: Math.floor((pageSize * page) - pageSize) }, { $limit: pageSize }],
                totalCount: [
                    {
                        $count: &#x27;count&#x27;
                    }
                ]
            }
        });

        return this.readModel.aggregate(aggregate).allowDiskUse(true).exec();
    }

    async getLeadByStatus(user: any, status: ExploitEnum, query: any, page: number, pageSize: number): Promise&lt;any&gt; {
        const offset &#x3D; (page &#x3D;&#x3D;&#x3D; 1) ? 0 : ((page - 1) * pageSize);

        const conditions: any &#x3D; {
            exploitStatus: (status &#x3D;&#x3D;&#x3D; ExploitEnum.ASSIGN) ? { $in: [ ExploitEnum.ASSIGN, ExploitEnum.REASSIGN ] } : status,
        };
        const employee &#x3D; await this.employeeRepository.findOne({id: user.id});

        if (!user.roles.includes(PermissionConst.LEAD_REPORT_VIEW_ALL)) {
            if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_EMP)) {
                conditions[&#x27;takeCare.id&#x27;] &#x3D; { $in: employee.staffIds };
            } else if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_IMPORTER)) {
                conditions[&#x27;importedBy.id&#x27;] &#x3D; user.id;
            } else {
                return [[], 0];
            }
        } else {
            conditions[&#x27;importedBy.id&#x27;] &#x3D; { $in: employee?.staffIds || [] };
        }

        if (query.leadRepository) conditions.repoId &#x3D; query.leadRepository;
        if (query.leadRepositoryConfig) {
            if (query.leadRepositoryConfig &#x3D;&#x3D;&#x3D; &#x27;hot&#x27;) conditions.isHot &#x3D; true;
            else conditions.repoConfigCode &#x3D; query.leadRepositoryConfig;
        }
        if (query.pos) conditions[&#x27;pos.id&#x27;] &#x3D; query.pos;
        if (query.takeCareId) conditions[&#x27;takeCare.id&#x27;] &#x3D; query.takeCareId;

        if (query.startDate || query.endDate) conditions.updatedDate &#x3D; {};
        if (query.startDate) conditions.updatedDate[&#x27;$gte&#x27;] &#x3D; new Date(parseInt(query.startDate));
        if (query.endDate) conditions.updatedDate[&#x27;$lte&#x27;] &#x3D; new Date(parseInt(query.endDate));
        return Promise.all([
                this.readModel
                    .aggregate([
                        { $match: conditions },
                        {
                            $lookup: {
                                from: &#x27;lead-repos&#x27;,
                                let: {
                                    repoId: &#x27;$repoId&#x27;,
                                    repoConfigCode: &#x27;$repoConfigCode&#x27;,
                                },
                                as: &#x27;repo&#x27;,
                                pipeline: [
                                    {
                                        $match: {
                                            $expr: {
                                                $eq: [&#x27;$id&#x27;, &#x27;$$repoId&#x27;],
                                            },
                                        },
                                    },
                                    {
                                        $project: {
                                            configHot: 1,
                                            configs: {
                                                $filter: {
                                                    input: &#x27;$configs&#x27;,
                                                    as: &#x27;configs&#x27;,
                                                    cond: {
                                                        $eq: [&#x27;$$configs.code&#x27;, &#x27;$$repoConfigCode&#x27;],
                                                    },
                                                },
                                            },
                                        }
                                    },            
                                ],
                            }
                        },
                        {
                            $addFields: { // &#x3D;&gt; Get 1st item of repositories array, cus aggregate $lookup return an array with just 1 item
                                repo: {
                                    $arrayElemAt: [&#x27;$repo&#x27;, 0],
                                },
                            },
                        },
                        {
                            $addFields: { // &#x3D;&gt; Collect lead config
                                config: {
                                    $arrayElemAt: [
                                        {
                                            $cond: {
                                                if: { $eq: [&#x27;$isHot&#x27;, true] },
                                                then: [&#x27;$repo.configHot&#x27;],
                                                else: &#x27;$repo.configs&#x27;,
                                            },
                                        },
                                        0
                                    ],
                                },
                            },
                        },
                        {
                            $addFields: { // &#x3D;&gt; Collect visiblePhone flag in lead config
                                visiblePhone: &#x27;$config.visiblePhone&#x27;,
                            },
                        },  
                        {
                            $project: {
                                _id: 1,
                                id: 1,
                                code: 1,
                                name: 1,
                                phone: 1,
                                exploitStatus: 1,
                                pos: 1,
                                takeCare: 1,
                                updatedDate: 1,
                                visiblePhone: 1,
                                importedBy: 1,
                                customer: 1,
                            }
                        },
                        { $skip: offset },
                        { $limit: pageSize },
                    ])
                    .allowDiskUse(true)
                    .exec(),

                this.readModel.countDocuments(conditions),
            ]);
    }

    async getLeadsByStatusForExport(user: any, status: ExploitEnum, query: any) {
        const leads &#x3D; [];
        const limit &#x3D; 10;
        let page &#x3D; 1;
        let serial &#x3D; 0;
        
        const conditions: any &#x3D; {
            exploitStatus: (status &#x3D;&#x3D;&#x3D; ExploitEnum.ASSIGN) ? { $in: [ ExploitEnum.ASSIGN, ExploitEnum.REASSIGN ] } : status,
        };

        const employee &#x3D; await this.employeeRepository.findOne({id: user.id});
        if (!user.roles.includes(PermissionConst.LEAD_REPORT_VIEW_ALL)) {
            if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_EMP)) {
                conditions[&#x27;takeCare.id&#x27;] &#x3D; { $in: employee.staffIds };
            } else if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_IMPORTER)) {
                conditions[&#x27;importedBy.id&#x27;] &#x3D; user.id;
            } else {
                return [[], 0];
            }
        } else {
            conditions[&#x27;importedBy.id&#x27;] &#x3D; { $in: employee.staffIds };
        }

        if (query.leadRepository) conditions.repoId &#x3D; query.leadRepository;
        if (query.leadRepositoryConfig) {
            if (query.leadRepositoryConfig &#x3D;&#x3D;&#x3D; &#x27;hot&#x27;) conditions.isHot &#x3D; true;
            else conditions.repoConfigCode &#x3D; query.leadRepositoryConfig;
        }
        if (query.pos) conditions[&#x27;pos.id&#x27;] &#x3D; query.pos;
        if (query.takeCareId) conditions[&#x27;takeCare.id&#x27;] &#x3D; query.takeCareId;

        if (query.startDate || query.endDate) conditions.updatedDate &#x3D; {};
        if (query.startDate) conditions.updatedDate[&#x27;$gte&#x27;] &#x3D; new Date(parseInt(query.startDate));
        if (query.endDate) conditions.updatedDate[&#x27;$lte&#x27;] &#x3D; new Date(parseInt(query.endDate));

        const total &#x3D; await this.readModel.countDocuments(conditions);
        const totalPages &#x3D; Math.ceil(total / limit);

        while (page &lt;&#x3D; totalPages) {
            const offset &#x3D; (page &#x3D;&#x3D;&#x3D; 1) ? 0 : ((page - 1) * limit);


            leads.push(
                ...await this.readModel
                    .aggregate([
                        { $match: conditions },
                        {
                            $lookup: {
                                from: &#x27;lead-repos&#x27;,
                                let: {
                                    repoId: &#x27;$repoId&#x27;,
                                    repoConfigCode: &#x27;$repoConfigCode&#x27;,
                                },
                                as: &#x27;repo&#x27;,
                                pipeline: [
                                    {
                                        $match: {
                                            $expr: {
                                                $eq: [&#x27;$id&#x27;, &#x27;$$repoId&#x27;],
                                            },
                                        },
                                    },
                                    {
                                        $project: {
                                            configHot: 1,
                                            configs: {
                                                $filter: {
                                                    input: &#x27;$configs&#x27;,
                                                    as: &#x27;configs&#x27;,
                                                    cond: {
                                                        $eq: [&#x27;$$configs.code&#x27;, &#x27;$$repoConfigCode&#x27;],
                                                    },
                                                },
                                            },
                                        }
                                    },            
                                ],
                            }
                        },
                        {
                            $addFields: { // &#x3D;&gt; Get 1st item of repositories array, cus aggregate $lookup return an array with just 1 item
                                repo: {
                                    $arrayElemAt: [&#x27;$repo&#x27;, 0],
                                },
                            },
                        },
                        {
                            $addFields: { // &#x3D;&gt; Collect lead config
                                config: {
                                    $arrayElemAt: [
                                        {
                                            $cond: {
                                                if: { $eq: [&#x27;$isHot&#x27;, true] },
                                                then: [&#x27;$repo.configHot&#x27;],
                                                else: &#x27;$repo.configs&#x27;,
                                            },
                                        },
                                        0
                                    ],
                                },
                            },
                        },
                        {
                            $addFields: { // &#x3D;&gt; Collect visiblePhone flag in lead config
                                visiblePhone: &#x27;$config.visiblePhone&#x27;,
                            },
                        },
                        {
                            $project: {
                                _id: 0,
                                code: 1,
                                name: 1,
                                phone: 1,
                                exploitStatus: 1,
                                pos: &#x27;$pos.name&#x27;,
                                exploitStatusModifiedBy: 1,
                                exploitStatusModifiedAt: 1,
                                takeCare: &#x27;$takeCare.name&#x27;,
                                updatedDate: 1,
                                surveys: 1,
                                reason: 1,
                                visiblePhone: 1,
                                importedBy: 1,
                                exploitHistory: 1,
                                customer: 1,
                                callHistory: 1
                            }
                        },
                        { $skip: offset },
                        { $limit: limit },
                    ])
                    .allowDiskUse(true)
                    .exec()
            );

            page++;
        }

        return leads.map(item &#x3D;&gt; {
            serial++;

            return {
                serial,
                ...item,
            };
        });
    }

    async createMany(records: ILead[]) {
      if (records.length) {
        const bulkData &#x3D; records.map((item) &#x3D;&gt; {
          if (typeof (item as any).toObject &#x3D;&#x3D;&#x3D; &#x27;function&#x27;) {
            item &#x3D; (item as any).toObject();
          }
          return {
              updateOne: {
                  filter: { id: item.id },
                  update: {
                      $set: { _id: item.id, updatedDate: new Date(), ...item },
                  },
                  upsert: true,
                  setDefaultsOnInsert: true,
              },
          };
        });

        return this.readModel
            .bulkWrite(bulkData)
            .then((r) &#x3D;&gt; {
                const { upsertedCount, modifiedCount } &#x3D; r;
                console.log(
                    &#x60;BulkWrite ${this.readModel.modelName} result &#x60;,
                    { upsertedCount, modifiedCount }
                );
            })
            .catch((e) &#x3D;&gt; console.error(e));
      }
    }

    async updateMany(records: any[]) {
      if (records.length) {
        const bulkData &#x3D; records.map((item) &#x3D;&gt; {
            const latestAssignHistory &#x3D; item.latestAssignHistory;
            delete item.latestAssignHistory;
            return {
                updateOne: {
                    filter: { id: item.id },
                    update: {
                        $set: { 
                            _id: item.id, 
                            updatedDate: new Date(),
                            ...item
                        },
                        $push: {
                            exploitHistory: {
                              $each: [latestAssignHistory],
                              $slice: -50
                           }
                        }
                    },
                    setDefaultsOnInsert: true,
                },
            }
        });

        return this.readModel
            .bulkWrite(bulkData)
            .then((r) &#x3D;&gt; {
                const { upsertedCount, modifiedCount } &#x3D; r;
                console.log(
                    &#x60;BulkWrite ${this.readModel.modelName} result &#x60;,
                    { upsertedCount, modifiedCount }
                );
            })
            .catch((e) &#x3D;&gt; console.error(e));
      }
    }

    async findMany(filter, projection &#x3D; {}, limit &#x3D; null) {
        return this.readModel.find(filter, projection).limit(limit);
    }

    async countExploitationByEmployee(where: Record&lt;string, any&gt;) {
      return this.readModel.aggregate([
          {
              $match: {
                &#x27;takeCare.id&#x27;: where.takeCareIds,
                &#x27;exploitStatus&#x27;: where.statuses,
                &#x27;exploitHistory.updatedAt&#x27;: where.dateFilter
              },
          },
          { $unwind: &#x27;$exploitHistory&#x27; },
          {
              $match: {
                  &#x27;exploitHistory.status&#x27;: where.statuses,
                  &#x27;exploitHistory.updatedAt&#x27;: where.dateFilter
              },
          },
          {
              $group: {
                  _id: {
                      date: {
                          $dateToString: {
                              format: &#x27;%Y-%m-%d&#x27;,
                              date: &#x27;$exploitHistory.updatedAt&#x27;,
                          },
                      },
                      status: &#x27;$exploitHistory.status&#x27;,
                  },
                  count: {
                      $sum: 1,
                  },
              },
          },
      ]);
    }

    async getTodayExploited(userId: string) {
      const where &#x3D; {
        &#x27;takeCare.id&#x27;: userId,
        &#x27;exploitHistory.takeCareId&#x27;: userId,
        &#x27;exploitHistory.status&#x27;: {
            $in: [ExploitEnum.ASSIGN, ExploitEnum.REASSIGN],
        },
        &#x27;exploitHistory.updatedAt&#x27;: {
            $gte: new Date(moment().startOf(&#x27;day&#x27;).valueOf()),
            $lte: new Date(moment().endOf(&#x27;day&#x27;).valueOf()),
        },
    };
      return this.readModel.find(where, {
          id: 1,
          exploitStatus: 1,
          exploitHistory: 1,
          assignDuration: 1,
      });
    }

    async fetchLeadsWithAggs (filters) {
        const aggs &#x3D; [
            {
                $match: filters, // &#x3D;&gt; Get leads by filters
            },
            {
                $lookup: {
                    from: &#x60;${CommonConst.LEAD_REPO_COLLECTION}s&#x60;,
                    let: {
                        repoId: &#x27;$repoId&#x27;,
                        configCode: &#x27;$repoConfigCode&#x27;,
                    },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $eq: [&#x27;$id&#x27;, &#x27;$$repoId&#x27;],
                                },
                            },
                        },
                        {
                            $unwind: &#x27;$configs&#x27;,
                        },
                        {
                            $match: {
                                $expr: {
                                    $eq: [&#x27;$configs.code&#x27;, &#x27;$$configCode&#x27;],
                                },
                            },
                        },
                        {
                            $project: {
                                value: {
                                    $cond: {
                                        if: {
                                            $eq: [&#x27;$isHot&#x27;, true],
                                        },
                                        then: &#x27;$configHot.visiblePhone&#x27;,
                                        else: &#x27;$configs.visiblePhone&#x27;,
                                    },
                                },
                            },
                        },
                    ],
                    as: &#x27;visiblePhone&#x27;,
                },
            },
            {
                $unwind: {
                    path: &#x27;$visiblePhone&#x27;,
                    preserveNullAndEmptyArrays: true
                },
            },
            {
                $project: {
                    id: 1,
                    code: 1,
                    createdDate: 1,
                    name: 1,
                    phone: 1,
                    email: 1,
                    profileUrl: 1,
                    isHot: 1,
                    visiblePhone: {
                      $cond: {
                        if: {
                            $eq: [ { $ifNull: [&#x27;$visiblePhone.value&#x27;, false] }, false],
                        },
                        then: false,
                        else: &#x27;$visiblePhone.value&#x27;,
                      },
                    },
                    assignDuration: 1,
                    exploitStatus: 1,
                    latestAssignHistory: {
                        $arrayElemAt: [
                            {
                                $filter: {
                                    input: &#x27;$exploitHistory&#x27;,
                                    as: &#x27;item&#x27;,
                                    cond: {
                                        $or: [
                                            {
                                                $eq: [
                                                    &#x27;$$item.status&#x27;,
                                                    &#x27;assign&#x27;,
                                                ],
                                            },
                                            {
                                                $eq: [
                                                    &#x27;$$item.status&#x27;,
                                                    &#x27;reassign&#x27;,
                                                ],
                                            },
                                        ],
                                    },
                                },
                            },
                            -1,
                        ],
                    },
                },
            },
        ];

        return this.readModel.aggregate(aggs);
    }

    async getLeadsToDeliver(user: any, page: number &#x3D; 1, pageSize: number &#x3D; 10, query: any &#x3D; {}): Promise&lt;ILeadDocument[]&gt; {
        let sort: any &#x3D; {
            createdDate: -1
        };
        const match: any &#x3D; {};
        const agg &#x3D; [];
        match.$and &#x3D; [];

        if (!user.roles.includes(PermissionConst.LEAD_REPORT_VIEW_ALL)) {
          if (user &amp;&amp; user.id) {
              if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_EMP)) {
                  const emp &#x3D; await this.employeeRepository.findOne({id: user.id});
                  const $or &#x3D; [
                    {processBy: {$in: emp.staffIds}},
                    {&#x27;takeCare.id&#x27;: {$in: emp.staffIds}}
                  ]
                  match.$and.push({$or});
              } else if (user.roles.includes(PermissionConst.LEAD_REPORT_BY_IMPORTER)) {
                  match.$and.push({&#x27;importedBy.id&#x27;: user.id});
              } else {
                  const $or &#x3D; [
                    {processBy: user.id},
                    {&#x27;takeCare.id&#x27;: user.id}
                  ]
                  match.$and.push({$or});
              }
          } else {
             return [];
          }
      } else {
          const emp &#x3D; await this.employeeRepository.findOne({id: user.id});
          match.$and.push({&#x27;importedBy.id&#x27;: {$in: emp.staffIds}});
      }
        if (!_.isEmpty(query.statusLead)) {
          match.$and.push({
            exploitStatus: {
              $in: query.statusLead
            }
          });
        } else {
          match.$and.push({
            exploitStatus: ExploitEnum.MANUAL_DELIVER
          });
        }

        if (!_.isEmpty(query.resource)) {
            match.source &#x3D; query.resource;
        } else {
            match.source &#x3D; {
                $nin: [
                    SourceEnum.DangTin,
                    SourceEnum.YeuCau
            ]};
        }
        if (!_.isEmpty(query.type)) {
            match.type &#x3D; { $in: query.type.split(&#x27;,&#x27;) };
        }
        if (!_.isEmpty(query.posId)) {
            match[&#x27;pos.id&#x27;] &#x3D; query.posId;
        }

        if (!_.isEmpty(query.exchangeId)) {
            match.$and.push({$or: [
                {&#x27;pos.id&#x27;: query.exchangeId},
                {&#x27;pos.parentId&#x27;: query.exchangeId}
            ]});
        }

        if (!_.isEmpty(query.assignedDateFrom)) {
            match.$and.push({assignedDate: {$gte: new Date(parseInt(query.assignedDateFrom, 10))}});
        }

        if (!_.isEmpty(query.assignedDateTo)) {
            match.$and.push({assignedDate: {$lte: new Date(parseInt(query.assignedDateTo, 10))}});
        }

        if (!_.isEmpty(query[&#x27;createdFrom&#x27;]) || !_.isEmpty(query[&#x27;createdTo&#x27;])) {
            match[&#x27;createdDate&#x27;] &#x3D; {};
            if (!_.isEmpty(query[&#x27;createdFrom&#x27;])) {
                match[&#x27;createdDate&#x27;][&#x27;$gte&#x27;] &#x3D; new Date(parseInt(query[&#x27;createdFrom&#x27;]));
            }
            if (!_.isEmpty(query[&#x27;createdTo&#x27;])) {
                match[&#x27;createdDate&#x27;][&#x27;$lte&#x27;] &#x3D; new Date(parseInt(query[&#x27;createdTo&#x27;]));
            }
        }
        if (!_.isEmpty(query.lifeCycleStatus)) {
            match.lifeCycleStatus &#x3D; { $in: query.lifeCycleStatus.split(&#x27;,&#x27;) };
        } else {
            agg.push(
                {
                    $lookup: {
                        from: &#x27;employees&#x27;,
                        localField: &#x27;processBy&#x27;,
                        foreignField: &#x27;id&#x27;,
                        as: &#x27;employeeTakeCare&#x27;
                    }
                });
        }
        let matchKeywords: any &#x3D; {};
        if (!_.isEmpty(query.q)) {
            matchKeywords[&#x27;$or&#x27;] &#x3D; [
                { $and: [{ &#x27;name&#x27;: { $regex: query.q, $options: &#x27;i&#x27; } }, { $or: [{ type: &#x27;BUY&#x27; }, { type: &#x27;RENT&#x27; }] }] },
                { &#x27;pos.name&#x27;: { $regex: query.q, $options: &#x27;i&#x27; } },
                { $and: [{ &#x27;property.address&#x27;: { $regex: query.q, $options: &#x27;i&#x27; } }, { $or: [{ type: &#x27;SELL&#x27; }, { type: &#x27;LEASE&#x27; }] }] },
                { $and: [{ &#x27;category.name&#x27;: { $regex: query.q, $options: &#x27;i&#x27; } }, { $or: [{ type: &#x27;SELL&#x27; }, { type: &#x27;LEASE&#x27; }] }] },
            ];
        }
        if (!_.isEmpty(query.sort)) {
            sort &#x3D; this.transformSort(query.sort) || {
                createdDate: -1
            };
        }

        agg.push(
            {
                $match: match
            },
            {
                $match: matchKeywords
            },
            {
                $sort: sort
            },
            {
                $project: {
                    id: 1,
                    project: 1,
                    source: 1,
                    code: 1,
                    description: 1,
                    email: 1,
                    phone: 1,
                    name: 1,
                    note: 1,
                    profileUrl: 1,
                    createdDate: 1,
                    assignedDate: 1,
                    updatedDate: 1,
                    countAssign: 1,
                    exploitStatus: 1,
                    takeCare: 1
                }
            },
            {
                $facet: {
                    rows: [{ $skip: Math.floor((pageSize * page) - pageSize) }, { $limit: pageSize }],
                    totalCount: [
                        {
                            $count: &#x27;count&#x27;
                        }
                    ]
                }
            }
        );

        return await this.readModel.aggregate(agg).allowDiskUse(true)
            .exec();
    }
}
</code></pre>
    </div>

</div>







                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'QueryRepository.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
