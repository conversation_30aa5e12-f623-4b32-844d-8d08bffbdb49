<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-lead documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-lead documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">







<ol class="breadcrumb">
  <li>Injectables</li>
  <li>LeadCareDomainService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/leadCare.domain/service.ts</code>
        </p>




            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#commandId">commandId</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Readonly</span>
                                <a href="#context">context</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#addAnswerConversation">addAnswerConversation</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#assignLeadCare">assignLeadCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#callingLeadCare">callingLeadCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#calTimeOutToProcess">calTimeOutToProcess</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#checkEmployeeExist">checkEmployeeExist</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#completeLeadCare">completeLeadCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createLeadCare">createLeadCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#createLeadCareImport">createLeadCareImport</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#createTimeout">createTimeout</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#customerCancelTicketLeadCare">customerCancelTicketLeadCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#customerClosedTicketLeadCare">customerClosedTicketLeadCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#executeCommand">executeCommand</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#failLeadCare">failLeadCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getEvents">getEvents</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#getLeadCare">getLeadCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#HandleTransferRequest">HandleTransferRequest</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#importDemandTicket">importDemandTicket</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#importLeadCareFromExcel">importLeadCareFromExcel</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#pendingLeadCare">pendingLeadCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#processLeadCare">processLeadCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#pullLeadCare">pullLeadCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#rejectAllLeadCare">rejectAllLeadCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#rejectLeadCare">rejectLeadCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#systemClosedTicketLeadCare">systemClosedTicketLeadCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#takeSurvey">takeSurvey</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#timeout">timeout</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#unprocessLeadCare">unprocessLeadCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#updateExploitHistory">updateExploitHistory</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateExploitStatus">updateExploitStatus</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateExploitStatusAssign">updateExploitStatusAssign</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateLeadCare">updateLeadCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateRateInfoLeadCareById">updateRateInfoLeadCareById</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateStatusLeadCare">updateStatusLeadCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateEmployeeBeforePull">validateEmployeeBeforePull</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#validateLeadCare">validateLeadCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateLeadCareToAssign">validateLeadCareToAssign</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#validateRecaptcha">validateRecaptcha</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section>
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(commandBus: CommandBus, queryRepository: <a href="../injectables/QueryRepository.html">QueryRepository</a>, employeeRepository: <a href="../injectables/EmployeeQueryRepository.html">EmployeeQueryRepository</a>, eventStreamRepository: <a href="../injectables/EventStreamRepository.html">EventStreamRepository</a>, codeGenerateService: <a href="../injectables/CodeGenerateService.html">CodeGenerateService</a>, configService: <a href="../injectables/ConfigService.html">ConfigService</a>, loggerService: <a href="../injectables/MsxLoggerService.html">MsxLoggerService</a>, orgchartClient: <a href="../injectables/OrgchartClient.html">OrgchartClient</a>, historyImportService: <a href="../injectables/HistoryImportService.html">HistoryImportService</a>, leadRepoCareRepository: <a href="../injectables/LeadRepoCareQueryRepository.html">LeadRepoCareQueryRepository</a>, primaryContractClient: <a href="../injectables/PrimaryContractClient.html">PrimaryContractClient</a>, leadJobService: <a href="../injectables/LeadjobService.html">LeadjobService</a>, employeeClient: <a href="../injectables/EmployeeClient.html">EmployeeClient</a>, notificationClient: <a href="../injectables/NotificationClient.html">NotificationClient</a>, propertyClient: <a href="../injectables/PropertyClient.html">PropertyClient</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="67" class="link-to-prism">src/modules/leadCare.domain/service.ts:67</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>commandBus</td>
                                                  
                                                        <td>
                                                                    <code>CommandBus</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>queryRepository</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/QueryRepository.html" target="_self" >QueryRepository</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>employeeRepository</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/EmployeeQueryRepository.html" target="_self" >EmployeeQueryRepository</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>eventStreamRepository</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/EventStreamRepository.html" target="_self" >EventStreamRepository</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>codeGenerateService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/CodeGenerateService.html" target="_self" >CodeGenerateService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>configService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/ConfigService.html" target="_self" >ConfigService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>loggerService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/MsxLoggerService.html" target="_self" >MsxLoggerService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>orgchartClient</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/OrgchartClient.html" target="_self" >OrgchartClient</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>historyImportService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/HistoryImportService.html" target="_self" >HistoryImportService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>leadRepoCareRepository</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/LeadRepoCareQueryRepository.html" target="_self" >LeadRepoCareQueryRepository</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>primaryContractClient</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/PrimaryContractClient.html" target="_self" >PrimaryContractClient</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>leadJobService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/LeadjobService.html" target="_self" >LeadjobService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>employeeClient</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/EmployeeClient.html" target="_self" >EmployeeClient</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>notificationClient</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/NotificationClient.html" target="_self" >NotificationClient</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>propertyClient</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/PropertyClient.html" target="_self" >PropertyClient</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section>
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="addAnswerConversation"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            addAnswerConversation
                        </b>
                        <a href="#addAnswerConversation"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>addAnswerConversation(user, dto)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1056"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:1056</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="assignLeadCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            assignLeadCare
                        </b>
                        <a href="#assignLeadCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>assignLeadCare(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, dto: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, timezoneclient: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="414"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:414</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>timezoneclient</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="callingLeadCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            callingLeadCare
                        </b>
                        <a href="#callingLeadCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>callingLeadCare(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, dto: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="372"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:372</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="calTimeOutToProcess"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            calTimeOutToProcess
                        </b>
                        <a href="#calTimeOutToProcess"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>calTimeOutToProcess(leadCare: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, timezoneclient: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="776"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:776</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>leadCare</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>timezoneclient</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="checkEmployeeExist"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            checkEmployeeExist
                        </b>
                        <a href="#checkEmployeeExist"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>checkEmployeeExist(employeeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="823"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:823</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>employeeId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="completeLeadCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            completeLeadCare
                        </b>
                        <a href="#completeLeadCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>completeLeadCare(dto: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="382"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:382</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createLeadCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            createLeadCare
                        </b>
                        <a href="#createLeadCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createLeadCare(dto: <a href="../s/Public.html">CreateLeadCareDto | ImportLeadCareFromPublicForm</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, timezoneclient?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, userId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, clientId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, secretKey?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="206"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:206</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../miscellaneous/variables.html#Public" target="_self" >CreateLeadCareDto | ImportLeadCareFromPublicForm</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>timezoneclient</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                                <tr>
                                    <td>userId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                                <tr>
                                    <td>clientId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                                <tr>
                                    <td>secretKey</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createLeadCareImport"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            createLeadCareImport
                        </b>
                        <a href="#createLeadCareImport"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createLeadCareImport(leadCare, actionName, timezoneclient)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1021"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:1021</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>leadCare</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>timezoneclient</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;any&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createTimeout"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            createTimeout
                        </b>
                        <a href="#createTimeout"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createTimeout(timezoneclient: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="796"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:796</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>timezoneclient</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="customerCancelTicketLeadCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            customerCancelTicketLeadCare
                        </b>
                        <a href="#customerCancelTicketLeadCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>customerCancelTicketLeadCare(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, dto: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, timezoneclient: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="444"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:444</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>timezoneclient</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="customerClosedTicketLeadCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            customerClosedTicketLeadCare
                        </b>
                        <a href="#customerClosedTicketLeadCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>customerClosedTicketLeadCare(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, dto: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, timezoneclient: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="429"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:429</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>timezoneclient</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="executeCommand"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            executeCommand
                        </b>
                        <a href="#executeCommand"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>executeCommand(action: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, commandId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, item: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="719"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:719</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>action</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>commandId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>item</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="failLeadCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            failLeadCare
                        </b>
                        <a href="#failLeadCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>failLeadCare(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, dto: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="388"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:388</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getEvents"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getEvents
                        </b>
                        <a href="#getEvents"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getEvents(leadCareId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="616"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:616</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>leadCareId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getLeadCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            getLeadCare
                        </b>
                        <a href="#getLeadCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getLeadCare(leadCareId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="812"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:812</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>leadCareId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="HandleTransferRequest"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            HandleTransferRequest
                        </b>
                        <a href="#HandleTransferRequest"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>HandleTransferRequest(user, dto, actionName)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1079"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:1079</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="importDemandTicket"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            importDemandTicket
                        </b>
                        <a href="#importDemandTicket"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>importDemandTicket(files: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, dto: <a href="../classes/ImportLeadCareDemandDto.html">ImportLeadCareDemandDto</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, timezoneclient?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, userId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="936"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:936</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>files</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/ImportLeadCareDemandDto.html" target="_self" >ImportLeadCareDemandDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>timezoneclient</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                                <tr>
                                    <td>userId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="importLeadCareFromExcel"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            importLeadCareFromExcel
                        </b>
                        <a href="#importLeadCareFromExcel"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>importLeadCareFromExcel(files: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, options: <a href="../classes/ImportLeadCareAsExcelDto.html">ImportLeadCareAsExcelDto</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, timezoneclient: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1034"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:1034</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>files</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>options</td>
                                    <td>
                                                <code><a href="../classes/ImportLeadCareAsExcelDto.html" target="_self" >ImportLeadCareAsExcelDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>timezoneclient</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="pendingLeadCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            pendingLeadCare
                        </b>
                        <a href="#pendingLeadCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>pendingLeadCare(dto: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user?: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="608"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:608</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-description"><p>Chuyển ticket về pool chung (ticket quá hạn)</p>
</div>

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="processLeadCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            processLeadCare
                        </b>
                        <a href="#processLeadCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>processLeadCare(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, dto: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="395"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:395</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="pullLeadCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            pullLeadCare
                        </b>
                        <a href="#pullLeadCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>pullLeadCare(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, timezoneclient: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, type?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="522"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:522</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>timezoneclient</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>type</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="rejectAllLeadCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            rejectAllLeadCare
                        </b>
                        <a href="#rejectAllLeadCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>rejectAllLeadCare(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, dto: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="146"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:146</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="rejectLeadCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            rejectLeadCare
                        </b>
                        <a href="#rejectLeadCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>rejectLeadCare(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, dto: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="87"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:87</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="systemClosedTicketLeadCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            systemClosedTicketLeadCare
                        </b>
                        <a href="#systemClosedTicketLeadCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>systemClosedTicketLeadCare(actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="470"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:470</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="takeSurvey"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            takeSurvey
                        </b>
                        <a href="#takeSurvey"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>takeSurvey(id, surveys)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1271"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:1271</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>id</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>surveys</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="timeout"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            timeout
                        </b>
                        <a href="#timeout"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>timeout()</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="900"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:900</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="unprocessLeadCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            unprocessLeadCare
                        </b>
                        <a href="#unprocessLeadCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>unprocessLeadCare(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, dto: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="407"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:407</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateExploitHistory"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            updateExploitHistory
                        </b>
                        <a href="#updateExploitHistory"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateExploitHistory(history: IExploitHistory[], status: <a href="../undefineds/ExploitCareEnum.html">ExploitCareEnum</a>, takeCareId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="507"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:507</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>history</td>
                                    <td>
                                            <code>IExploitHistory[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>[]</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>status</td>
                                    <td>
                                                <code><a href="../miscellaneous/enumerations.html#ExploitCareEnum" target="_self" >ExploitCareEnum</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>takeCareId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateExploitStatus"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateExploitStatus
                        </b>
                        <a href="#updateExploitStatus"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateExploitStatus(dto: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, usrId: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1188"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:1188</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>usrId</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateExploitStatusAssign"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateExploitStatusAssign
                        </b>
                        <a href="#updateExploitStatusAssign"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateExploitStatusAssign(dto: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user?: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="621"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:621</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateLeadCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateLeadCare
                        </b>
                        <a href="#updateLeadCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateLeadCare(dto: <a href="../classes/UpdateLeadCareDto.html">UpdateLeadCareDto</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, usrId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="707"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:707</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                                <code><a href="../classes/UpdateLeadCareDto.html" target="_self" >UpdateLeadCareDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>usrId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateRateInfoLeadCareById"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateRateInfoLeadCareById
                        </b>
                        <a href="#updateRateInfoLeadCareById"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateRateInfoLeadCareById(rateData: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1262"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:1262</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>rateData</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateStatusLeadCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateStatusLeadCare
                        </b>
                        <a href="#updateStatusLeadCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateStatusLeadCare()</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="855"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:855</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateEmployeeBeforePull"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            validateEmployeeBeforePull
                        </b>
                        <a href="#validateEmployeeBeforePull"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateEmployeeBeforePull(employeeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, type: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="831"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:831</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>employeeId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>type</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateLeadCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            validateLeadCare
                        </b>
                        <a href="#validateLeadCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateLeadCare(leadCare)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="1024"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:1024</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>leadCare</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>&quot;Số điện thoại sai&quot; | &quot;Email sai&quot; | &quot;&quot;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateLeadCareToAssign"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            validateLeadCareToAssign
                        </b>
                        <a href="#validateLeadCareToAssign"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateLeadCareToAssign(leadCareId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="802"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:802</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>leadCareId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateRecaptcha"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            validateRecaptcha
                        </b>
                        <a href="#validateRecaptcha"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateRecaptcha(recaptcha)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="360"
                            class="link-to-prism">src/modules/leadCare.domain/service.ts:360</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>recaptcha</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>
            <section>
    
        <h3 id="inputs">
            Properties
        </h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="commandId"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Private</span>
                            commandId</b>
                            <a href="#commandId"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="67" class="link-to-prism">src/modules/leadCare.domain/service.ts:67</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="context"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Private</span>
                                <span class="modifier">Readonly</span>
                            context</b>
                            <a href="#context"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>LeadCareDomainService.name</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="66" class="link-to-prism">src/modules/leadCare.domain/service.ts:66</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Injectable, BadRequestException, NotFoundException, HttpService } from &#x27;@nestjs/common&#x27;;
import { CommandBus } from &#x27;@nestjs/cqrs&#x27;;
import {
  CreateLeadCareDto, CreateServiceRequestDto,
  ImportLeadCareAsExcelDto,
  ImportLeadCareDemandDto,
  ImportLeadCareFromPublicForm,
  UpdateLeadCareDto,
  UpdateStatusAssignDto,
  UpdateStatusDto,
} from &#x27;./dto/leadCare.dto&#x27;;
import {
    IExploitHistory
} from &#x27;../shared/services/leadCare/interfaces/leadCare.interface&#x27;;
import { CreateLeadCareCommand } from &#x27;./commands/impl/create.cmd&#x27;;
import { Action } from &#x27;../shared/enum/action.enum&#x27;;
import { ProcessLeadCareCommand } from &#x27;./commands/impl/process.cmd&#x27;;
import { CompleteLeadCareCommand } from &#x27;./commands/impl/complete.cmd&#x27;;
import { FailLeadCareCommand } from &#x27;./commands/impl/fail.cmd&#x27;;
import { ChangeStatusLeadCareCommand } from &#x27;./commands/impl/changeStatus.cmd&#x27;;
import { UnprocessLeadCareCommand } from &#x27;./commands/impl/unprocess.cmd&#x27;;
import { QueryRepository } from &#x27;../leadCare.queryside/repository/query.repository&#x27;;
import { isNullOrUndefined } from &#x27;util&#x27;;
import { EmployeeQueryRepository } from &#x27;../employee/repository/query.repository&#x27;;
import { ConfigTimeConst } from &#x27;../shared/constant/config-time.const&#x27;;
import { AssignLeadCareCommand } from &#x27;./commands/impl/assign.cmd&#x27;;
import { LifeCycleStatusEnum } from &#x27;../shared/enum/life-cycle-status.enum&#x27;;
import { ReassignLeadCareCommand } from &#x27;./commands/impl/reassign.cmd&#x27;;
import { PendingLeadCareCommand } from &#x27;./commands/impl/pending.cmd&#x27;;
import { StatusEnum } from &#x27;../shared/enum/status.enum&#x27;;
import { EventStreamRepository } from &#x27;./repository/event-stream.repository&#x27;;
import { UpdateLeadCareCommand } from &#x27;./commands/impl/update.cmd&#x27;;
import { ExpiredLeadCareCommand } from &#x27;./commands/impl/expired.cmd&#x27;;
import { RawQueryRepository } from &#x27;../raw/repository/query.repository&#x27;;
import { MsxLoggerService } from &#x27;../logger/logger.service&#x27;;
import { ErrorConst } from &#x27;../shared/constant/error.const&#x27;;
import { ILeadCareProcessed } from &#x27;../shared/services/leadCare/interfaces/leadCare.interface&#x27;;
import { map } from &#x27;rxjs/operators&#x27;;
import { OrgchartClient } from &#x27;../mgs-sender/orgchart.client&#x27;;
import { CommonConst } from &#x27;../../modules/shared/constant/common.const&#x27;;
import { CmdPatternConst } from &#x27;../../modules/shared/constant/cmd-pattern.const&#x27;;
import { ConfigService } from &#x27;../config/config.service&#x27;;
import { CommonUtils } from &#x27;../shared/classes/class-utils&#x27;;
import { CodeGenerateService } from &#x27;../code-generate/service&#x27;;
import { HistoryImportService } from &#x27;../history-import/service&#x27;;
const XLSX &#x3D; require(&#x27;xlsx&#x27;);
import { LeadRepoCareQueryRepository } from &#x27;../leadRepoCare.queryside/repositories/query.repository&#x27;;
import * as _ from &#x27;lodash&#x27;;
import { ConversationEnum, ExploitCareEnum } from &#x27;../shared/enum/exploit.enum&#x27;;
import { LeadRepoCareEnum} from &quot;../shared/enum/type.enum&quot;;
import {PrimaryContractClient} from &quot;../mgs-sender/primary-contract.client&quot;;
import { CustomerClosedTicketCareCommand } from &#x27;./commands/impl/customerClosedTicket.cmd&#x27;;
import { LeadjobService } from &#x27;../leadJob/application/service&#x27;;
import { EmployeeClient } from &#x27;../mgs-sender/employee.client&#x27;;
import { isEmpty } from &#x27;lodash&#x27;;
import { NotificationClient } from &#x27;../mgs-sender/notification.client&#x27;;
import {PropertyClient} from &quot;../mgs-sender/property.client&quot;;

const uuid &#x3D; require(&#x27;uuid&#x27;);
const timezone &#x3D; require(&#x27;moment-timezone&#x27;);
const request &#x3D; require(&#x27;request&#x27;);

@Injectable()
export class LeadCareDomainService {
    private readonly context &#x3D; LeadCareDomainService.name;
    private commandId: string;
    constructor(
        private readonly commandBus: CommandBus,
        private readonly queryRepository: QueryRepository,
        private readonly employeeRepository: EmployeeQueryRepository,
        private readonly eventStreamRepository: EventStreamRepository,
        private readonly codeGenerateService: CodeGenerateService,
        private readonly configService: ConfigService,
        private readonly loggerService: MsxLoggerService,
        private readonly orgchartClient: OrgchartClient,
        private readonly historyImportService: HistoryImportService,
        private readonly leadRepoCareRepository: LeadRepoCareQueryRepository,
        private readonly primaryContractClient: PrimaryContractClient,
        private readonly leadJobService: LeadjobService,
        private readonly employeeClient : EmployeeClient, 
        private readonly notificationClient: NotificationClient,
        private readonly propertyClient: PropertyClient,

    ) { }

    async rejectLeadCare(user: any, dto: any, actionName: string) {
        this.loggerService.log(this.context, &#x27;Reject service&#x27;);
        const leadCare &#x3D; await this.queryRepository.findLeadCareById(dto.id);
        if (!leadCare) {
            throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.HAVE_NOT_ANY_LEAD) });
        }
        
        // Check ticket
        if (leadCare.lifeCycleStatus !&#x3D;&#x3D; LifeCycleStatusEnum.PENDING) {
            const history: ILeadCareProcessed[] &#x3D; leadCare.processedHistory || [];
            history.push({
                id: uuid.v4(),
                processedDate: new Date(),
                processBy: user.id,
                isReject: true,
                isTimeOut: false,
                causeReject: dto.causeReject
            } as ILeadCareProcessed);
            const model: any &#x3D; {
                id: leadCare.id,
                customerId: leadCare.customerId,
                modifiedBy: user.email,
                // processBy: null,
                // timeOut: null,
                lifeCycleStatus: LifeCycleStatusEnum.REMOVED,
                reason: dto.causeReject[0],
                processedHistory: history
            };
            // Get DVKH POS
            // let pos &#x3D; null;
            // try {
            //     const request &#x3D; {
            //         model: null,
            //         action: CommonConst.AGGREGATES.LEAD.REJECTED
            //     };
            //     pos &#x3D; await this.orgchartClient.sendDataPromise(request, CmdPatternConst.SERVICE.LEAD.LISTENER);
            //     console.log(&#x27;pos : &#x27;, pos);
                
            // } catch (err) {
            //     this.loggerService.log(this.context, &#x27;[Error] Cannot find POS DVKH&#x27;, err);
            // }
            // flow mới update 20/2/2020
            // Yêu cầu bị trả về thì đặt lifeCycleStatus là removed. 
            // và gán vào pos DVKH 
            // không quan tâm tới trả về mấy lần
            // follow mới update 29/06/2020
            // Yêu cầu trả về vẫn đặt ở pos đó, status là removed
            // model.pos &#x3D; {
            //     id: pos.id,
            //     name: pos.name,
            //     code: pos.code
            // };
            this.commandId &#x3D; uuid.v4();
            await this.executeCommand(Action.FAIL, actionName, this.commandId, model)
        }
        // this.employeeRepository.penalty({ id: user.id });
        return true;
    }

    async rejectAllLeadCare(user: any, dto: any, actionName: string) {
        this.loggerService.log(this.context, &#x27;Reject service&#x27;);
        const leadCares &#x3D; await this.queryRepository.findLeadCaresAssignedEmployee(user.id);
        if (!leadCares || leadCares.length &#x3D;&#x3D;&#x3D; 0) {
            throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.HAVE_NOT_ANY_LEAD) });
        }
        
        // Check ticket
        for (const leadCare of leadCares) {
            if (leadCare.lifeCycleStatus !&#x3D;&#x3D; LifeCycleStatusEnum.PENDING) {
                const history: ILeadCareProcessed[] &#x3D; leadCare.processedHistory || [];
                history.push({
                    id: uuid.v4(),
                    processedDate: new Date(),
                    processBy: user.id,
                    isReject: true,
                    isTimeOut: false,
                    causeReject: dto.causeReject
                } as ILeadCareProcessed);
                this.commandId &#x3D; uuid.v4();
                const model: any &#x3D; {
                    id: leadCare.id,
                    customerId: leadCare.customerId,
                    // processBy: null,
                    // timeOut: null,
                    modifiedBy: user.email,
                    lifeCycleStatus: LifeCycleStatusEnum.REMOVED,
                    reason: dto.causeReject[0],
                    processedHistory: history
                };
                // Get DVKH POS
                // let pos &#x3D; null;
                // try {
                //     const request &#x3D; {
                //         model: null,
                //         action: CommonConst.AGGREGATES.LEAD.REJECTED
                //     };
                //     pos &#x3D; await this.orgchartClient.sendDataPromise(request, CmdPatternConst.SERVICE.LEAD.LISTENER);
                // } catch (err) {
                //     this.loggerService.log(this.context, &#x27;[Error] Cannot find POS DVKH&#x27;, err);
                // }
                // flow mới update 20/2/2020
                // Yêu cầu bị trả về thì đặt lifeCycleStatus là removed. 
                // và gán vào pos DVKH 
                // không quan tâm tới trả về mấy lần
                // follow mới update 29/06/2020
                // Yêu cầu trả về vẫn đặt ở pos đó, status là removed
                // model.pos &#x3D; {
                //     id: pos.id,
                //     name: pos.name,
                //     code: pos.code
                // };
                this.commandId &#x3D; uuid.v4();
                await this.executeCommand(Action.FAIL, actionName, this.commandId, model);
            }
        }
        // this.employeeRepository.penalty({ id: user.id });
        return true;
    }

    async createLeadCare(dto: CreateLeadCareDto|ImportLeadCareFromPublicForm, actionName: string, timezoneclient?: string, userId?: string, clientId?: string, secretKey?: string) {
        // if (!CommonUtils.stringNotEmpty(dto.recaptcha)) {
        //     throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.INVALID_FIELD, &#x27;recaptcha&#x27;) });
        // }
        // if (dto.recaptcha) {
        //     let checkRecaptcha &#x3D; await this.validateRecaptcha(dto.recaptcha);
        //     if (!checkRecaptcha &#x3D;&#x3D;&#x3D; true) {
        //         throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.INVALID_FIELD, &#x27;recaptcha&#x27;) });
        //     }
        // }
        if (!isNullOrUndefined(userId)) {
            const employee: any &#x3D; await this.employeeRepository.findOne({id: userId});
            if (!isNullOrUndefined(employee) &amp;&amp; !isNullOrUndefined(employee.pos) &amp;&amp; dto instanceof CreateLeadCareDto) {
                dto.pos &#x3D; {
                    id: employee.pos?.id,
                    name: employee.pos?.name,
                    code: employee.pos?.code,
                    type: employee.pos?.type,
                    parentId: employee.pos?.parentId,
                    address: employee.pos?.personalInfo?.address || &#x27;&#x27;
                };

                dto.timeOut &#x3D; this.createTimeout(timezoneclient);
                dto.timezoneclient &#x3D; timezoneclient;
                dto.processBy &#x3D; userId;
                dto.lifeCycleStatus &#x3D; dto.lifeCycleStatus || LifeCycleStatusEnum.ASSIGNED;
                dto.modifiedBy &#x3D; userId;
                dto.employee &#x3D; {
                    id: employee.id,
                    name: employee.name,
                    code: employee.code,
                    email: employee.email
                };

            }
        }

        if (dto instanceof ImportLeadCareFromPublicForm) {
            dto.importedBy &#x3D; {
                clientId: clientId,
            }
        }
        this.loggerService.log(this.context, &#x27;create service&#x27;);

        // Check leadCare is hot or not.
        if (dto instanceof ImportLeadCareFromPublicForm &amp;&amp; !(dto.isHot || dto.repoConfigCode)) throw new BadRequestException();
        if (dto instanceof ImportLeadCareFromPublicForm &amp;&amp; dto.isHot &amp;&amp; dto.repoConfigCode) delete dto.repoConfigCode;
        // Check leadCare repository is available.
        if (dto instanceof ImportLeadCareFromPublicForm) {
            const repo &#x3D; await this.leadRepoCareRepository.findById(dto.repoId);

            if (!repo) throw new BadRequestException();

            if (dto.repoConfigCode &amp;&amp; !_.find(repo.configs, (config) &#x3D;&gt; config.code &#x3D;&#x3D;&#x3D; dto.repoConfigCode)) throw new BadRequestException();
        }

        this.commandId &#x3D; uuid.v4();
        if (isNullOrUndefined(dto.id) || dto.id.trim().length &#x3D;&#x3D;&#x3D; 0) {
            const now &#x3D; timezone().tz(timezoneclient);
            // const rawData &#x3D; await this.rawRepository.findOne({ id: dto.rawId });

            // if (isNullOrUndefined(rawData)) {
            //     throw new NotFoundException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, &#x27;raw&#x27;) });
            // }

            // dto.phone &#x3D; rawData.phone;
            // dto.email &#x3D; rawData.email;
            // dto.customerId &#x3D; rawData.customerId;
            // this.rawRepository.delete(rawData);

            const startWorkingTime &#x3D; now.clone().set({ hour: ConfigTimeConst.START_WORKING_HOURS, minute: 0, second: 0, millisecond: 0 });
            const endWorkingTime &#x3D; now.clone().set({ hour: ConfigTimeConst.END_WORKING_HOURS, minute: 0, second: 0, millisecond: 0 });

            let t0;
            let t1;
            let t2;
            let t3;
            if(!dto.createdDate){
                dto.createdDate &#x3D; now.format();
            }

            if (now.isBefore(startWorkingTime)) {
                t0 &#x3D; startWorkingTime;
            } else if (now.isBefore(endWorkingTime)) {
                t0 &#x3D; now;
            } else {
                startWorkingTime.add(1, &#x27;days&#x27;);
                endWorkingTime.add(1, &#x27;days&#x27;);
                t0 &#x3D; startWorkingTime;
            }
            dto.t0 &#x3D; t0.format();

            t1 &#x3D; t0.add(ConfigTimeConst.TICKET_GREEN_TIME, &#x27;minutes&#x27;);
            if (t1.isAfter(endWorkingTime)) {
                startWorkingTime.add(1, &#x27;days&#x27;);
                t1 &#x3D; startWorkingTime.clone().add(t1.diff(endWorkingTime, &#x27;minutes&#x27;), &#x27;minutes&#x27;);
                endWorkingTime.add(1, &#x27;days&#x27;);
            }
            dto.t1 &#x3D; t1.format();

            t2 &#x3D; t1.add(ConfigTimeConst.TICKET_YELLOW_TIME, &#x27;minutes&#x27;);
            if (t2.isAfter(endWorkingTime)) {
                startWorkingTime.add(1, &#x27;days&#x27;);
                t2 &#x3D; startWorkingTime.clone().add(t2.diff(endWorkingTime, &#x27;minutes&#x27;), &#x27;minutes&#x27;);
                endWorkingTime.add(1, &#x27;days&#x27;);
            }
            dto.t2 &#x3D; t2.format();

            t3 &#x3D; t2.add(ConfigTimeConst.TICKET_RED_TIME, &#x27;minutes&#x27;);
            if (t3.isAfter(endWorkingTime)) {
                startWorkingTime.add(1, &#x27;days&#x27;);
                t3 &#x3D; startWorkingTime.clone().add(t3.diff(endWorkingTime, &#x27;minutes&#x27;), &#x27;minutes&#x27;);
            }
            dto.t3 &#x3D; t3.format();

            dto.timezoneclient &#x3D; timezoneclient;

            dto.processedDate &#x3D; now.format();
            //add code
            dto.code &#x3D; await this.codeGenerateService.generateCode(CommonConst.LEAD_CARE_PREFIX);
            return await this.executeCommand(Action.CREATE, actionName, this.commandId, dto);
        } else {
            if (dto.type &#x3D;&#x3D;&#x3D; CommonConst.TYPE.PRIMARY) {
                const leadCare &#x3D; await this.queryRepository.findOne({id: dto.id});
                const newProcess &#x3D; {
                    isInNeed: dto.isInNeed,
                    reasonNoNeed: dto.reasonNoNeed,
                    otherReason: dto.otherReason,
                    interestedProduct: dto.interestedProduct,
                    direction: dto.direction,
                    needLoan: dto.needLoan,
                    isAppointment: dto.isAppointment,
                    isVisited: dto.isVisited,
                    note: dto.note,
                    isCalled: dto.isCalled,
                    callId: dto.isCalled ? dto.callId : &#x27;&#x27;,
                    startCall: dto.isCalled ? dto.startCall : null,
                    endCall: dto.isCalled ? dto.endCall : null,
                    answerTime: dto.isCalled ? dto.answerTime : 0,

                    updateDate: new Date(),

                };
                if (leadCare &amp;&amp; leadCare.callHistory &amp;&amp; leadCare.callHistory.length &gt; 0) {
                    dto.callHistory &#x3D; leadCare.callHistory;
                    dto.callHistory.push(newProcess);
                } else {
                    dto.callHistory &#x3D; [newProcess];
                }
            }
        }
        return await this.executeCommand(Action.UPDATE, actionName, this.commandId, dto);
    }

    async validateRecaptcha(recaptcha) {
        return new Promise(async (resolve, reject) &#x3D;&gt; {
            await request(this.configService.get(&#x27;URL_CAPTCHA&#x27;) + &#x27;?secret&#x3D;&#x27;
                + this.configService.get(&#x27;SECRET_KEY_CAPTCHA&#x27;) + &#x27;&amp;response&#x3D;&#x27;
                + recaptcha
                , function (error, response, body) {
                    body &#x3D; JSON.parse(body);
                    resolve(body.success);
                })
        });
    }

    async callingLeadCare(user: any, dto: any, actionName: string) {
        this.loggerService.log(this.context, &#x27;Update leadCare: start calling&#x27;);
        const leadCare &#x3D; await this.queryRepository.findOne({ id: dto.id });
        if (!leadCare) {
            return;
        }
        dto.isCalled &#x3D; true;
        return await this.executeCommand(Action.UPDATE, actionName, this.commandId, dto);
    }

    async completeLeadCare(dto: any, actionName: string) {
        this.loggerService.log(this.context, &#x27;complete service&#x27;);
        this.commandId &#x3D; uuid.v4();
        return await this.executeCommand(Action.COMPLETE, actionName, this.commandId, dto);
    }

    async failLeadCare(user: any, dto: any, actionName: string) {
        this.loggerService.log(this.context, &#x27;Fail service&#x27;);
        dto.modifiedBy &#x3D; user.name;
        this.commandId &#x3D; uuid.v4();
        return await this.executeCommand(Action.FAIL, actionName, this.commandId, dto);
    }

    async processLeadCare(user: any, dto: any, actionName: string) {
        this.loggerService.log(this.context, &#x27;Process service&#x27;);
        const isValidMarkProcessing &#x3D; await this.queryRepository.isValidMarkProcessing(user.id, dto.id);
        if (!isNullOrUndefined(isValidMarkProcessing) &amp;&amp; isValidMarkProcessing &#x3D;&#x3D;&#x3D; false) {
            throw new NotFoundException({ errors: ErrorConst.CommonError(ErrorConst.INVALID_MARK_PROCESSING) });
        }

        dto.modifiedBy &#x3D; user.name;
        this.commandId &#x3D; uuid.v4();
        return await this.executeCommand(Action.PROCESS, actionName, this.commandId, dto);
    }

    async unprocessLeadCare(user: any, dto: any, actionName: string) {
        this.loggerService.log(this.context, &#x27;Unprocess service&#x27;);
        this.commandId &#x3D; uuid.v4();
        dto.modifiedBy &#x3D; user.name;
        return await this.executeCommand(Action.UNPROCESS, actionName, this.commandId, dto);
    }

    async assignLeadCare(user: any, dto: any, actionName: string, timezoneclient: string) {
        this.loggerService.log(this.context, &#x27;assign service&#x27;);
        const leadCare &#x3D; await this.validateLeadCareToAssign(dto.id);
        await this.checkEmployeeExist(dto.assignFor);
        dto.timeOut &#x3D; await this.calTimeOutToProcess(leadCare, timezoneclient);
        dto.processBy &#x3D; dto.assignFor;
        dto.lifeCycleStatus &#x3D; LifeCycleStatusEnum.ASSIGNED;
        dto.status &#x3D; leadCare.status;
        dto.timezoneclient &#x3D; timezoneclient;
        dto.modifiedBy &#x3D; user.name;
        dto.assignedDate &#x3D; Date.now(),
            this.commandId &#x3D; uuid.v4();
        return await this.executeCommand(Action.ASSIGN, actionName, this.commandId, dto);
    }

    async customerClosedTicketLeadCare(user: any, dto: any, actionName: string, timezoneclient: string) {        
        this.loggerService.log(this.context, &#x27;customer closed ticket service&#x27;);
        dto.lifeCycleStatus &#x3D; LifeCycleStatusEnum.CUSTOMER_CLOSED_TICKET;
        let leadCare &#x3D; await this.getLeadCare(dto.id);
        // Validate value of cutomerId and current userId
        if(!user ||!leadCare|| user.id !&#x3D;&#x3D;leadCare.customerId) 
            throw new BadRequestException(&quot;Yêu cầu không hợp lệ.&quot;);
        dto.exploitHistory&#x3D; await this.updateExploitHistory(leadCare.exploitHistory,ExploitCareEnum.CUSTOMER_CLOSED_TICKET,&#x27;&#x27;);
        dto.timezoneclient &#x3D; timezoneclient;
        dto.status&#x3D;ExploitCareEnum.CUSTOMER_CLOSED_TICKET;
        dto.exploitStatus&#x3D;ExploitCareEnum.CUSTOMER_CLOSED_TICKET;        
            this.commandId &#x3D; uuid.v4();
        return await this.executeCommand(Action.CLOSED_TICKET, actionName, this.commandId, dto);        
    }

    async customerCancelTicketLeadCare(user: any, dto: any, actionName: string, timezoneclient: string) {
        this.loggerService.log(this.context, &#x27;customer cancel ticket service&#x27;);
        dto.lifeCycleStatus &#x3D; LifeCycleStatusEnum.CUSTOMER_CLOSED_TICKET;
        let leadCare &#x3D; await this.getLeadCare(dto.id);
        // Validate value of cutomerId and current userId
        if(!user ||!leadCare|| user.id !&#x3D;&#x3D;leadCare.customerId)
            throw new BadRequestException(&quot;Yêu cầu không hợp lệ.&quot;);
        dto.exploitHistory&#x3D; await this.updateExploitHistory(leadCare.exploitHistory,ExploitCareEnum.CANCEL,&#x27;&#x27;);
        dto.timezoneclient &#x3D; timezoneclient;
        dto.status&#x3D;ExploitCareEnum.CANCEL;
        dto.exploitStatus&#x3D;ExploitCareEnum.CANCEL;
        this.commandId &#x3D; uuid.v4();
        // send notification to client
        this.notificationClient.createNotificationCare(
          &quot;care_serviceRequestCancelByCustomer&quot;,
          null,
          leadCare.customerId,
          CommonConst.AGGREGATES.LEAD_CARE.NAME,
          leadCare.id,
          {
              code: leadCare.code
          }
        );
        return await this.executeCommand(Action.CLOSED_TICKET, actionName, this.commandId, dto);
    }

    async systemClosedTicketLeadCare(actionName: string) {
        
        this.loggerService.log(this.context, &#x27;system closed ticket service&#x27;);
        const leadCares &#x3D; await this.queryRepository.findMany({
            exploitStatus: ExploitCareEnum.DONE,
            updatedDate: {
              $lt: new Date(),
            }
        }, {}, {}, 1000);
        let result;
        leadCares.forEach(async leadCare &#x3D;&gt; {            
            let currentHistory &#x3D; leadCare.exploitHistory;
            
            //currentHistory &#x3D; currentHistory.sort((a,b) &#x3D;&gt; (a.updatedAt &gt; b.updatedAt) ? 1 : ((b.updatedAt &gt; a.updatedAt) ? -1 : 0));
            if(currentHistory[currentHistory.length-1].status&#x3D;&#x3D;&#x3D;ExploitCareEnum.DONE)
            {
                // get updateAt 
                let updatedAt &#x3D; currentHistory[currentHistory.length-1].updatedAt;
                if(updatedAt)
                {
                    updatedAt.setDate(updatedAt.getDate() + 3);
                    let checkDate &#x3D; new Date()&gt;&#x3D;updatedAt?true:false;
                    if(checkDate)
                    {
                        leadCare.exploitHistory&#x3D;  this.updateExploitHistory(leadCare.exploitHistory,ExploitCareEnum.SYSTEM_CLOSED_TICKET,&#x27;&#x27;);
                        leadCare.lifeCycleStatus &#x3D; LifeCycleStatusEnum.SYSTEM_CLOSED_TICKET;
                        leadCare.status&#x3D;ExploitCareEnum.SYSTEM_CLOSED_TICKET;
                        leadCare.exploitStatus&#x3D;ExploitCareEnum.SYSTEM_CLOSED_TICKET;
                        this.commandId &#x3D; uuid.v4();
                        await this.executeCommand(actionName, &#x27;&#x27;, this.commandId, leadCare);
                    }
                }
            }
           
        });        
    }

    private updateExploitHistory(
        history: IExploitHistory[] &#x3D; [],
        status: ExploitCareEnum,
        takeCareId?: string
    ) {
        const newHistory: IExploitHistory &#x3D; {
          status, updatedAt: new Date(), updatedBy: takeCareId, takeCareId
        };
        if (history.length &gt; 500) {
          history &#x3D; history.slice(history.length - 500);
        }
        history.push(newHistory);
        return history;
    }

    async pullLeadCare(user: any, actionName: string, timezoneclient: string, type?: string) {
        this.loggerService.log(this.context, &#x27;pull service&#x27;);
        const employee &#x3D; await this.validateEmployeeBeforePull(user.id, type);
        let sharedPool &#x3D; false;
        if (employee.pos.taxNumber &#x3D;&#x3D;&#x3D; &#x27;3602545493&#x27;) { // Hard code DXS Tax No to get leadCare from shared pool
            sharedPool &#x3D; true;
        }
        const leadCareReadyToAssign &#x3D; await this.queryRepository.findLeadCaresToAssign(employee.id, employee.pos.id, employee.pos.parentId, type, sharedPool);
        if (isNullOrUndefined(leadCareReadyToAssign) || leadCareReadyToAssign.length &lt;&#x3D; 0 ) {
            throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.HAVE_NOT_ANY_LEAD) });
        }

        const tpull &#x3D; timezone().tz(timezoneclient);
        const leadCaresPullLatest &#x3D; [];
        for (const leadCare of leadCareReadyToAssign) {
            leadCaresPullLatest.push(leadCare.id);
            const model &#x3D; {
                id: leadCare.id,
                // timeOut: await this.calTimeOutToProcess(leadCare, timezoneclient),
                timeOut: this.createTimeout(timezoneclient),
                processBy: employee.id,
                lifeCycleStatus: LifeCycleStatusEnum.ASSIGNED,
                status: leadCare.status,
                timezoneclient,
                modifiedBy: user.name,
                assignedDate: Date.now(),
            };
            // Start countdown
            // const runTime &#x3D; Number(ConfigTimeConst.TICKET_TIME_OUT) * 60000;
            // setTimeout(() &#x3D;&gt; this.timeoutLeadCare(leadCare.id), runTime);
            this.commandId &#x3D; uuid.v4();
            await this.executeCommand(Action.ASSIGN, actionName, this.commandId, model);
        }
        this.employeeRepository.update(
            {
                id: user.id,
                timePullLatest: tpull.format(&#x27;DD-MM-YYYY, h:mm:ss a&#x27;).toString(),
                leadCaresPullLatest
            }
        );
        return true;
    }

    /**
     * Chuyển ticket về pool chung (ticket quá hạn)
     * @param id
     */
    // async timeoutLeadCare(id: string) {
    //     const lead &#x3D; await this.queryRepository.findOne({ id });
    //     if (!lead) {
    //         return;
    //     }
    //     const timezoneclient &#x3D; lead.timezoneclient;
    //     const timeOut &#x3D; timezone(lead.timeOut).tz(timezoneclient);
    //     const now &#x3D; timezone().tz(timezoneclient);

    //     // if (lead.lifeCycleStatus &#x3D;&#x3D;&#x3D; LifeCycleStatusEnum.PROCESSING) {
    //     //     timeOut.add(ConfigTimeConst.TICKET_HOLDING_TIME, &#x27;minutes&#x27;);
    //     // }

    //     if (timeOut &lt;&#x3D; now) {
    //         // flow mới update 20/2/2020
    //         // Yêu cầu bị timeout thì đặt lifeCycleStatus là inpool, để NV khác pull ticket về
    //         // không quan tâm tới bị timeout mấy lần
    //         if (lead.lifeCycleStatus !&#x3D;&#x3D; LifeCycleStatusEnum.PENDING) {
    //             const history: ILeadProcessed[] &#x3D; lead.processedHistory || [];
    //             history.push({
    //                 id: uuid.v4(),
    //                 processedDate: new Date(),
    //                 processBy: lead.processBy,
    //                 isReject: false,
    //                 isTimeOut: true,
    //             } as ILeadProcessed);
    //             this.commandId &#x3D; uuid.v4();
    //             const model: any &#x3D; {
    //                 id: lead.id,
    //                 lifeCycleStatus: LifeCycleStatusEnum.IN_POOL,
    //                 modifiedBy: lead.modifiedBy,
    //                 processBy: null,
    //                 processedHistory: history
    //             };
    //             await this.executeCommand(Action.EXPIRED, &#x27;background action&#x27;, this.commandId, model);
    //         }
    //     }
        // }

    async pendingLeadCare(dto: any, actionName: string, user?: any) {
        this.loggerService.log(this.context, &#x27;pending service&#x27;);

        // dto.modifiedBy &#x3D; user.name
        this.commandId &#x3D; uuid.v4();
        return await this.executeCommand(Action.PENDING, actionName, this.commandId, dto);
    }

    async getEvents(leadCareId: string) {
        this.loggerService.log(this.context, &#x27;Get all history event&#x27;);
        return await this.eventStreamRepository.findAllEventStreamById(leadCareId);
    }

    async updateExploitStatusAssign(dto: any, actionName: string, user?:any){
        this.loggerService.log(this.context, &#x27;Update Exploit Status leadCare&#x27;);
        this.commandId &#x3D; uuid.v4();
        const usrId &#x3D; user.id

        const getUser &#x3D; await this.employeeClient.sendDataPromise(dto.takeCare,CmdPatternConst.SERVICE.EMPLOYEE.EMPLOYEE_GET_BY_ID)
        if(isEmpty(getUser)){
            throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.USER_TAKE_CARE_NOT_FOUND, &#x27;LeadCare&#x27;) });
        }
        const leadCareInf: any &#x3D; await this.queryRepository.findLeadCareById(dto.id);
        if (!!leadCareInf){
          const { exploitHistory } &#x3D; leadCareInf;
          exploitHistory.push({
            status: ExploitCareEnum.ASSIGN,
            updatedAt: new Date(),
            updatedBy: usrId,
            takeCareId: usrId,
          });

          leadCareInf.takeCare &#x3D; {
              id: dto.takeCare.id,
              name: dto.takeCare.name,
              phone:dto.takeCare.phone,
              email:dto.takeCare.email,
              code: dto.takeCare.code,
              role: getUser.role,
          }
          leadCareInf.exploitHistory &#x3D; exploitHistory;
          leadCareInf.exploitStatus &#x3D; ExploitCareEnum.ASSIGN;
          leadCareInf.status &#x3D; ExploitCareEnum.ASSIGN;
          leadCareInf.assignedDate &#x3D; new Date();
          leadCareInf.note &#x3D; dto.note;
          leadCareInf.dateEndWork &#x3D; dto.dateEndWork;
          if (leadCareInf.leadJob) {
            const leadCareUpdate: any &#x3D; {
              id: leadCareInf.leadJob.id,
              _id: leadCareInf.leadJob.id,
              takeCare: leadCareInf.takeCare,
              note: leadCareInf.note,
              assignedDate: leadCareInf.assignedDate,
              dateEndWork: leadCareInf.dateEndWork
            }

            await this.leadJobService.update(user, leadCareUpdate);
            // send notification to app bql
            this.notificationClient.createNotificationBQL({
              eventName: &quot;bql_assignWork_job&quot;,
              belongTo: dto.takeCare.id,
              entityId: dto.id,
              entityName: &quot;lead-cares&quot;,
              extraData: {
                id: dto.id,
                code: leadCareInf.code,
              }
            });
          } else {
            const leadJob &#x3D; await this.leadJobService.create(user, leadCareInf, &quot;create&quot;, false);
            leadCareInf.leadJob &#x3D; leadJob;
            // send notification to app bql
            this.notificationClient.createNotificationBQL({
              eventName: &quot;bql_assignWork_job&quot;,
              belongTo: dto.takeCare.id,
              entityId: dto.id,
              entityName: &quot;lead-cares&quot;,
              extraData: {
                id: dto.id,
                code: leadJob.code,
              }
            });

            this.notificationClient.createNotificationBQL({
              eventName: &quot;bql_assignWork&quot;,
              belongTo: user.id,
              entityId: dto.id,
              entityName: &quot;lead-cares&quot;,
              extraData: {
                id: dto.id,
                code: leadCareInf.code,
              }
            });
          }
         return this.executeCommand(Action.CHANGE_STATUS, actionName, this.commandId, leadCareInf);
    }
        throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, &#x27;LeadCare&#x27;) });
    }

    async updateLeadCare(dto: UpdateLeadCareDto, actionName: string, usrId: string){
        this.loggerService.log(this.context, &#x27;Update LeadCare info&#x27;);
        this.commandId &#x3D; uuid.v4();
        const leadCareInf &#x3D; await this.queryRepository.findLeadCareById(dto.id);
        Object.assign(leadCareInf, {...dto, modifiedBy: usrId});
        if (!!leadCareInf){
            return this.executeCommand(Action.UPDATE, actionName, this.commandId, leadCareInf);
        }
        throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, &#x27;LeadCare&#x27;) });
    }


    private async executeCommand(
        action: string,
        actionName: string,
        commandId: string,
        item: any
    ) {

        let commandObject &#x3D; null;
        switch (action) {
            case Action.CREATE:
                commandObject &#x3D; new CreateLeadCareCommand(actionName, commandId, item);
                break;
            case Action.CHANGE_STATUS:
                commandObject &#x3D; new ChangeStatusLeadCareCommand(actionName, commandId, item);
                break;
            case Action.UPDATE:
                commandObject &#x3D; new UpdateLeadCareCommand(actionName, commandId, item);
                break;
            case Action.REASSIGN:
                commandObject &#x3D; new ReassignLeadCareCommand(actionName, commandId, item);
                break;
            case Action.COMPLETE:
                commandObject &#x3D; new CompleteLeadCareCommand(actionName, commandId, item);
                break;
            case Action.FAIL:
                commandObject &#x3D; new FailLeadCareCommand(actionName, commandId, item);
                break;
            case Action.PROCESS:
                commandObject &#x3D; new ProcessLeadCareCommand(actionName, commandId, item);
                break;
            case Action.UNPROCESS:
                commandObject &#x3D; new UnprocessLeadCareCommand(actionName, commandId, item);
                break;
            case Action.ASSIGN:
                commandObject &#x3D; new AssignLeadCareCommand(actionName, commandId, item);
                break;
            case Action.PENDING:
                commandObject &#x3D; new PendingLeadCareCommand(actionName, commandId, item);
                break;
            case Action.EXPIRED:
                commandObject &#x3D; new ExpiredLeadCareCommand(actionName, commandId, item);
                break;
            case Action.CLOSED_TICKET:
                commandObject &#x3D; new CustomerClosedTicketCareCommand(actionName, commandId, item);
                break;
            default:
                break;
        }

        return await this.commandBus.execute(commandObject)
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    private calTimeOutToProcess(leadCare: any, timezoneclient: string) {
        const ta &#x3D; timezone().tz(timezoneclient);
        const startWorkingTime &#x3D; ta.clone().set({ hour: ConfigTimeConst.START_WORKING_HOURS, minute: 0, second: 0, millisecond: 0 });
        const endWorkingTime &#x3D; ta.clone().set({ hour: ConfigTimeConst.END_WORKING_HOURS, minute: 0, second: 0, millisecond: 0 });

        let timeOut;
        const t2 &#x3D; timezone(leadCare.t2).tz(timezoneclient);
        if (t2 &gt; ta) {
            return leadCare.t3;
        } else {
            timeOut &#x3D; ta.add(ConfigTimeConst.TICKET_RED_TIME, &#x27;minutes&#x27;);
            if (timeOut.isAfter(endWorkingTime)) {
                startWorkingTime.add(1, &#x27;days&#x27;);
                const diff &#x3D; timeOut.diff(startWorkingTime, &#x27;minutes&#x27;);
                timeOut &#x3D; startWorkingTime.add(diff, &#x27;minutes&#x27;);
            }
        }
        return timeOut.format();
    }

    private createTimeout(timezoneclient: string) {
        const ta &#x3D; timezone().tz(timezoneclient);
        const timeOut &#x3D; ta.add(ConfigTimeConst.TICKET_TIME_OUT, &#x27;minutes&#x27;);
        return timeOut.format();
    }

    private async validateLeadCareToAssign(leadCareId: string) {
        const leadCare &#x3D; await this.queryRepository.findOne({ id: leadCareId });
        if (isNullOrUndefined(leadCare)) {
            throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, &#x27;ticket&#x27;) });
        } else if (leadCare.processBy !&#x3D; null) {
            throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.LEAD_ASSIGNED) });
        }
        return leadCare;
    }

    private async getLeadCare(leadCareId: string) {
        const leadCare &#x3D; await this.queryRepository.findOne({ id: leadCareId });                
        const newHistory: IExploitHistory &#x3D; {
            status:ExploitCareEnum.CUSTOMER_CLOSED_TICKET, updatedAt: new Date(), updatedBy: &quot;&quot;, takeCareId:&quot;&quot;
        };        
        if (isNullOrUndefined(leadCare)) {
            throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, &#x27;ticket&#x27;) });
        }       
        return leadCare;
    }

    private async checkEmployeeExist(employeeId: string) {
        const employee &#x3D; await this.employeeRepository.findOne({ id: employeeId });
        if (isNullOrUndefined(employee)) {
            throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, &#x27;employee&#x27;) });
        }
        return employee;
    }

    private async validateEmployeeBeforePull(employeeId: string, type: string) {
        const employee &#x3D; await this.employeeRepository.findOne({ id: employeeId });
        if (isNullOrUndefined(employee)) {
            throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, &#x27;employee&#x27;) });
        }

        // Không check isPenalty nữa - O2OWADMIN-619
        // if (employee.isPenalty &amp;&amp; employee.isPenalty &#x3D;&#x3D;&#x3D; true) {
        //     throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.EMPLOYEE_PENALTY) });
        // }

        if (isNullOrUndefined(employee.pos) || isNullOrUndefined(employee.pos.id)) {
            throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.EMPLOYEE_NOT_ASSIGN_POS) });
        }


        const isValidBeforePull &#x3D; await this.queryRepository.isValidBeforePull(employeeId, type);
        if (isValidBeforePull &#x3D;&#x3D;&#x3D; false) {
            throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.EMPLOYEE_NOT_PROCESSED_YET) });
        }

        return employee;
    }

    async updateStatusLeadCare() {
        const all &#x3D; await this.queryRepository.findAll();

        if (all.length &#x3D;&#x3D;&#x3D; 0) {
            return;
        }

        const yellow &#x3D; [];
        const red &#x3D; [];
        // let warning &#x3D; [];
        await all.forEach(leadCare &#x3D;&gt; {
            const timezoneclient &#x3D; leadCare.timezoneclient;
            const now &#x3D; timezone().tz(timezoneclient);
            const t1 &#x3D; timezone(leadCare.t1).tz(timezoneclient);
            const t2 &#x3D; timezone(leadCare.t2).tz(timezoneclient);
            if (t1 &lt;&#x3D; now &amp;&amp; now &lt; t2) {
                yellow.push(leadCare.id);
            } else if (t2 &lt;&#x3D; now) {
                red.push(leadCare.id);
                // if(leadCare.processBy) warning.push(leadCare);
            }
        });

        if (yellow.length &gt; 0) {
            await this.queryRepository.updateStatus({ ids: yellow, status: StatusEnum.YELLOW });
        }

        if (red.length &gt; 0) {
            await this.queryRepository.updateStatus({ ids: red, status: StatusEnum.RED });
        }

        // warning.forEach(item &#x3D;&gt; {
        //     const msg &#x3D; {
        //         title: &#x60;Ticket sắp hết hạn khai thác.&#x60;,
        //         content: &#x60;Ticket của bạn sắp hết hạn xử lý. Hãy nhanh khai thác nếu không sẽ bị không thể tiếp tục xử lý&#x60;,
        //         entityName: &#x27;leadCare&#x27;,
        //         entityId: item.id,
        //         eventName: &#x27;leadCareWarning&#x27;
        //     }
        //     const sender &#x3D; {};
        //     const receivers &#x3D; [item.processBy];
        //     this.notifyService.subscribe(CmdPatternConst.SERVICE.NOTIFICATION.NOTIFY, {msg, sender, receivers});
        // });
    }

    async timeout() {
        const leadCareProcess &#x3D; await this.queryRepository.findLeadCaresAssigned();
        if (leadCareProcess.length &#x3D;&#x3D;&#x3D; 0) {
            return;
        }
        for (const leadCare of leadCareProcess) {
            const timezoneclient &#x3D; leadCare.timezoneclient;
            const timeOut &#x3D; timezone(leadCare.timeOut).tz(timezoneclient);
            const now &#x3D; timezone().tz(timezoneclient);

            if (timeOut &lt;&#x3D; now) {
                // flow mới update 20/2/2020
                // Yêu cầu bị timeout thì đặt lifeCycleStatus là inpool, để NV khác pull ticket về
                // không quan tâm tới bị timeout mấy lần
                if (leadCare.lifeCycleStatus !&#x3D;&#x3D; LifeCycleStatusEnum.PENDING) {
                    const history: ILeadCareProcessed[] &#x3D; leadCare.processedHistory || [];
                    history.push({
                        id: uuid.v4(),
                        processedDate: new Date(),
                        processBy: leadCare.processBy,
                        isReject: false,
                        isTimeOut: true,
                    } as ILeadCareProcessed);
                    this.commandId &#x3D; uuid.v4();
                    const model: any &#x3D; {
                        id: leadCare.id,
                        lifeCycleStatus: LifeCycleStatusEnum.IN_POOL,
                        modifiedBy: leadCare.modifiedBy,
                        processBy: null,
                        processedHistory: history
                    };
                    await this.executeCommand(Action.EXPIRED, &#x27;background action&#x27;, this.commandId, model);
                }
            }
        }
    }
    async importDemandTicket(files: any , dto: ImportLeadCareDemandDto, actionName: string, timezoneclient?: string, userId?: string){
        const history &#x3D; [];
        let sanList &#x3D; [{
            id: &#x27;dxs-shared&#x27;,
            name: &#x27;Các sàn DXS&#x27;,
            code: &#x27;DXS-Virtual&#x27;,
            parentId: &#x27;&#x27;,
            type: &#x27;SAN&#x27;
        }];
        if (CommonUtils.stringNotEmpty(dto.exchangeId)) {
            // console.log(11111111111, dto.exchangeId);
            sanList &#x3D; await this.orgchartClient.sendDataPromise({posId: dto.exchangeId}, CmdPatternConst.LISTENER.GET_POS_BY_QUERY);
        }
        // console.log(JSON.stringify(sanList));
        const workbook &#x3D; XLSX.read(files[0].buffer, { type: &#x27;buffer&#x27; });
        const sheet_name_list &#x3D; workbook.SheetNames;
        const leadCareList: any &#x3D; await XLSX.utils.sheet_to_json(workbook.Sheets[sheet_name_list[0]], { range: 1 });
        let employee: any &#x3D; await this.employeeRepository.findOne({ id: userId });
        if (isNullOrUndefined(employee)) {
            employee &#x3D; {
                id: userId
            };
        } else {
            employee &#x3D; {
                id: employee.id,
                name: employee.name,
                code: employee.code,
                email: employee.email
            };
        }

        for (const [i, leadCare] of leadCareList.entries()) {
            leadCare.pos &#x3D; {
                id: sanList[0].id,
                name: sanList[0].name,
                parentId: sanList[0].parentId,
                code: sanList[0].code,
                type: sanList[0].type
            };
            leadCare.phone &#x3D; leadCare.phone ? leadCare.phone.toString() : &#x27;&#x27;;
            leadCare.email &#x3D; leadCare.email ? leadCare.email.toString().toLowerCase() : &#x27;&#x27;;
            leadCare.type &#x3D; CommonConst.TYPE.PRIMARY;
            leadCare.timestamp &#x3D; dto.timestamp;
            leadCare.source &#x3D; dto.resource;
            leadCare.customer &#x3D; {
                personalInfo: {
                    name: leadCare.name,
                    phone: leadCare.phone,
                    email: leadCare.email,
                    identities: [
                        {
                            type: &#x27;CMND&#x27;,
                            value: leadCare.identity
                        }
                    ]
                }
            };
            leadCare.property &#x3D; {
                code: leadCare.propertyCode,
                project: { id: leadCare.projectId, name: leadCare.projectName }
            };
            leadCare.createdDate &#x3D; +leadCare.createdDate;
            leadCare.importedBy &#x3D; employee || {};
            const validate &#x3D; this.validateLeadCare(leadCare);
            if (validate) {
                history.push({
                    line: i + 1,
                    error: validate
                });
            } else {
                await this.createLeadCareImport(leadCare, actionName, timezoneclient);
            }
        }
        const readModel &#x3D; {
            fileName: files[0].originalname,
            processBy: employee || {},
            success: leadCareList.length - history.length,
            fail: history.length,
            type: CommonConst.TYPE.PRIMARY,
            createdDate: new Date(),
            updatedDate: new Date(),
            description: history
        };
        await this.historyImportService.create(readModel);
    }
    private async createLeadCareImport(leadCare , actionName , timezoneclient): Promise&lt;any&gt; {
        return await this.createLeadCare(leadCare, actionName, timezoneclient);
    }
    private validateLeadCare(leadCare) {
        if(isNullOrUndefined(leadCare.phone) || !leadCare.phone.toString().match(CommonConst.REGEX_VN_PHONE)){
            return &#x27;Số điện thoại sai&#x27;;
        }
        if(leadCare.email &amp;&amp; !leadCare.email.toString().match(CommonConst.REGEX_EMAIL)){
            return &#x27;Email sai&#x27;;
        }
        return &#x27;&#x27;;
    }

    async importLeadCareFromExcel(files: any, options: ImportLeadCareAsExcelDto, actionName: string, timezoneclient: string, user: any) {
        const workbook &#x3D; XLSX.readCare(files[0].buffer, { type: &#x27;buffer&#x27; });
        const sheets &#x3D; workbook.SheetNames;
        let leadCares &#x3D; await XLSX.utils.sheet_to_json(workbook.Sheets[sheets[0]], { range: 1 });

        leadCares &#x3D; leadCares.map(leadCare &#x3D;&gt; {
            return {
                ...leadCare,
                repoId: options.repoId,
                repoConfigCode: options.repoConfigCode,
                importedBy: {
                    id: user.id,
                    name: user.name,
                },
            };
        });

        for (const leadCare of leadCares) {
            await this.createLeadCareImport(leadCare, actionName, timezoneclient);
        }
    }

    async addAnswerConversation(user, dto) {
        const transfer &#x3D; await this.queryRepository.findOne({id: dto.id, customerId: user.id})
        if (!transfer) {
            throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, &#x27;Yêu cầu chuyển nhượng&#x27;) });
        }
        const conversation &#x3D; {
            user: {
                id: user.id,
                name: user.name
            },
            status: ConversationEnum.ANSWER,
            message: dto.conversation.message,
            files: dto.conversation.files,
            createdDate: new Date()
        }
        let updateQuery &#x3D; {
            $set : {exploitStatus: ExploitCareEnum.ADD_ANSWER, updatedDate: new Date(), modifiedBy: user.id},
            $push: {conversations: conversation}
        };
        await this.queryRepository.updateByQuery({id: dto.id}, updateQuery);
        return {success: true};
    }

    async HandleTransferRequest(user, dto, actionName) {
        const request &#x3D; await this.queryRepository.findOne({id: dto.id, repoType: LeadRepoCareEnum.TRANSFER})
        if (!request) {
            throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, &#x27;LeadCare&#x27;) });
        }
        let updateQuery &#x3D; {};
        let payload &#x3D; {};
        switch (dto.type) {
            // Đang xử lý
            case ExploitCareEnum.PROCESSING:
                if (request.exploitStatus &#x3D;&#x3D;&#x3D; ExploitCareEnum.DONE || request.exploitStatus &#x3D;&#x3D;&#x3D; ExploitCareEnum.CANCEL) {
                    throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.STATUS_NOT_INVALID) });
                }
                updateQuery &#x3D; {$set : {exploitStatus: ExploitCareEnum.PROCESSING, note: dto.note, updatedDate: new Date(), modifiedBy: user.id}};
                payload &#x3D; {
                    id: &#x27;&#x27;,
                    entityName: &#x27;notificationManual&#x27;,
                    eventName: &#x27;notificationManual&#x27;,
                    listUserId: [request.customerId],
                    extraData: {
                        title: &#x27;Yêu cầu chuyển nhượng đang được xử lý&#x27;,
                        content: &#x60;Yêu cầu ${request.code} của bạn đang được xử lý&#x60;
                    }
                };
                this.notificationClient.createNotificationMultipleCare(payload);
                break;
            // Hoàn thành
            case ExploitCareEnum.DONE:
                if (request.exploitStatus !&#x3D;&#x3D; ExploitCareEnum.PROCESSING &amp;&amp; request.exploitStatus !&#x3D;&#x3D; ExploitCareEnum.ADD_QUESTION &amp;&amp; request.exploitStatus !&#x3D;&#x3D; ExploitCareEnum.ADD_ANSWER) {
                    throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.STATUS_NOT_INVALID) });
                }
                updateQuery &#x3D; {$set : {exploitStatus: ExploitCareEnum.DONE, note: dto.note, updatedDate: new Date(), modifiedBy: user.id}};

                payload &#x3D; {
                    id: &#x27;&#x27;,
                    entityName: &#x27;notificationManual&#x27;,
                    eventName: &#x27;notificationManual&#x27;,
                    listUserId: [request.customerId],
                    extraData: {
                        title: &#x27;Yêu cầu chuyển nhượng đã hoàn thành&#x27;,
                        content: &#x60;Yêu cầu ${request.code} của bạn đã hoàn thành&#x60;
                    }
                };
                this.notificationClient.createNotificationMultipleCare(payload);
                break;
            // Trả về
            case ExploitCareEnum.CANCEL:
                if (request.exploitStatus !&#x3D;&#x3D; ExploitCareEnum.PROCESSING &amp;&amp; request.exploitStatus !&#x3D;&#x3D; ExploitCareEnum.ADD_QUESTION &amp;&amp; request.exploitStatus !&#x3D;&#x3D; ExploitCareEnum.ADD_ANSWER) {
                    throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.STATUS_NOT_INVALID) });
                }
                updateQuery &#x3D; {$set : {exploitStatus: ExploitCareEnum.CANCEL, note: dto.note, reason: dto.reason, updatedDate: new Date(), modifiedBy: user.id}};
                payload &#x3D; {
                    id: &#x27;&#x27;,
                    entityName: &#x27;notificationManual&#x27;,
                    eventName: &#x27;notificationManual&#x27;,
                    listUserId: [request.customerId],
                    extraData: {
                        title: &#x27;Yêu cầu chuyển nhượng đã bị trả về&#x27;,
                        content: &#x60;Yêu cầu ${request.code} của bạn đã bị trả về. Lý do: ${dto.reason}&#x60;
                    }
                };
                this.notificationClient.createNotificationMultipleCare(payload);
                break;
            case &#x27;update&#x27;: {
                if (request.exploitStatus !&#x3D;&#x3D; ExploitCareEnum.PROCESSING &amp;&amp; request.exploitStatus !&#x3D;&#x3D; ExploitCareEnum.ADD_QUESTION &amp;&amp; request.exploitStatus !&#x3D;&#x3D; ExploitCareEnum.ADD_ANSWER) {
                    throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.STATUS_NOT_INVALID) });
                }
                if (dto.conversation &amp;&amp; dto.conversation.message) {
                    const conversation &#x3D; {
                        user: {
                            id: user.id,
                            name: user.name
                        },
                        status: ConversationEnum.QUESTION,
                        message: dto.conversation.message,
                        files: dto.conversation.files,
                        createdDate: new Date()
                    }
                    updateQuery &#x3D; {
                        $set : {exploitStatus: ExploitCareEnum.ADD_QUESTION, note: dto.note, updatedDate: new Date(), modifiedBy: user.id},
                        $push: {conversations: conversation}
                    };

                    // Gửi thông báo yêu cầu bổ sung
                    payload &#x3D; {
                        id: &#x27;&#x27;,
                        entityName: &#x27;transfer_conversation&#x27;,
                        eventName: &#x27;transfer_conversation&#x27;,
                        listUserId: [request.customerId],
                        extraData: {
                            id: request.id,
                            title: &#x27;Yêu cầu bổ sung thông tin&#x27;,
                            content: dto.conversation.message
                        }
                    };
                    this.notificationClient.createNotificationMultipleCare(payload);
                } else {
                    updateQuery &#x3D; {$set : {note: dto.note, updatedDate: new Date(), modifiedBy: user.id}};
                }
                break;
            }
            default: {
                throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, &#x27;Type&#x27;) });
            }
        }
        await this.queryRepository.updateByQuery({id: dto.id}, updateQuery);
        return { success: true };
    }

    async updateExploitStatus(dto: any, actionName: string, usrId:any){
        this.loggerService.log(this.context, &#x27;Update Exploit Status leadCare&#x27;);
        this.commandId &#x3D; uuid.v4();
        const leadCareInf: any &#x3D; await this.queryRepository.findLeadCareById(dto.id);
        if (!!leadCareInf){
          const {repoType, exploitHistory, customerId, code, processedHistory} &#x3D; leadCareInf;
          exploitHistory.push({
            status: dto.status as ExploitCareEnum,
            updatedAt: new Date(),
            updatedBy: usrId,
            takeCareId: usrId,
          });

          const exploitStatus &#x3D; dto.status
          Object.assign(dto, { exploitHistory});
        if(dto.status &#x3D;&#x3D;&#x3D; ExploitCareEnum.CANCEL) {
            if(leadCareInf.customData &amp;&amp; leadCareInf.customData.contractId) {
              switch (leadCareInf.repoType) {
                case  LeadRepoCareEnum.TRANSFER:
                  this.primaryContractClient.sendData({
                    id: leadCareInf.customData.contractId,
                    status: false
                  }, CmdPatternConst.PRIMARY_CONTRACT.PRIMARY_CONTRACT_UPDATE_TRANSFER_CONFIRM);
                  break;
                case  LeadRepoCareEnum.PROJECT:
                  this.primaryContractClient.sendData({
                    id: leadCareInf.customData.contractId,
                    status: false
                  }, CmdPatternConst.PRIMARY_CONTRACT.PRIMARY_CONTRACT_UPDATE_DEPOSIT_CONFIRM);
                  break;
                default:
                  break;
              }
            }
            if(leadCareInf.customerId) {
                // Thông báo cho khách hàng
                this.notificationClient.createNotificationCare(
                  &quot;care_serviceRequestCancelByBql&quot;,
                  null,
                  leadCareInf.customerId,
                  CommonConst.AGGREGATES.LEAD_CARE.NAME,
                  leadCareInf.id,
                  {
                      code: leadCareInf.code,
                      id: leadCareInf.id,
                      causeReject: dto.causeReject
                  }
                );
            }
        } else if (dto.status &#x3D;&#x3D;&#x3D; ExploitCareEnum.SURVEY) {
            if (!leadCareInf.canSurvey) {
                throw new BadRequestException({ errors: ErrorConst.Error(ErrorConst.LEAD_CARE_CAN_NOT_SURVEY) });
            }
            dto.isRequireSurvey &#x3D; true;
            if(leadCareInf.customerId) {
                // Thông báo cho khách hàng
                this.notificationClient.createNotificationCare(
                  &quot;care_serviceRequestSurveyByBql&quot;,
                  null,
                  leadCareInf.customerId,
                  CommonConst.AGGREGATES.LEAD_CARE.NAME,
                  leadCareInf.id,
                  {
                      code: leadCareInf.code,
                      id: leadCareInf.id,
                  }
                );
            }
        }
          return this.executeCommand(Action.CHANGE_STATUS, actionName, this.commandId, {...dto, repoType, usrId, customerId, code, processedHistory ,exploitStatus });
    }
        throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, &#x27;LeadCare&#x27;) });
    }

    async updateRateInfoLeadCareById(rateData: any) {
        let leadCare: any &#x3D; await this.queryRepository.findOne({ id: rateData.leadCareId });
        leadCare.rateValue &#x3D; rateData.rateValue;
        leadCare.rateDescription &#x3D; rateData.rateDescription;

        this.commandId &#x3D; uuid.v4();
        return await this.executeCommand(Action.UPDATE, &#x27;&#x27;, this.commandId, leadCare);
    }

    async takeSurvey(id, surveys) {
        let leadCare: any &#x3D; await this.queryRepository.findOne({ id });
        if (!leadCare || !leadCare.canSurvey) {
            throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, &#x27;LeadCare&#x27;) });
        }
        leadCare.surveyAnswers &#x3D; surveys;
        leadCare.submitSurvey &#x3D; true;

        // Thông báo cho ban quản lý
        const bqls &#x3D; await this.propertyClient.sendDataPromise(leadCare.project.id,CmdPatternConst.PROJECT.GET_ACCOUNT_BQL_MEMBER)
        for (let bql of bqls) {
            this.notificationClient.createNotificationBQL({
                eventName: &quot;bql_serviceRequestSurveyByCustomer&quot;,
                belongTo: bql.id,
                entityId: leadCare.id,
                entityName: CommonConst.AGGREGATES.LEAD_CARE.NAME,
                extraData: {
                    customerName: leadCare.name,
                    id: leadCare.id,
                }
            });
        }

        this.commandId &#x3D; uuid.v4();
        return await this.executeCommand(Action.UPDATE, &#x27;&#x27;, this.commandId, leadCare);
    }
}
</code></pre>
    </div>

</div>







                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'LeadCareDomainService.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
