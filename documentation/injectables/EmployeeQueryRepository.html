<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-lead documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-lead documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">







<ol class="breadcrumb">
  <li>Injectables</li>
  <li>EmployeeQueryRepository</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/employee/repository/query.repository.ts</code>
        </p>




            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#create">create</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#delete">delete</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#find">find</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAll">findAll</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAndUpdate">findAndUpdate</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findEmployeesAvailable">findEmployeesAvailable</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findManyWithSort">findManyWithSort</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findOne">findOne</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getManagerByPos">getManagerByPos</a>
                            </li>
                            <li>
                                <a href="#getStaffByIds">getStaffByIds</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#penalty">penalty</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#resetPenalty">resetPenalty</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#saveAll">saveAll</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#update">update</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateMany">updateMany</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section>
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(readModel: <a href="../interfaces/IEmployeeDocument.html">Model<IEmployeeDocument></a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="10" class="link-to-prism">src/modules/employee/repository/query.repository.ts:10</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>readModel</td>
                                                  
                                                        <td>
                                                                        <code><a href="../interfaces/IEmployeeDocument.html" target="_self" >Model&lt;IEmployeeDocument&gt;</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section>
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="create"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            create
                        </b>
                        <a href="#create"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>create(readmodel)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="33"
                            class="link-to-prism">src/modules/employee/repository/query.repository.ts:33</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>readmodel</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="../interfaces/IEmployeeDocument.html" target="_self" >Promise&lt;IEmployeeDocument&gt;</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="delete"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            delete
                        </b>
                        <a href="#delete"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>delete(model)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="51"
                            class="link-to-prism">src/modules/employee/repository/query.repository.ts:51</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>model</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;any&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="find"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            find
                        </b>
                        <a href="#find"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>find(query)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="79"
                            class="link-to-prism">src/modules/employee/repository/query.repository.ts:79</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;any[]&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAll"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findAll
                        </b>
                        <a href="#findAll"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAll()</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="17"
                            class="link-to-prism">src/modules/employee/repository/query.repository.ts:17</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="../interfaces/IEmployeeDocument.html" target="_self" >Promise&lt;IEmployeeDocument[]&gt;</a></code>

                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAndUpdate"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findAndUpdate
                        </b>
                        <a href="#findAndUpdate"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAndUpdate(model: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="89"
                            class="link-to-prism">src/modules/employee/repository/query.repository.ts:89</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>model</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findEmployeesAvailable"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findEmployeesAvailable
                        </b>
                        <a href="#findEmployeesAvailable"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findEmployeesAvailable(posId)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="107"
                            class="link-to-prism">src/modules/employee/repository/query.repository.ts:107</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>posId</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findManyWithSort"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findManyWithSort
                        </b>
                        <a href="#findManyWithSort"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findManyWithSort(query, projection?)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="165"
                            class="link-to-prism">src/modules/employee/repository/query.repository.ts:165</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>projection</td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findOne"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findOne
                        </b>
                        <a href="#findOne"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findOne(query)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="25"
                            class="link-to-prism">src/modules/employee/repository/query.repository.ts:25</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="../interfaces/IEmployeeDocument.html" target="_self" >Promise&lt;IEmployeeDocument&gt;</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getManagerByPos"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getManagerByPos
                        </b>
                        <a href="#getManagerByPos"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getManagerByPos(id)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="175"
                            class="link-to-prism">src/modules/employee/repository/query.repository.ts:175</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>id</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getStaffByIds"></a>
                    <span class="name">
                        <b>
                            getStaffByIds
                        </b>
                        <a href="#getStaffByIds"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getStaffByIds(ids)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="171"
                            class="link-to-prism">src/modules/employee/repository/query.repository.ts:171</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>ids</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="penalty"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            penalty
                        </b>
                        <a href="#penalty"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>penalty(model)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="60"
                            class="link-to-prism">src/modules/employee/repository/query.repository.ts:60</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>model</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="resetPenalty"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            resetPenalty
                        </b>
                        <a href="#resetPenalty"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>resetPenalty()</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="69"
                            class="link-to-prism">src/modules/employee/repository/query.repository.ts:69</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="saveAll"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            saveAll
                        </b>
                        <a href="#saveAll"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>saveAll(models: any[])</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="178"
                            class="link-to-prism">src/modules/employee/repository/query.repository.ts:178</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>models</td>
                                    <td>
                                            <code>any[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;any&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="update"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            update
                        </b>
                        <a href="#update"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>update(model)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="42"
                            class="link-to-prism">src/modules/employee/repository/query.repository.ts:42</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>model</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="../interfaces/IEmployeeDocument.html" target="_self" >Promise&lt;IEmployeeDocument&gt;</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateMany"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateMany
                        </b>
                        <a href="#updateMany"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateMany(query, model)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="156"
                            class="link-to-prism">src/modules/employee/repository/query.repository.ts:156</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>model</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="../interfaces/IEmployeeDocument.html" target="_self" >Promise&lt;IEmployeeDocument&gt;</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Model } from &#x27;mongoose&#x27;;
import { Inject, Injectable } from &#x27;@nestjs/common&#x27;;
import { IEmployeeDocument } from &#x27;../interfaces/document.interface&#x27;;
import _ &#x3D; require(&#x27;lodash&#x27;);
import { isNullOrUndefined } from &#x27;util&#x27;;
import { LifeCycleStatusEnum } from &#x27;../../../modules/shared/enum/life-cycle-status.enum&#x27;;
import { CommonConst } from &#x27;../../../modules/shared/constant/common.const&#x27;;

@Injectable()
export class EmployeeQueryRepository {

    constructor(
        @Inject(CommonConst.QUERY_MODEL_TOKEN)
        private readonly readModel: Model&lt;IEmployeeDocument&gt;
    ) { }

    async findAll(): Promise&lt;IEmployeeDocument[]&gt; {
        return await this.readModel.find()
            .exec()
            .then(result &#x3D;&gt; {
                return result;
            });
    }

    async findOne(query): Promise&lt;IEmployeeDocument&gt; {
        return await this.readModel.findOne(query)
            .exec()
            .then(result &#x3D;&gt; {
                return result;
            });
    }

    async create(readmodel): Promise&lt;IEmployeeDocument&gt; {
        return await this.readModel.create(readmodel)
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    async update(model): Promise&lt;IEmployeeDocument&gt; {
        return await this.readModel.update({ id: model.id }, model)
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    async delete(model): Promise&lt;any&gt; {
        return await this.readModel.deleteOne({ id: model.id })
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    async penalty(model) {
        await this.readModel.update(
            { id: model.id },
            { $set: { isPenalty: true } }
        ).catch((error) &#x3D;&gt; {
            return error;
        });
    }

    async resetPenalty() {
        await this.readModel.update(
            { isPenalty: true },
            { $set: { isPenalty: false } }
        ).catch((error) &#x3D;&gt; {
            return error;
        });
    }


    async find(query): Promise&lt;any[]&gt; {

        return await this.readModel.find(query)
            .exec()
            .then(result &#x3D;&gt; {
                // console.log(&#x27;employee find/res &#x3D;&gt;&#x27;, result);
                return result;
            });
    }

    async findAndUpdate(model: any) {
        model.isPenalty &#x3D; isNullOrUndefined(model.isPenalty) ? false : model.isPenalty;
        model.timePullLatest &#x3D; isNullOrUndefined(model.timePullLatest) ? &#x27;&#x27; : model.timePullLatest;
        model.active &#x3D; isNullOrUndefined(model.active) ? true : model.active;
        return await this.readModel.findOneAndUpdate(
            { id: model.id },
            model,
            { upsert: true }
        ).exec()
            .then((response) &#x3D;&gt; {
                // console.log(&#x27;res &#x3D;&gt;&#x27;, response);
                return response;
            }).catch((error) &#x3D;&gt; {
                // console.log(&#x27;error &#x3D;&gt;&#x27;, error);
                return error;
            });
    }

    async findEmployeesAvailable(posId) {
        return await this.readModel.aggregate([
            {
                $match: {
                    &#x27;pos.id&#x27;: posId,
                    &#x27;isPenalty&#x27;: { $ne: true }
                }
            },
            {
                $lookup: {
                    from: &#x27;leads&#x27;,
                    let: { employeeId: &#x27;$id&#x27; },
                    pipeline: [
                        {
                            $match:
                            {
                                $expr:
                                {
                                    $and:
                                        [
                                            { $eq: [&#x27;$$employeeId&#x27;, &#x27;$processBy&#x27;] },
                                            {
                                                $or: [
                                                    { $eq: [&#x27;$lifeCycleStatus&#x27;, LifeCycleStatusEnum.ASSIGNED] },
                                                    { $eq: [&#x27;$lifeCycleStatus&#x27;, LifeCycleStatusEnum.PROCESSING] },
                                                ]

                                            }
                                        ]
                                }
                            }
                        },
                    ],
                    as: &#x27;leads&#x27;
                }
            },
            {
                $project: {
                    id: {
                        $cond: {
                            if:  { $anyElementTrue: [&#x27;$leads&#x27;] },
                            then: null,
                            else: &#x27;$id&#x27;
                        }
                    },
                }
            }
        ]).allowDiskUse(true);
    }
    async updateMany(query, model): Promise&lt;IEmployeeDocument&gt; {
      return await this.readModel.updateMany(query, model)
          .then((response) &#x3D;&gt; {
              return response;
          }).catch((error) &#x3D;&gt; {
              return error;
          });
    }

    async findManyWithSort(query, projection?) {
      return this.readModel
          .find(query, projection)
          .sort({ id: -1 });
    }

    getStaffByIds(ids) {
        return this.readModel.find({ id: { $in: ids } }, { code: 1, name: 1 });
    }

    async getManagerByPos(id) {
      return this.readModel.find({ &quot;pos.id&quot;: id, managerAt: id }, { code: 1, name: 1 });
    }
    async saveAll(models: any[]): Promise&lt;any&gt; {
      if (models &amp;&amp; models.length) {
        return await this.readModel.bulkWrite(
          models.map((model) &#x3D;&gt; {
            return {
              updateOne: {
                filter: { id: model.id },   // Detect Update by id
                update: { $set: model },
                upsert: true,  
              }
            };
          })
        );
      } else {
        return [];
      }
    }
}
</code></pre>
    </div>

</div>







                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'EmployeeQueryRepository.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
