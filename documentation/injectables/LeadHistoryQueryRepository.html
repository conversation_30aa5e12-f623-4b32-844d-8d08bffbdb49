<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-lead documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-lead documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">







<ol class="breadcrumb">
  <li>Injectables</li>
  <li>LeadHistoryQueryRepository</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/lead-history/repository/query.repository.ts</code>
        </p>




            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#count">count</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#countCancelTicketsConsignmentByUser">countCancelTicketsConsignmentByUser</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#countCancelTicketsDemandByUser">countCancelTicketsDemandByUser</a>
                            </li>
                            <li>
                                <a href="#countReport">countReport</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#countStatusForLeads">countStatusForLeads</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#countStatusToDayByEmployee">countStatusToDayByEmployee</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#create">create</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#deleteMany">deleteMany</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#filter">filter</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#filterReport">filterReport</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAll">findAll</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#findAllStatus">findAllStatus</a>
                            </li>
                            <li>
                                <a href="#findLeadByCustomerId">findLeadByCustomerId</a>
                            </li>
                            <li>
                                <a href="#findLeadByEmployeeId">findLeadByEmployeeId</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getHistoryByCustomer">getHistoryByCustomer</a>
                            </li>
                            <li>
                                    <span class="modifier">Protected</span>
                                <a href="#transformSort">transformSort</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section>
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(readModel: Model<ILeadHistoryDocument>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="11" class="link-to-prism">src/modules/lead-history/repository/query.repository.ts:11</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>readModel</td>
                                                  
                                                        <td>
                                                                    <code>Model&lt;ILeadHistoryDocument&gt;</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section>
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="count"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            count
                        </b>
                        <a href="#count"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>count(query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="140"
                            class="link-to-prism">src/modules/lead-history/repository/query.repository.ts:140</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;number&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="countCancelTicketsConsignmentByUser"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            countCancelTicketsConsignmentByUser
                        </b>
                        <a href="#countCancelTicketsConsignmentByUser"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>countCancelTicketsConsignmentByUser(query)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="119"
                            class="link-to-prism">src/modules/lead-history/repository/query.repository.ts:119</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="countCancelTicketsDemandByUser"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            countCancelTicketsDemandByUser
                        </b>
                        <a href="#countCancelTicketsDemandByUser"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>countCancelTicketsDemandByUser(query)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="98"
                            class="link-to-prism">src/modules/lead-history/repository/query.repository.ts:98</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="countReport"></a>
                    <span class="name">
                        <b>
                            countReport
                        </b>
                        <a href="#countReport"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>countReport(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, posInPool: any[])</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="377"
                            class="link-to-prism">src/modules/lead-history/repository/query.repository.ts:377</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>posInPool</td>
                                    <td>
                                            <code>any[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>[]</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="countStatusForLeads"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            countStatusForLeads
                        </b>
                        <a href="#countStatusForLeads"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>countStatusForLeads(leadIds, lifeCycleStatus)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="84"
                            class="link-to-prism">src/modules/lead-history/repository/query.repository.ts:84</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>leadIds</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>lifeCycleStatus</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="countStatusToDayByEmployee"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            countStatusToDayByEmployee
                        </b>
                        <a href="#countStatusToDayByEmployee"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>countStatusToDayByEmployee(employeeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String" target="_blank">String</a>, lifeCycleStatus: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="44"
                            class="link-to-prism">src/modules/lead-history/repository/query.repository.ts:44</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>employeeId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String" target="_blank" >String</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>lifeCycleStatus</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="create"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            create
                        </b>
                        <a href="#create"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>create(readmodel)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="18"
                            class="link-to-prism">src/modules/lead-history/repository/query.repository.ts:18</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>readmodel</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;ILeadHistoryDocument&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deleteMany"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            deleteMany
                        </b>
                        <a href="#deleteMany"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deleteMany()</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="27"
                            class="link-to-prism">src/modules/lead-history/repository/query.repository.ts:27</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;ILeadHistoryDocument&gt;</code>

                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="filter"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            filter
                        </b>
                        <a href="#filter"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>filter(query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, isPaging: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="175"
                            class="link-to-prism">src/modules/lead-history/repository/query.repository.ts:175</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>isPaging</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>false</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="filterReport"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            filterReport
                        </b>
                        <a href="#filterReport"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>filterReport(user: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, posId: string[], isPaging: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="233"
                            class="link-to-prism">src/modules/lead-history/repository/query.repository.ts:233</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>posId</td>
                                    <td>
                                            <code>string[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>[]</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>isPaging</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>false</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAll"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findAll
                        </b>
                        <a href="#findAll"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAll(page: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>, pageSize: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>, query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, sortString: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="61"
                            class="link-to-prism">src/modules/lead-history/repository/query.repository.ts:61</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>page</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>1</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>pageSize</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>10</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>sortString</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>&#x27;&#x27;</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findAllStatus"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            findAllStatus
                        </b>
                        <a href="#findAllStatus"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>findAllStatus()</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="75"
                            class="link-to-prism">src/modules/lead-history/repository/query.repository.ts:75</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findLeadByCustomerId"></a>
                    <span class="name">
                        <b>
                            findLeadByCustomerId
                        </b>
                        <a href="#findLeadByCustomerId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findLeadByCustomerId(customerId: string[], mapping: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="148"
                            class="link-to-prism">src/modules/lead-history/repository/query.repository.ts:148</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>customerId</td>
                                    <td>
                                            <code>string[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>[]</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>mapping</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>false</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="findLeadByEmployeeId"></a>
                    <span class="name">
                        <b>
                            findLeadByEmployeeId
                        </b>
                        <a href="#findLeadByEmployeeId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>findLeadByEmployeeId(employeeId: string[], query: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="361"
                            class="link-to-prism">src/modules/lead-history/repository/query.repository.ts:361</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>employeeId</td>
                                    <td>
                                            <code>string[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>[]</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>query</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>{}</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getHistoryByCustomer"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getHistoryByCustomer
                        </b>
                        <a href="#getHistoryByCustomer"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getHistoryByCustomer(customerId)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="36"
                            class="link-to-prism">src/modules/lead-history/repository/query.repository.ts:36</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>customerId</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="transformSort"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Protected</span>
                            transformSort
                        </b>
                        <a href="#transformSort"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>transformSort(paramSort?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String" target="_blank">String</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="157"
                            class="link-to-prism">src/modules/lead-history/repository/query.repository.ts:157</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>paramSort</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String" target="_blank" >String</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Model } from &#x27;mongoose&#x27;;
import { Inject, Injectable } from &#x27;@nestjs/common&#x27;;
import { ILeadHistoryDocument } from &#x27;../interfaces/document.interface&#x27;;
import _ &#x3D; require(&#x27;lodash&#x27;);
import { CommonConst } from &#x27;../../../modules/shared/constant/common.const&#x27;;
import { LifeCycleStatusEnum } from &#x27;../../../modules/shared/enum/life-cycle-status.enum&#x27;;
import { TransactionTypeEnum } from &#x27;../../../modules/shared/enum/transaction-type.enum&#x27;;
const moment &#x3D; require(&#x27;moment&#x27;);

@Injectable()
export class LeadHistoryQueryRepository {

    constructor(
        @Inject(CommonConst.QUERY_MODEL_TOKEN)
        private readonly readModel: Model&lt;ILeadHistoryDocument&gt;
    ) { }

    async create(readmodel): Promise&lt;ILeadHistoryDocument&gt; {
        return await this.readModel.create(readmodel)
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    async deleteMany(): Promise&lt;ILeadHistoryDocument&gt; {
        return await this.readModel.deleteMany({})
            .then((response) &#x3D;&gt; {
                return response;
            }).catch((error) &#x3D;&gt; {
                return error;
            });
    }

    async getHistoryByCustomer(customerId) {
        return await this.readModel.find({ customerId })
            .exec()
            .then(result &#x3D;&gt; {
                return result;
            });
    }

    async countStatusToDayByEmployee(employeeId: String, lifeCycleStatus: string) {
        const currentDate &#x3D; moment().startOf(&#x27;day&#x27;);
        return await this.readModel.find(
            {
                processBy: employeeId,
                lifeCycleStatus,
                decisionDate: { $gte: currentDate }
            })
            .countDocuments()
            .then(rs &#x3D;&gt; {
                return rs;
            })
            .catch(ex &#x3D;&gt; {
                return ex;
            });
    }

    async findAll(page: number &#x3D; 1, pageSize &#x3D; 10, query: any &#x3D; {}, sortString: string &#x3D; &#x27;&#x27;) {
        let sort: any &#x3D; {
            decisionDate: -1
        };
        if (!_.isEmpty(sortString)) {
            sort &#x3D; this.transformSort(sortString);
        }
        return await this.readModel.find(query)
            .sort(sort)
            .skip(Math.floor(((pageSize * page) - pageSize)))
            .limit(pageSize)
            .exec();
    }

    async findAllStatus() {
        return await this.readModel.aggregate([
            { $group: { _id: &#x27;$lifeCycleStatus&#x27; } }
        ])
            .then(rs &#x3D;&gt; {
                return rs;
            });
    }

    async countStatusForLeads(leadIds, lifeCycleStatus) {
        return await this.readModel.find(
            {
                id: { $in: leadIds },
                lifeCycleStatus
            })
            .countDocuments()
            .then(rs &#x3D;&gt; {
                return rs;
            })
            .catch(ex &#x3D;&gt; {
                return ex;
            });
    }
    async countCancelTicketsDemandByUser(query) {
        return await this.readModel.find(
            {
                $and:
                    [
                        query,
                        { &#x27;lifeCycleStatus&#x27;: &#x27;removed&#x27; },
                        {
                            $or: [
                                { type: &#x27;BUY&#x27; },
                                { type: &#x27;RENT&#x27; }
                            ]
                        }
                    ]
            }
        ).countDocuments()
            .exec()
            .then(demand &#x3D;&gt; {
                return demand;
            });
    }
    async countCancelTicketsConsignmentByUser(query) {
        return await this.readModel.find(
            {
                $and:
                    [
                        query,
                        { &#x27;lifeCycleStatus&#x27;: &#x27;removed&#x27; },
                        {
                            $or: [
                                { type: &#x27;SELL&#x27; },
                                { type: &#x27;LEASE&#x27; }
                            ]
                        }
                    ]
            }
        ).countDocuments()
            .exec()
            .then(consignment &#x3D;&gt; {
                return consignment;
            });
    }
    async count(query: any &#x3D; {}): Promise&lt;number&gt; {
        return await this.readModel.countDocuments(query).exec();
    }

    /**
    * 
    * @param {Array&lt;String&gt;} customerId 
    */
    findLeadByCustomerId(customerId: string[] &#x3D; [], mapping: boolean &#x3D; false) {
        return this.readModel.find({ customerId: { $in: customerId, $exists: true, $nin: [null, &#x27;&#x27;] } }).exec()
            .then((res) &#x3D;&gt; {
                if (mapping) {
                    return _.groupBy(res, &#x27;customerId&#x27;);
                }
                return res;
            });
    }
    protected transformSort(paramSort?: String) {
        let sort: any &#x3D; paramSort;
        if (_.isString(sort)) {
            sort &#x3D; sort.split(&#x27;,&#x27;);
        }
        if (Array.isArray(sort)) {
            let sortObj &#x3D; {};
            sort.forEach(s &#x3D;&gt; {
                if (s.startsWith(&#x27;-&#x27;))
                    sortObj[s.slice(1)] &#x3D; -1;
                else
                    sortObj[s] &#x3D; 1;
            });
            return sortObj;
        }

        return sort;
    }
    async filter(query: any &#x3D; {}, isPaging: boolean &#x3D; false) {
        const limit &#x3D; parseInt(query[&#x27;pageSize&#x27;]) || 10;
        const skip &#x3D; (parseInt(query[&#x27;page&#x27;]) || 1) * limit - limit;
        const { page, pageSize, ...q } &#x3D; query;
        let sort: any &#x3D; {
            createdDate: -1
        };
        if (!_.isEmpty(query.sort)) {
            sort &#x3D; this.transformSort(query.sort);
        }
        console.log(q);
        const aggregate &#x3D; [
            {
                $match: q
            },
            {
                $lookup: {
                    from: &#x27;customers&#x27;,
                    localField: &#x27;customerId&#x27;,    // field in the orders collection
                    foreignField: &#x27;id&#x27;,  // field in the items collection
                    as: &#x27;customers&#x27;
                }
            },
            {
                $lookup: {
                    from: &#x27;employees&#x27;,
                    localField: &#x27;processBy&#x27;,    // field in the orders collection
                    foreignField: &#x27;id&#x27;,  // field in the items collection
                    as: &#x27;employees&#x27;
                }
            },
            {
                $addFields: {
                    customer: {
                        $ifNull: [&#x27;$customer&#x27;, { $arrayElemAt: [&#x27;$customers&#x27;, 0] }]
                    },
                    employeeTakeCare: { $arrayElemAt: [&#x27;$employees&#x27;, 0] }
                }
            },
            {
                $project: {
                    customers: 0,
                    employees: 0
                }
            }
        ];
        if (isPaging) {
            return await this.readModel.aggregate(aggregate)
                .sort(sort)
                .skip(skip)
                .limit(limit)
                .exec();
        } else {
            return await this.readModel.aggregate(aggregate)
                .sort(sort)
                .exec();
        }
    }
    async filterReport(user: any &#x3D; {}, query: any &#x3D; {}, posId: string[] &#x3D; [], isPaging: boolean &#x3D; false) {
        const limit &#x3D; parseInt(query[&#x27;pageSize&#x27;]) || 10;
        const skip &#x3D; (parseInt(query[&#x27;page&#x27;]) || 1) * limit - limit;
        const { page, pageSize, sort &#x3D; &#x27;&#x27;, ...q } &#x3D; query;
        let sortObject: any &#x3D; {
            createdDate: -1
        };
        if (!_.isEmpty(query.sort)) {
            sortObject &#x3D; this.transformSort(query.sort);
        }
        const aggregate &#x3D; [
            {
                $match: {
                    type: { $in: [TransactionTypeEnum.buy, TransactionTypeEnum.lease, TransactionTypeEnum.rent, TransactionTypeEnum.sell] },
                    lifeCycleStatus: { $in: [LifeCycleStatusEnum.COMPLETED] }
                }
            },
            {
                $lookup: {
                    from: &#x27;customers&#x27;,
                    let: { customerId: &#x27;$customerId&#x27; },
                    pipeline: [
                        {
                            $match: {
                                $expr:
                                {
                                    $and:
                                        [
                                            { $eq: [&#x27;$$customerId&#x27;, &#x27;$id&#x27;] },
                                        ]
                                }


                            }
                        },
                        {
                            $addFields: {
                                &#x27;personalInfo.phone&#x27;: {
                                    $cond: { if: { $ne: [&#x27;$modifiedBy&#x27;, user.id] }, then: &#x27;******&#x27;, else: &#x27;$personalInfo.phone&#x27; },
                                },
                                &#x27;phone&#x27;: {
                                    $cond: { if: { $ne: [&#x27;$modifiedBy&#x27;, user.id] }, then: &#x27;******&#x27;, else: &#x27;$phone&#x27; },
                                }
                            }
                        }
                    ],
                    as: &#x27;customers&#x27;
                }
            },
            {
                $lookup: {
                    from: &#x27;employees&#x27;,
                    let: { employeeId: &#x27;$processBy&#x27; },
                    pipeline: [
                        {
                            $match:
                            {
                                $expr:
                                {
                                    $and:
                                        [
                                            { $in: [&#x27;$$employeeId&#x27;, &#x27;$staffIds&#x27;] },
                                            { $eq: [user.id, &#x27;$id&#x27;] },
                                        ]
                                }


                            }
                        },
                    ],
                    as: &#x27;employeeTakeCare&#x27;
                }
            },
            { $unwind: &#x27;$employeeTakeCare&#x27; },
            {
                $lookup: {
                    from: &#x27;employees&#x27;,
                    let: { employeeId: &#x27;$processBy&#x27; },
                    pipeline: [
                        {
                            $match: {
                                $expr:
                                {
                                    $and:
                                        [
                                            { $eq: [&#x27;$$employeeId&#x27;, &#x27;$id&#x27;] },
                                        ]
                                }


                            }
                        },
                    ],
                    as: &#x27;employeeTakeCare&#x27;
                }
            },
            { $unwind: &#x27;$employeeTakeCare&#x27; },
            {
                $addFields: {
                    customer: {
                        $ifNull: [&#x27;$customer&#x27;, { $arrayElemAt: [&#x27;$customers&#x27;, 0] }]
                    },
                    // employeeTakeCare: { $arrayElemAt: [&#x27;$employees&#x27;, 0] }
                }
            },
            {
                $project: {
                    customers: 0,
                    employees: 0,
                    phone: 0
                }
            },
            { $match: q }
        ];
        if (isPaging) {
            return await this.readModel.aggregate(aggregate)
                .sort(sortObject)
                .skip(skip)
                .limit(limit)
                .allowDiskUse(true)
                .exec();
        } else {
            return await this.readModel.aggregate(aggregate)
                .sort(sortObject)
                .allowDiskUse(true)
                .exec();
        }
    }
    findLeadByEmployeeId(employeeId: string[] &#x3D; [], query: any &#x3D; {}) {
        let match: any &#x3D; {
            processBy: { $in: employeeId, $exists: true, $nin: [null, &#x27;&#x27;] }, type: { $in: [TransactionTypeEnum.buy, TransactionTypeEnum.lease, TransactionTypeEnum.rent, TransactionTypeEnum.sell] },
            lifeCycleStatus: { $in: [LifeCycleStatusEnum.COMPLETED] }
        };
        if ((query.dateFrom !&#x3D;&#x3D; undefined &amp;&amp; query.dateFrom !&#x3D; null) || (query.dateTo !&#x3D;&#x3D; undefined &amp;&amp; query.dateTo !&#x3D; null)) {
            match.assignedDate &#x3D; {};
            if ((query.dateFrom !&#x3D;&#x3D; undefined &amp;&amp; query.dateFrom !&#x3D; null)) {
                match.assignedDate.$gte &#x3D; new Date(parseInt(query.dateFrom));
            }
            if ((query.dateTo !&#x3D;&#x3D; undefined &amp;&amp; query.dateTo !&#x3D; null)) {
                match.assignedDate.$lte &#x3D; new Date(parseInt(query.dateTo));
            }
        }
        return this.readModel.find(match).sort({ assignedDate: -1 }).exec();
    }
    countReport(user: any &#x3D; {}, query: any &#x3D; {}, posInPool: any[] &#x3D; [], ) {
        const { page, pageSize, ...q } &#x3D; query;
        const aggregate &#x3D; [
            {
                $match: {
                    type: { $in: [TransactionTypeEnum.buy, TransactionTypeEnum.lease, TransactionTypeEnum.rent, TransactionTypeEnum.sell] },
                    lifeCycleStatus: LifeCycleStatusEnum.COMPLETED
                }
            },
            {
                $lookup: {
                    from: &#x27;customers&#x27;,
                    let: { customerId: &#x27;$customerId&#x27; },
                    pipeline: [
                        {
                            $match: {
                                $expr:
                                {
                                    $and:
                                        [
                                            { $eq: [&#x27;$$customerId&#x27;, &#x27;$id&#x27;] },
                                        ]
                                }
                            }
                        }
                    ],
                    as: &#x27;customers&#x27;
                }
            },
            {
                $lookup: {
                    from: &#x27;employees&#x27;,
                    let: { employeeId: &#x27;$processBy&#x27; },
                    pipeline: [
                        {
                            $match:
                            {
                                $expr:
                                {
                                    $and:
                                        [
                                            { $in: [&#x27;$$employeeId&#x27;, &#x27;$staffIds&#x27;] },
                                            { $eq: [user.id, &#x27;$id&#x27;] },
                                        ]
                                }


                            }
                        },
                    ],
                    as: &#x27;employeeTakeCare&#x27;
                }
            },
            { $unwind: &#x27;$employeeTakeCare&#x27; },
            {
                $lookup: {
                    from: &#x27;employees&#x27;,
                    let: { employeeId: &#x27;$processBy&#x27; },
                    pipeline: [
                        {
                            $match: {
                                $expr:
                                {
                                    $and:
                                        [
                                            { $eq: [&#x27;$$employeeId&#x27;, &#x27;$id&#x27;] },
                                        ]
                                }


                            }
                        },
                    ],
                    as: &#x27;employeeTakeCare&#x27;
                }
            },
            { $unwind: &#x27;$employeeTakeCare&#x27; },
            {
                $addFields: {
                    customer: {
                        $ifNull: [&#x27;$customer&#x27;, { $arrayElemAt: [&#x27;$customers&#x27;, 0] }]
                    },
                    // employeeTakeCare: { $arrayElemAt: [&#x27;$employees&#x27;, 0] }
                }
            },
            {
                $project: {
                    customers: 0,
                    employees: 0,
                    phone: 0
                }
            },
            { $match: q }
        ];
        return this.readModel.aggregate(aggregate)
            .allowDiskUse(true)
            .exec();
    }
}
</code></pre>
    </div>

</div>







                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'LeadHistoryQueryRepository.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
