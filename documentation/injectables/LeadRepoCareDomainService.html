<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-lead documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-lead documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">







<ol class="breadcrumb">
  <li>Injectables</li>
  <li>LeadRepoCareDomainService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/leadRepoCare.domain/service.ts</code>
        </p>




            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Readonly</span>
                                <a href="#configPrefix">configPrefix</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Readonly</span>
                                <a href="#context">context</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Readonly</span>
                                <a href="#leadRepoCarePrefix">leadRepoCarePrefix</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Readonly</span>
                                <a href="#successResponse">successResponse</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#activeLeadRepoCareConfig">activeLeadRepoCareConfig</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createLeadRepoCare">createLeadRepoCare</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#executeCommand">executeCommand</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#getCode">getCode</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#updateConfigData">updateConfigData</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateLeadRepoCareConfig">updateLeadRepoCareConfig</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateLeadRepoCareConfigs">updateLeadRepoCareConfigs</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#updateLeadRepoCareMain">updateLeadRepoCareMain</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateExploitTime">validateExploitTime</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateOrgChart">validateOrgChart</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateProject">validateProject</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section>
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(commandBus: CommandBus, propertyClient: <a href="../injectables/PropertyClient.html">PropertyClient</a>, loggerService: <a href="../injectables/MsxLoggerService.html">MsxLoggerService</a>, queryRepo: <a href="../injectables/LeadRepoCareQueryRepository.html">LeadRepoCareQueryRepository</a>, orgchartClient: <a href="../injectables/OrgchartClient.html">OrgchartClient</a>, codeSrv: <a href="../injectables/CodeGenerateService.html">CodeGenerateService</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="38" class="link-to-prism">src/modules/leadRepoCare.domain/service.ts:38</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>commandBus</td>
                                                  
                                                        <td>
                                                                    <code>CommandBus</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>propertyClient</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/PropertyClient.html" target="_self" >PropertyClient</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>loggerService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/MsxLoggerService.html" target="_self" >MsxLoggerService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>queryRepo</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/LeadRepoCareQueryRepository.html" target="_self" >LeadRepoCareQueryRepository</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>orgchartClient</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/OrgchartClient.html" target="_self" >OrgchartClient</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>codeSrv</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/CodeGenerateService.html" target="_self" >CodeGenerateService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section>
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="activeLeadRepoCareConfig"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            activeLeadRepoCareConfig
                        </b>
                        <a href="#activeLeadRepoCareConfig"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>activeLeadRepoCareConfig(undefined: <a href="../classes/ActiveLeadRepoCareConfigDto.html">ActiveLeadRepoCareConfigDto</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, timezoneClient: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="266"
                            class="link-to-prism">src/modules/leadRepoCare.domain/service.ts:266</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                                <code><a href="../classes/ActiveLeadRepoCareConfigDto.html" target="_self" >ActiveLeadRepoCareConfigDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>userId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>timezoneClient</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createLeadRepoCare"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            createLeadRepoCare
                        </b>
                        <a href="#createLeadRepoCare"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createLeadRepoCare(record: <a href="../classes/CreateLeadRepoCareDto.html">CreateLeadRepoCareDto</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, timezoneClient: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="54"
                            class="link-to-prism">src/modules/leadRepoCare.domain/service.ts:54</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>record</td>
                                    <td>
                                                <code><a href="../classes/CreateLeadRepoCareDto.html" target="_self" >CreateLeadRepoCareDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>userId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>timezoneClient</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="executeCommand"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            executeCommand
                        </b>
                        <a href="#executeCommand"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>executeCommand(action: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, commandId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, item: <a href="../classes/LeadRepoCare.html">LeadRepoCare</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="368"
                            class="link-to-prism">src/modules/leadRepoCare.domain/service.ts:368</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>action</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>commandId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>item</td>
                                    <td>
                                                <code><a href="../classes/LeadRepoCare.html" target="_self" >LeadRepoCare</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getCode"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            getCode
                        </b>
                        <a href="#getCode"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getCode(config)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="48"
                            class="link-to-prism">src/modules/leadRepoCare.domain/service.ts:48</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>config</td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>false</code>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateConfigData"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            updateConfigData
                        </b>
                        <a href="#updateConfigData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateConfigData(src: LeadRepoCareConfig[], code: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, record: <a href="../classes/LeadRepoCareConfig.html">LeadRepoCareConfig</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="396"
                            class="link-to-prism">src/modules/leadRepoCare.domain/service.ts:396</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>src</td>
                                    <td>
                                            <code>LeadRepoCareConfig[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>code</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>record</td>
                                    <td>
                                                <code><a href="../classes/LeadRepoCareConfig.html" target="_self" >LeadRepoCareConfig</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateLeadRepoCareConfig"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateLeadRepoCareConfig
                        </b>
                        <a href="#updateLeadRepoCareConfig"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateLeadRepoCareConfig(undefined: <a href="../classes/UpdateLeadRepoCareConfigDto.html">UpdateLeadRepoCareConfigDto</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, timezoneClient: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="154"
                            class="link-to-prism">src/modules/leadRepoCare.domain/service.ts:154</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                                <code><a href="../classes/UpdateLeadRepoCareConfigDto.html" target="_self" >UpdateLeadRepoCareConfigDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>userId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>timezoneClient</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateLeadRepoCareConfigs"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateLeadRepoCareConfigs
                        </b>
                        <a href="#updateLeadRepoCareConfigs"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateLeadRepoCareConfigs(id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, configs: LeadRepoCareConfig[], actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, timezoneClient: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="207"
                            class="link-to-prism">src/modules/leadRepoCare.domain/service.ts:207</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>configs</td>
                                    <td>
                                            <code>LeadRepoCareConfig[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>userId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>timezoneClient</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateLeadRepoCareMain"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            updateLeadRepoCareMain
                        </b>
                        <a href="#updateLeadRepoCareMain"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateLeadRepoCareMain(undefined: <a href="../classes/UpdateLeadRepoCareMainDto.html">UpdateLeadRepoCareMainDto</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, userId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, timezoneClient: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="110"
                            class="link-to-prism">src/modules/leadRepoCare.domain/service.ts:110</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                                <code><a href="../classes/UpdateLeadRepoCareMainDto.html" target="_self" >UpdateLeadRepoCareMainDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>userId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>timezoneClient</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateExploitTime"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            validateExploitTime
                        </b>
                        <a href="#validateExploitTime"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateExploitTime(exploitTime: <a href="../classes/DateRange.html">DateRange</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="349"
                            class="link-to-prism">src/modules/leadRepoCare.domain/service.ts:349</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>exploitTime</td>
                                    <td>
                                                <code><a href="../classes/DateRange.html" target="_self" >DateRange</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateOrgChart"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            validateOrgChart
                        </b>
                        <a href="#validateOrgChart"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateOrgChart(orgChartIds: string[])</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="314"
                            class="link-to-prism">src/modules/leadRepoCare.domain/service.ts:314</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>orgChartIds</td>
                                    <td>
                                            <code>string[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateProject"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            validateProject
                        </b>
                        <a href="#validateProject"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateProject(projectId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="336"
                            class="link-to-prism">src/modules/leadRepoCare.domain/service.ts:336</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>projectId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>
            <section>
    
        <h3 id="inputs">
            Properties
        </h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="configPrefix"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Private</span>
                                <span class="modifier">Readonly</span>
                            configPrefix</b>
                            <a href="#configPrefix"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;SRFC&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="38" class="link-to-prism">src/modules/leadRepoCare.domain/service.ts:38</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="context"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Private</span>
                                <span class="modifier">Readonly</span>
                            context</b>
                            <a href="#context"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>LeadRepoCareDomainService.name</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="35" class="link-to-prism">src/modules/leadRepoCare.domain/service.ts:35</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="leadRepoCarePrefix"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Private</span>
                                <span class="modifier">Readonly</span>
                            leadRepoCarePrefix</b>
                            <a href="#leadRepoCarePrefix"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>&#x27;SRF&#x27;</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="37" class="link-to-prism">src/modules/leadRepoCare.domain/service.ts:37</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="successResponse"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Private</span>
                                <span class="modifier">Readonly</span>
                            successResponse</b>
                            <a href="#successResponse"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{ success: true }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="36" class="link-to-prism">src/modules/leadRepoCare.domain/service.ts:36</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { BadRequestException, Injectable } from &#x27;@nestjs/common&#x27;;
import { CommandBus } from &#x27;@nestjs/cqrs&#x27;;
import { PropertyClient } from &#x27;../mgs-sender/property.client&#x27;;
import {
    LeadRepoCare,
    DateRange,
    LeadRepoCareConfig,
} from &#x27;../shared/models/leadRepoCare/model&#x27;;
import * as Bluebird from &#x27;bluebird&#x27;;
import { CmdPatternConst } from &#x27;../shared/constant/cmd-pattern.const&#x27;;
import { ErrorConst } from &#x27;../shared/constant/error.const&#x27;;
import uuid &#x3D; require(&#x27;uuid&#x27;);
import { Action } from &#x27;../shared/enum/action.enum&#x27;;
import { CreateLeadRepoCareCommand } from &#x27;./commands/impl/create.cmd&#x27;;
import { MsxLoggerService } from &#x27;../logger/logger.service&#x27;;
import { LeadRepoCareQueryRepository } from &#x27;../leadRepoCare.queryside/repositories/query.repository&#x27;;
import { UpdateLeadRepoCareCommand } from &#x27;./commands/impl/update.cmd&#x27;;
import { OrgchartClient } from &#x27;../mgs-sender/orgchart.client&#x27;;
import { CodeGenerateService } from &#x27;../code-generate/service&#x27;;
import { cloneDeep, isEmpty } from &#x27;lodash&#x27;;
import {
    ActiveLeadRepoCareConfigDto,
    CreateLeadRepoCareDto,
    UpdateLeadRepoCareConfigDto,
    UpdateLeadRepoCareMainDto,
} from &#x27;./dto/domain.dto&#x27;;
import { LeadRepoCareCommandModel } from &#x27;../shared/models/leadRepoCare/command.model&#x27;;
import { LeadRepoCareDomainModel } from &#x27;./models/domain.model&#x27;;
import { updateArrData } from &#x27;../shared/utils/updateArrData&#x27;;
import { UpdateConfigLeadRepoCareCommand } from &#x27;./commands/impl/updateConfig&#x27;;
import { ActiveConfigLeadRepoCareCommand } from &#x27;./commands/impl/activeConfig&#x27;;

@Injectable()
export class LeadRepoCareDomainService {
    private readonly context &#x3D; LeadRepoCareDomainService.name;
    private readonly successResponse &#x3D; { success: true };
    private readonly leadRepoCarePrefix &#x3D; &#x27;SRF&#x27;;
    private readonly configPrefix &#x3D; &#x27;SRFC&#x27;;
    constructor(
        private readonly commandBus: CommandBus,
        private readonly propertyClient: PropertyClient,
        private readonly loggerService: MsxLoggerService,
        private readonly queryRepo: LeadRepoCareQueryRepository,
        private readonly orgchartClient: OrgchartClient,
        protected readonly codeSrv: CodeGenerateService
    ) {}

    private async getCode(config &#x3D; false) {
        const prefix &#x3D; config ? this.configPrefix : this.leadRepoCarePrefix;
        const code &#x3D; await this.codeSrv.generateCode(prefix);
        return code;
    }

    async createLeadRepoCare(
        record: CreateLeadRepoCareDto,
        actionName: string,
        userId: string,
        timezoneClient: string
    ) {
        this.loggerService.log(this.context, &#x27;createLeadRepoCare&#x27;);
        const { configs } &#x3D; record;

        if (!configs?.length) {
            throw new BadRequestException({
                errors: ErrorConst.CommonError(
                    ErrorConst.INVALID_INPUT,
                    &#x27;configs&#x27;
                ),
            });
        }

        // const hotOrgCharts &#x3D; await this.validateOrgChart(configHot.orgChartIds);

        // Object.assign(configHot, { orgCharts: hotOrgCharts });

        const validConfigs &#x3D; [];
        await Bluebird.mapSeries(configs, async (config) &#x3D;&gt; {
            await this.validateExploitTime(config.exploitTime);
            const configOrgCharts &#x3D; await this.validateOrgChart(config.orgChartIds);
            await this.validateProject(config.projectId);
            const configCode &#x3D; await this.getCode(true);
            Object.assign(config, {
              code: configCode,
              orgCharts: configOrgCharts,
            });
            validConfigs.push(config);
        });

        const commandId &#x3D; uuid.v4();

        const leadRepoCareCode &#x3D; await this.getCode();

        Object.assign(record, {
            code: leadRepoCareCode,
            configs: validConfigs,
            modifiedBy: userId,
            timezoneClient,
        });

        await this.executeCommand(
            Action.CREATE,
            actionName,
            commandId,
            record as LeadRepoCareCommandModel
        );

        return this.successResponse;
    }

    async updateLeadRepoCareMain(
        { id, name, type }: UpdateLeadRepoCareMainDto,
        actionName: string,
        userId: string,
        timezoneClient: string
    ) {
        this.loggerService.log(this.context, &#x27;updateLeadRepoCareMain&#x27;);
        const leadRepoCare &#x3D; await this.queryRepo.findById(id, true);
        if (!leadRepoCare) {
            throw new BadRequestException({
                errors: ErrorConst.CommonError(
                    ErrorConst.NOT_FOUND,
                    &#x27;leadRepoCare&#x27;
                ),
            });
        }

        // const { configHot: defaultConfig } &#x3D; leadRepoCare;

        // const hotOrgCharts &#x3D; await this.validateOrgChart(configHot.orgChartIds);
        // Object.assign(configHot, { orgCharts: hotOrgCharts });
        // Object.assign(defaultConfig, configHot);

        const commandId &#x3D; uuid.v4();

        Object.assign(leadRepoCare, {
            // configHot: defaultConfig,
            name,
            type,
            updatedDate: Date.now(),
            modifiedBy: userId,
            timezoneClient,
        });

        await this.executeCommand(
            Action.UPDATE_MAIN,
            actionName,
            commandId,
            leadRepoCare as LeadRepoCareCommandModel
        );

        return this.successResponse;
    }

    async updateLeadRepoCareConfig(
        { id, config }: UpdateLeadRepoCareConfigDto,
        actionName: string,
        userId: string,
        timezoneClient: string
    ) {
        this.loggerService.log(this.context, &#x27;updateLeadRepoCareConfig&#x27;);
        const leadRepoCare &#x3D; await this.queryRepo.findById(id, true);
        if (!leadRepoCare) {
            throw new BadRequestException({
                errors: ErrorConst.CommonError(
                    ErrorConst.NOT_FOUND,
                    &#x27;leadRepoCare&#x27;
                ),
            });
        }

        let { configs } &#x3D; leadRepoCare;
        await this.validateExploitTime(config.exploitTime);
        const configOrgCharts &#x3D; await this.validateOrgChart(config.orgChartIds);
        await this.validateProject(config.projectId);
        Object.assign(config, {orgCharts: configOrgCharts});

        const existed &#x3D; configs.find(i &#x3D;&gt; i.code &#x3D;&#x3D;&#x3D; config.code);
        if (!existed) {
          const newCode &#x3D; await this.getCode(true);
          Object.assign(config, { code: newCode });
          configs &#x3D; this.updateConfigData(configs, null, config);
        } else {
            Object.assign(existed, config);
            configs &#x3D; this.updateConfigData(configs, existed.code, existed);
        }
        

        Object.assign(leadRepoCare, {
            configs,
            updatedDate: Date.now(),
            modifiedBy: userId || leadRepoCare.modifiedBy,
            timezoneClient: timezoneClient || leadRepoCare.timezoneClient,
        });

        const commandId &#x3D; uuid.v4();

        await this.executeCommand(
            Action.UPDATE_CONFIG,
            actionName,
            commandId,
            leadRepoCare as LeadRepoCareCommandModel
        );

        return this.successResponse;
    }

    async updateLeadRepoCareConfigs(
      id: string,
      configs: LeadRepoCareConfig[],
      actionName: string,
      userId: string,
      timezoneClient: string
    ) {
      this.loggerService.log(this.context, &#x27;updateLeadRepoCareConfigs&#x27;);
      const leadRepoCare &#x3D; await this.queryRepo.findById(id, true);
      if (!leadRepoCare) {
          throw new BadRequestException({
              errors: ErrorConst.CommonError(
                  ErrorConst.NOT_FOUND,
                  &#x27;leadRepoCare&#x27;
              ),
          });
      }

      let { configs: defaultConfigs } &#x3D; leadRepoCare;

      await Bluebird.mapSeries(configs, async config &#x3D;&gt; {
        await this.validateExploitTime(config.exploitTime);
        const configOrgCharts &#x3D; await this.validateOrgChart(config.orgChartIds);
        await this.validateProject(config.projectId);

        Object.assign(config, {orgCharts: configOrgCharts});

        const existed &#x3D; defaultConfigs.find(i &#x3D;&gt; i.code &#x3D;&#x3D;&#x3D; config.code);
        if (!existed) {
          defaultConfigs &#x3D; this.updateConfigData(defaultConfigs, null, config);
        } else {
          Object.assign(existed, config);
          defaultConfigs &#x3D; this.updateConfigData(
            defaultConfigs,
            existed.code,
            existed
          );
        }
      });
    
      Object.assign(leadRepoCare, {
          configs,
          updatedDate: Date.now(),
          modifiedBy: userId || leadRepoCare.modifiedBy,
          timezoneClient: timezoneClient || leadRepoCare.timezoneClient,
      });

      const commandId &#x3D; uuid.v4();

      await this.executeCommand(
          Action.UPDATE_CONFIG,
          actionName,
          commandId,
          leadRepoCare as LeadRepoCareCommandModel
      );

      return this.successResponse;
    }

    async activeLeadRepoCareConfig(
        { id, configCode }: ActiveLeadRepoCareConfigDto,
        actionName: string,
        userId: string,
        timezoneClient: string
    ) {
        this.loggerService.log(this.context, &#x27;activeLeadRepoCareConfig&#x27;);
        const leadRepoCare &#x3D; await this.queryRepo.findById(id, true);
        if (!leadRepoCare) {
            throw new BadRequestException({
                errors: ErrorConst.CommonError(
                    ErrorConst.NOT_FOUND,
                    &#x27;leadRepoCare&#x27;
                ),
            });
        }

        const { configs } &#x3D; leadRepoCare;

        const targetIdx &#x3D; configs.findIndex((item) &#x3D;&gt; item.code &#x3D;&#x3D;&#x3D; configCode);
        if (targetIdx &#x3D;&#x3D;&#x3D; -1) {
            throw new BadRequestException({
                errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, &#x27;config&#x27;),
            });
        }

        const target &#x3D; cloneDeep(configs[targetIdx]);
        Object.assign(target, { active: !target.active });

        const updatedConfigs &#x3D; updateArrData(target, configs, targetIdx);
        Object.assign(leadRepoCare, {
            configs: updatedConfigs,
            modifiedBy: userId,
            updatedDate: Date.now(),
            timezoneClient,
        });

        const commandId &#x3D; uuid.v4();

        await this.executeCommand(
            Action.ACTIVE_CONFIG,
            actionName,
            commandId,
            leadRepoCare as LeadRepoCareCommandModel
        );
        return this.successResponse;
    }

    private async validateOrgChart(orgChartIds: string[]) {
        const orgCharts: any[] &#x3D; await this.orgchartClient.sendDataPromise(
            { posIds: orgChartIds },
            CmdPatternConst.ORGCHART.GET_POS_BY_QUERY
        );

        if (orgCharts?.length !&#x3D;&#x3D; orgChartIds.length) {
            throw new BadRequestException({
                errors: ErrorConst.CommonError(
                    ErrorConst.NOT_FOUND,
                    &#x27;orgchart&#x27;
                ),
            });
        }

        return orgCharts.map((item) &#x3D;&gt; ({
          id: item.id,
          name: item.name,
          staffIds: item.staffIds,
        }));
    }

    private async validateProject(projectId: string) {
        const project &#x3D; await this.propertyClient.sendDataPromise(
            { id: projectId },
            CmdPatternConst.PROJECT.GET_PROJECT_BY_ID
        );

        if (!project) {
            throw new BadRequestException({
                errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, &#x27;project&#x27;),
            });
        }
    }

    private async validateExploitTime(exploitTime: DateRange) {
        if (!exploitTime?.from || !exploitTime?.to) {
            throw new BadRequestException({
                errors: ErrorConst.CommonError(
                    ErrorConst.INVALID_FIELD,
                    &#x27;exploitTime&#x27;
                ),
            });
        }
        if (new Date(exploitTime.from) &gt; new Date(exploitTime.to)) {
            throw new BadRequestException({
                errors: ErrorConst.CommonError(
                    ErrorConst.INVALID_FIELD,
                    &#x27;exploitTime&#x27;
                ),
            });
        }
    }

    private async executeCommand(
        action: string,
        actionName: string,
        commandId: string,
        item: LeadRepoCare
    ) {
        switch (action) {
            case Action.CREATE:
                return this.commandBus.execute(
                    new CreateLeadRepoCareCommand(actionName, commandId, item)
                );
            case Action.UPDATE_MAIN:
                return this.commandBus.execute(
                    new UpdateLeadRepoCareCommand(actionName, commandId, item)
                );
            case Action.UPDATE_CONFIG:
                return this.commandBus.execute(
                    new UpdateConfigLeadRepoCareCommand(actionName, commandId, item)
                );
            case Action.ACTIVE_CONFIG:
                return this.commandBus.execute(
                    new ActiveConfigLeadRepoCareCommand(actionName, commandId, item)
                );
            default:
                break;
        }
    }

    private updateConfigData(src: LeadRepoCareConfig[], code: string, record: LeadRepoCareConfig) {
      if (!code) {
        src &#x3D; src.concat(record);
      } else {
        const idx &#x3D; src.findIndex(item &#x3D;&gt; item.code &#x3D;&#x3D;&#x3D; code);
        src &#x3D; [...src.slice(0, idx), record, ...src.slice(idx + 1)];
      }

      return src;
    }
}
</code></pre>
    </div>

</div>







                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'LeadRepoCareDomainService.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
