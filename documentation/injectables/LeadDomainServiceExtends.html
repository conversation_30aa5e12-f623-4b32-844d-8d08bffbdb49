<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-lead documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-lead documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">







<ol class="breadcrumb">
  <li>Injectables</li>
  <li>LeadDomainServiceExtends</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/lead.domain/service.extend.ts</code>
        </p>




            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Readonly</span>
                                <a href="#context">context</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Readonly</span>
                                <a href="#response">response</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#createLeadCommon">createLeadCommon</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#deliveredToAll">deliveredToAll</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#deliverLeads">deliverLeads</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#deliverNormalLead">deliverNormalLead</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#executeCommand">executeCommand</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#getExpireTime">getExpireTime</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#getOrgChart">getOrgChart</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#importLeadFromExcel">importLeadFromExcel</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#initQueueData">initQueueData</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#leadDelivery">leadDelivery</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#parseQueueData">parseQueueData</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#preProcessLead">preProcessLead</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#reAssignHotLead">reAssignHotLead</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#renewNormalLead">renewNormalLead</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#updateConfigData">updateConfigData</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#updateExploitHistory">updateExploitHistory</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateEmployee">validateEmployee</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#validateLead">validateLead</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                    <span class="modifier">Async</span>
                                <a href="#validateRepo">validateRepo</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section>
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(commandBus: CommandBus, loggerService: <a href="../injectables/MsxLoggerService.html">MsxLoggerService</a>, leadRepoQueryRepo: <a href="../injectables/LeadRepoQueryRepository.html">LeadRepoQueryRepository</a>, employeeRepo: <a href="../injectables/EmployeeQueryRepository.html">EmployeeQueryRepository</a>, leadQueryRepo: <a href="../injectables/QueryRepository.html">QueryRepository</a>, genCodeSrv: <a href="../injectables/CodeGenerateService.html">CodeGenerateService</a>, leadRepoDomainSrv: <a href="../injectables/LeadRepoDomainService.html">LeadRepoDomainService</a>, historyImportSrc: <a href="../injectables/HistoryImportService.html">HistoryImportService</a>, sourceRepository: <a href="../injectables/LeadSourceQueryRepository.html">LeadSourceQueryRepository</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="49" class="link-to-prism">src/modules/lead.domain/service.extend.ts:49</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>commandBus</td>
                                                  
                                                        <td>
                                                                    <code>CommandBus</code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>loggerService</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/MsxLoggerService.html" target="_self" >MsxLoggerService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>leadRepoQueryRepo</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/LeadRepoQueryRepository.html" target="_self" >LeadRepoQueryRepository</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>employeeRepo</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/EmployeeQueryRepository.html" target="_self" >EmployeeQueryRepository</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>leadQueryRepo</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/QueryRepository.html" target="_self" >QueryRepository</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>genCodeSrv</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/CodeGenerateService.html" target="_self" >CodeGenerateService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>leadRepoDomainSrv</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/LeadRepoDomainService.html" target="_self" >LeadRepoDomainService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>historyImportSrc</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/HistoryImportService.html" target="_self" >HistoryImportService</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>sourceRepository</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/LeadSourceQueryRepository.html" target="_self" >LeadSourceQueryRepository</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section>
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createLeadCommon"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            createLeadCommon
                        </b>
                        <a href="#createLeadCommon"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createLeadCommon(options: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, timezoneClient?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user?: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="955"
                            class="link-to-prism">src/modules/lead.domain/service.extend.ts:955</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>options</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>timezoneClient</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deliveredToAll"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            deliveredToAll
                        </b>
                        <a href="#deliveredToAll"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deliveredToAll(posQueue: <a href="../classes/Queue.html">Queue<PosQueueData></a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="721"
                            class="link-to-prism">src/modules/lead.domain/service.extend.ts:721</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>posQueue</td>
                                    <td>
                                                <code><a href="../classes/Queue.html" target="_self" >Queue&lt;PosQueueData&gt;</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deliverLeads"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            deliverLeads
                        </b>
                        <a href="#deliverLeads"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deliverLeads(dto: DeliverLeadDto[], user)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="532"
                            class="link-to-prism">src/modules/lead.domain/service.extend.ts:532</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>dto</td>
                                    <td>
                                            <code>DeliverLeadDto[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="deliverNormalLead"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            deliverNormalLead
                        </b>
                        <a href="#deliverNormalLead"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>deliverNormalLead(recordNumber)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="229"
                            class="link-to-prism">src/modules/lead.domain/service.extend.ts:229</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>recordNumber</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="executeCommand"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            executeCommand
                        </b>
                        <a href="#executeCommand"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>executeCommand(action: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, commandId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, item: CommandModel | CommandModel[], additionalData?: T)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="807"
                            class="link-to-prism">src/modules/lead.domain/service.extend.ts:807</a></div>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">
                    <b>Type parameters :</b>
                    <ul class="type-parameters">
                        <li>T</li>
                    </ul>
                </td>
            </tr>

            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>action</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>commandId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>item</td>
                                    <td>
                                            <code>CommandModel | CommandModel[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>additionalData</td>
                                    <td>
                                            <code>T</code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getExpireTime"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            getExpireTime
                        </b>
                        <a href="#getExpireTime"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getExpireTime(start: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date" target="_blank">Date</a>, duration: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="929"
                            class="link-to-prism">src/modules/lead.domain/service.extend.ts:929</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>start</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date" target="_blank" >Date</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>duration</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getOrgChart"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            getOrgChart
                        </b>
                        <a href="#getOrgChart"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getOrgChart(posQueue: <a href="../classes/Queue.html">Queue<PosQueueData></a>, initData: Map<string | any[]>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="933"
                            class="link-to-prism">src/modules/lead.domain/service.extend.ts:933</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>posQueue</td>
                                    <td>
                                                <code><a href="../classes/Queue.html" target="_self" >Queue&lt;PosQueueData&gt;</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>initData</td>
                                    <td>
                                            <code>Map&lt;string | any[]&gt;</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="importLeadFromExcel"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            importLeadFromExcel
                        </b>
                        <a href="#importLeadFromExcel"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>importLeadFromExcel(files: any[], options: <a href="../classes/ImportLeadAsExcelDto.html">ImportLeadAsExcelDto</a>, actionName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, timezoneClient?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user?: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="63"
                            class="link-to-prism">src/modules/lead.domain/service.extend.ts:63</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>files</td>
                                    <td>
                                            <code>any[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>options</td>
                                    <td>
                                                <code><a href="../classes/ImportLeadAsExcelDto.html" target="_self" >ImportLeadAsExcelDto</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>actionName</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>timezoneClient</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                                <tr>
                                    <td>user</td>
                                    <td>
                                                <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="initQueueData"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            initQueueData
                        </b>
                        <a href="#initQueueData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>initQueueData(config: <a href="../classes/LeadRepoConfig.html">LeadRepoConfig</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="727"
                            class="link-to-prism">src/modules/lead.domain/service.extend.ts:727</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>config</td>
                                    <td>
                                                <code><a href="../classes/LeadRepoConfig.html" target="_self" >LeadRepoConfig</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>Promise&lt;Map&lt;string, any[]&gt;&gt;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="leadDelivery"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            leadDelivery
                        </b>
                        <a href="#leadDelivery"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>leadDelivery(data: ILeadDocument[], config: <a href="../classes/LeadRepoConfig.html">LeadRepoConfig</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="627"
                            class="link-to-prism">src/modules/lead.domain/service.extend.ts:627</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>data</td>
                                    <td>
                                            <code>ILeadDocument[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>config</td>
                                    <td>
                                                <code><a href="../classes/LeadRepoConfig.html" target="_self" >LeadRepoConfig</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="parseQueueData"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            parseQueueData
                        </b>
                        <a href="#parseQueueData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>parseQueueData(posQueue: <a href="../classes/Queue.html">Queue<PosQueueData></a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="714"
                            class="link-to-prism">src/modules/lead.domain/service.extend.ts:714</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>posQueue</td>
                                    <td>
                                                <code><a href="../classes/Queue.html" target="_self" >Queue&lt;PosQueueData&gt;</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="preProcessLead"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            preProcessLead
                        </b>
                        <a href="#preProcessLead"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>preProcessLead(lead, repository: <a href="../classes/LeadRepo.html">LeadRepo</a>, config: <a href="../classes/LeadRepoConfig.html">LeadRepoConfig</a>, user, timezoneClient, source)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="884"
                            class="link-to-prism">src/modules/lead.domain/service.extend.ts:884</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>lead</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>repository</td>
                                    <td>
                                                <code><a href="../classes/LeadRepo.html" target="_self" >LeadRepo</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>config</td>
                                    <td>
                                                <code><a href="../classes/LeadRepoConfig.html" target="_self" >LeadRepoConfig</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>timezoneClient</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>source</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="reAssignHotLead"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            reAssignHotLead
                        </b>
                        <a href="#reAssignHotLead"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>reAssignHotLead(recordNumber)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="413"
                            class="link-to-prism">src/modules/lead.domain/service.extend.ts:413</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>recordNumber</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="renewNormalLead"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            renewNormalLead
                        </b>
                        <a href="#renewNormalLead"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>renewNormalLead(recordNumber)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="355"
                            class="link-to-prism">src/modules/lead.domain/service.extend.ts:355</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>recordNumber</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateConfigData"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            updateConfigData
                        </b>
                        <a href="#updateConfigData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateConfigData(src: LeadRepoConfig[], code: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, config: <a href="../classes/LeadRepoConfig.html">LeadRepoConfig</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="919"
                            class="link-to-prism">src/modules/lead.domain/service.extend.ts:919</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>src</td>
                                    <td>
                                            <code>LeadRepoConfig[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>code</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>config</td>
                                    <td>
                                                <code><a href="../classes/LeadRepoConfig.html" target="_self" >LeadRepoConfig</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="updateExploitHistory"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            updateExploitHistory
                        </b>
                        <a href="#updateExploitHistory"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>updateExploitHistory(history: IExploitHistory[], status: <a href="../undefineds/ExploitEnum.html">ExploitEnum</a>, takeCareId?: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, takeCareInfo?: <a href="../interfaces/ITakeCare.html">ITakeCare</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="838"
                            class="link-to-prism">src/modules/lead.domain/service.extend.ts:838</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                    <td>Default value</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>history</td>
                                    <td>
                                            <code>IExploitHistory[]</code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                        <code>[]</code>
                                    </td>

                                </tr>
                                <tr>
                                    <td>status</td>
                                    <td>
                                                <code><a href="../miscellaneous/enumerations.html#ExploitEnum" target="_self" >ExploitEnum</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>takeCareId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                                <tr>
                                    <td>takeCareInfo</td>
                                    <td>
                                                <code><a href="../interfaces/ITakeCare.html" target="_self" >ITakeCare</a></code>
                                    </td>

                                    <td>
                                        Yes
                                    </td>

                                    <td>
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{ history: {}; newHistory: any; }</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateEmployee"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            validateEmployee
                        </b>
                        <a href="#validateEmployee"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateEmployee(employee, orgChartGroup: Map<string | string[]>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="865"
                            class="link-to-prism">src/modules/lead.domain/service.extend.ts:865</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>employee</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>orgChartGroup</td>
                                    <td>
                                            <code>Map&lt;string | string[]&gt;</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateLead"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            validateLead
                        </b>
                        <a href="#validateLead"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateLead(lead)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="855"
                            class="link-to-prism">src/modules/lead.domain/service.extend.ts:855</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>lead</td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>&quot;Số điện thoại sai&quot; | &quot;Email sai&quot; | &quot;&quot;</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="validateRepo"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Private</span>
                            <span class="modifier">Async</span>
                            validateRepo
                        </b>
                        <a href="#validateRepo"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>validateRepo(repoId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, repoConfigCode: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="780"
                            class="link-to-prism">src/modules/lead.domain/service.extend.ts:780</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>repoId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>repoConfigCode</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>
            <section>
    
        <h3 id="inputs">
            Properties
        </h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="context"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Private</span>
                                <span class="modifier">Readonly</span>
                            context</b>
                            <a href="#context"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>LeadDomainServiceExtends.name</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="48" class="link-to-prism">src/modules/lead.domain/service.extend.ts:48</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="response"></a>
                        <span class="name">
                            <b>
                                <span class="modifier">Private</span>
                                <span class="modifier">Readonly</span>
                            response</b>
                            <a href="#response"><span class="icon ion-ios-link"></span></a>
                        </span>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <i>Default value : </i><code>{ success: true }</code>
                        </td>
                    </tr>
                        <tr>
                            <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="49" class="link-to-prism">src/modules/lead.domain/service.extend.ts:49</a></div>
                            </td>
                        </tr>


            </tbody>
        </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { BadRequestException, Injectable } from &#x27;@nestjs/common&#x27;;
import { CommandBus } from &#x27;@nestjs/cqrs&#x27;;
import * as Bluebird from &#x27;bluebird&#x27;;
import { cloneDeep, concat, flatMapDeep, isEmpty, maxBy, uniq } from &#x27;lodash&#x27;;
import { isNullOrUndefined } from &#x27;util&#x27;;
import uuid &#x3D; require(&#x27;uuid&#x27;);
import { CodeGenerateService } from &#x27;../code-generate/service&#x27;;
import { EmployeeQueryRepository } from &#x27;../employee/repository/query.repository&#x27;;
import { HistoryImportService } from &#x27;../history-import/service&#x27;;
import { ILeadDocument } from &#x27;../lead.queryside/interfaces/document.interface&#x27;;
import { QueryRepository } from &#x27;../lead.queryside/repository/query.repository&#x27;;
import { LeadRepoDomainService } from &#x27;../leadRepo.domain/service&#x27;;
import { LeadRepoQueryRepository } from &#x27;../leadRepo.queryside/repositories/query.repository&#x27;;
import { MsxLoggerService } from &#x27;../logger/logger.service&#x27;;
import { Queue } from &#x27;../shared/classes/class-queue&#x27;;
import { CommonConst } from &#x27;../shared/constant/common.const&#x27;;
import { ErrorConst } from &#x27;../shared/constant/error.const&#x27;;
import { Action } from &#x27;../shared/enum/action.enum&#x27;;
import { ExploitEnum } from &#x27;../shared/enum/exploit.enum&#x27;;
import { LifeCycleStatusEnum } from &#x27;../shared/enum/life-cycle-status.enum&#x27;;
import { SourceEnum } from &#x27;../shared/enum/source.enum&#x27;;
import { StatusEnum } from &#x27;../shared/enum/status.enum&#x27;;
import { CommandModel } from &#x27;../shared/eventStream/models/command.model&#x27;;
import {
  LeadRepo,
    LeadRepoConfig,
    LeadRepoConfigHot,
} from &#x27;../shared/models/leadRepo/model&#x27;;
import {
    ILead, IExploitHistory, ITakeCare
} from &#x27;../shared/services/lead/interfaces/lead.interface&#x27;;
import { ImportLeadCommand } from &#x27;./commands/impl/importLead.cmd&#x27;;
import { RenewLeadCommand } from &#x27;./commands/impl/renewLead.cmd&#x27;;
import { DeliverLeadDto, ImportLeadAsExcelDto } from &#x27;./dto/lead.dto&#x27;;
import * as moment from &#x27;moment&#x27;;
import * as momentTz from &#x27;moment-timezone&#x27;;
import { LeadSourceQueryRepository } from &#x27;../leadSource/repository/query.repository&#x27;;
import { PermissionConst } from &#x27;../shared/constant/permission.const&#x27;;
const XLSX &#x3D; require(&#x27;xlsx&#x27;);

interface PosQueueData {
    id: string;
    employeeQueue: Queue&lt;Object&gt;;
}

@Injectable()
export class LeadDomainServiceExtends {
    private readonly context &#x3D; LeadDomainServiceExtends.name;
    private readonly response &#x3D; { success: true };

    constructor(
        private readonly commandBus: CommandBus,
        private readonly loggerService: MsxLoggerService,
        private readonly leadRepoQueryRepo: LeadRepoQueryRepository,
        private readonly employeeRepo: EmployeeQueryRepository,
        private readonly leadQueryRepo: QueryRepository,
        private readonly genCodeSrv: CodeGenerateService,
        private readonly leadRepoDomainSrv: LeadRepoDomainService, // private readonly notificationClient: NotificationClient
        private readonly historyImportSrc: HistoryImportService,
        private readonly sourceRepository: LeadSourceQueryRepository,
        ) {}

    async importLeadFromExcel(
        files: any[],
        options: ImportLeadAsExcelDto,
        actionName: string,
        timezoneClient?: string,
        user?: any
    ) {
        const { repoConfigCode, repoId, source } &#x3D; options;
        const workbook &#x3D; XLSX.read(files[0].buffer, { type: &#x27;buffer&#x27; });
        const sheets &#x3D; workbook.SheetNames;
        const data: any[] &#x3D; await XLSX.utils.sheet_to_json(
            workbook.Sheets[sheets[0]],
            {
                range: 1,
            }
        );

        const { repository, config } &#x3D; await this.validateRepo(repoId, repoConfigCode);

        if (!source) throw new BadRequestException(&#x27;source is required!&#x27;);
        const leadSource: any &#x3D; await this.sourceRepository.findOne({ name: source });
        if(!leadSource) this.sourceRepository.create({name: source});
        const orgChartGroup &#x3D; new Map();
        if (config.orgCharts?.length) {
          config.orgCharts.forEach(item &#x3D;&gt; {
            orgChartGroup.set({ id: item.id, name: item.name }, item.staffIds);
          });
        }

        const hotLeads &#x3D; [];
        const normalLeads &#x3D; [];
        const failedLeads &#x3D; [];
        const assignedLeads &#x3D; [];

        await Bluebird.mapSeries(data, async (item, idx) &#x3D;&gt; {
          const validateLead &#x3D; this.validateLead(item);
          if (validateLead) {
              return failedLeads.push({ line: idx + 1, error: validateLead });
          }
          const { assignee } &#x3D; item;
          const lead &#x3D; await this.preProcessLead(
            item,
            repository,
            config,
            user,
            timezoneClient,
            source,
        );

         

          if (assignee) {
              const employee &#x3D; await this.employeeRepo.findOne({ code: assignee });
              const {error, pos} &#x3D; await this.validateEmployee(employee, orgChartGroup);
              if (error) {
                  return failedLeads.push({
                      line: idx + 1,
                      error,
                  });
              }
              const {history, newHistory} &#x3D; this.updateExploitHistory(
                  lead.exploitHistory,
                  ExploitEnum.ASSIGN,
                  employee.id,
                  {
                      id: employee.id,
                      name: employee.name,
                      phone: employee.phone,
                      email: employee.email,
                  }
              );
              Object.assign(lead, {
                  takeCare: {
                      id: employee.id,
                      name: employee.name,
                      email: employee.email,
                      phone: employee.phone,
                  },
                  assignDuration: config.assignDuration,
                  expireTime: this.getExpireTime(
                      newHistory?.updatedAt,
                      config.assignDuration
                  ),
                  latestAssignHistory: newHistory,
                  exploitStatus: ExploitEnum.ASSIGN,
                  exploitHistory: history,
                  pos,
              });
              assignedLeads.push(lead);
          } else {
              Object.assign(lead, {
                  takeCare: {},
              });
              if (lead.isHot) {
                  hotLeads.push(lead);
              } else {
                  normalLeads.push(lead);
              }
          }
        });
                
        let { newConfig, result } &#x3D; await this.leadDelivery(
            hotLeads,
            repository.configHot as LeadRepoConfig
        );

        let records &#x3D; [...normalLeads, ...assignedLeads];

        if (result.length) {
          const importCommandId &#x3D; uuid.v4();
          result &#x3D; result.map(r &#x3D;&gt; {
            r.notiUser &#x3D; user;
            return r;
          })
          await this.executeCommand(
            Action.IMPORT_LEAD,
            actionName,
            importCommandId,
            result as any[],
            newConfig.notification || {}
          );

          await this.leadRepoDomainSrv.updateLeadRepoMain(
            {
              id: repository.id,
              configHot: newConfig as LeadRepoConfigHot,
              name: repository.name,
            },
            actionName,
            user?.id,
            timezoneClient,
            true
          );
        }

        if (records.length) {
          const importCommandId &#x3D; uuid.v4();
          records &#x3D; records.map(r &#x3D;&gt; {
            r.notiUser &#x3D; user;
            return r;
          })
          await this.executeCommand(
            Action.IMPORT_LEAD,
            actionName,
            importCommandId,
            records,
            config.notification || {}
          );
        }

        const importHistoryRecord &#x3D; {
          fileName: files[0].originalname,
          processBy: user,
          success: [...normalLeads, ...hotLeads, ...assignedLeads].length,
          fail: failedLeads.length,
          type: CommonConst.TYPE.PRIMARY,
          createdDate: new Date(),
          updatedDate: new Date(),
          description: failedLeads
        };

        await this.historyImportSrc.create(importHistoryRecord);

        return this.response;
    }

    async deliverNormalLead(recordNumber) {
        this.loggerService.log(this.context, &#x27;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;Start delivering normal leads...&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x27;);
        const where &#x3D; {
          &#x27;configs.exploitTime.from&#x27; :{ $lte : new Date()},
          &#x27;configs.exploitTime.to&#x27; :{ $gte : new Date()}
        }
        const repos &#x3D; await this.leadRepoQueryRepo.findMany({
          where: where as any,
          projection: { configs: 1, name: 1, code: 1, id: 1, timezoneClient: 1 },
        }, true
        );

        if (!repos?.length) return;
        const bulkRepos &#x3D; [];

        await Bluebird.map(repos, async (r) &#x3D;&gt; {
            const { configs } &#x3D; r;
            const activeConfigs &#x3D; configs.filter(item &#x3D;&gt; item.active);
            if (!activeConfigs?.length) return;
            let shouldUpdate &#x3D; false;
            await Bluebird.mapSeries(activeConfigs, async (c) &#x3D;&gt; {
                const { exploitTime } &#x3D; c;
                const nowMm &#x3D; moment();
                const now &#x3D; nowMm.toDate();
                const day &#x3D; now.getDay() &#x3D;&#x3D;&#x3D; 0 ? 7 : now.getDay() - 1;
                if (
                    now &lt; moment(exploitTime.from).utc().toDate() ||
                    now &gt; moment(exploitTime.to).utc().toDate()
                ) {
                    return;
                }

                const timezoneOffset &#x3D; momentTz.tz(r.timezoneClient).utcOffset() / 60;
                
                if (c.isWorkingTime &amp;&amp; c.workingTime[day] &amp;&amp; c.workingTime[day].startTime &amp;&amp; c.workingTime[day].endTime) {
                    if (now &lt; new Date(&#x60;${nowMm.format(&#x27;YYYY-MM-DD&#x27;)} ${c.workingTime[day].startTime} ${timezoneOffset &gt; 0 ? &#x27;+&#x27; + timezoneOffset : timezoneOffset}&#x60;) ||
                        now &gt; new Date(&#x60;${nowMm.format(&#x27;YYYY-MM-DD&#x27;)} ${c.workingTime[day].endTime} ${timezoneOffset &gt; 0 ? &#x27;+&#x27; + timezoneOffset : timezoneOffset}&#x60;)
                    ) {
                        return;
                    }
                }
                const leads &#x3D; await this.leadQueryRepo.findMany(
                    {
                        repoId: r.id,
                        repoConfigCode: c.code,
                        isHot: false,
                        exploitStatus: {
                            $in: [ExploitEnum.NEW, ExploitEnum.RENEW],
                        },
                    },
                    {
                        exploitHistory: 1,
                        exploitStatus: 1,
                        takeCare: 1,
                        id: 1,
                        pos: 1,
                        expireTime: 1,
                        code: 1,
                        createdDate: 1,
                        name: 1,
                        phone: 1,
                        email: 1,
                        isHot: 1,
                        notiUser: 1
                    },
                    // recordNumber
                );

                const { result, newConfig } &#x3D; await this.leadDelivery(
                    leads as any[],
                    c
                );

                if (!result.length) return;
                // exe 100 records
                const executeNum &#x3D; Math.floor(result.length / 100) + Math.floor(result.length % 100);
                for (let i &#x3D; 0; i &lt; executeNum; i++) {
                  const executeResult &#x3D; result.slice((i * 100), (i * 100) + 100);
                  const importCommandId &#x3D; uuid.v4();
                  await this.executeCommand(
                      Action.RENEW_LEAD,
                      &#x27;Deliver normal lead&#x27;,
                      importCommandId,
                      // result as any,
                      executeResult.map(e &#x3D;&gt; {
                          return {
                              id: e.id,
                              exploitStatus: e.exploitStatus,
                              takeCare: e.takeCare,
                              assignDuration: e.assignDuration,
                              expireTime: e.expireTime,
                              visiblePhone: e.visiblePhone,
                              latestAssignHistory: e.latestAssignHistory,
                              isHot: e.isHot,
                              name: e.name,
                              email: e.email,
                              code: e.code,
                              phone: e.phone,
                              pos: e.pos,
                              notiUser: e.notiUser,
                          } as any;
                      }),
                      newConfig.notification || {}
                  );
                }

                this.updateConfigData(configs, c.code, newConfig);
                if (!shouldUpdate) {
                  shouldUpdate &#x3D; true;
                }

                this.loggerService.log(this.context, &#x60;Delivered ${result.length} leads in ${r.code}-${c.code}&#x60;);
            });

            if (shouldUpdate) {
                Object.assign(r, { configs, updatedDate: new Date() });
                bulkRepos.push(r);
            }
        });
        if (bulkRepos.length) {
          this.leadRepoDomainSrv.bulkUpdateLeadConfig(bulkRepos);
        }

        this.loggerService.log(this.context, &#x60;Finish delivering normal leads&#x60;);
    }

    async renewNormalLead(recordNumber) {
        this.loggerService.log(this.context, &#x27;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;Start renewing normal leads...&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x27;);
        const assigningLead &#x3D; await this.leadQueryRepo.findMany(
            {
                // &#x27;takeCare.id&#x27;: { $exists: true },
                isHot: false,
                expireTime: {
                    $lt: new Date(),
                },
                assignDuration: { $ne: -1 },
                exploitStatus: {
                    $in: [
                        ExploitEnum.REASSIGN,
                        ExploitEnum.ASSIGN,
                        ExploitEnum.PROCESSING,
                    ],
                },
            },
            { id: 1},
            // recordNumber
        );

        if (!assigningLead?.length) {
            return;
        }

        assigningLead.forEach((lead) &#x3D;&gt; {
            const latestAssignHistory: IExploitHistory &#x3D; {
              status: ExploitEnum.RENEW, updatedAt: new Date()
            };

            Object.assign(lead, {
                exploitStatus: ExploitEnum.RENEW,
                latestAssignHistory: latestAssignHistory,
                takeCare: {},
            });
        });
        // exe 100 records
        const executeNum &#x3D; Math.floor(assigningLead.length / 100) + Math.floor(assigningLead.length % 100);
        for (let i &#x3D; 0; i &lt; executeNum; i++) {
          const executeResult &#x3D; assigningLead.slice((i * 100), (i * 100) + 100);
          const importCommandId &#x3D; uuid.v4();
          await this.executeCommand(
            Action.RENEW_LEAD,
            &#x27;Renew normal lead&#x27;,
            importCommandId,
            executeResult.map(e &#x3D;&gt; ({
              id: e.id,
              exploitStatus: e.exploitStatus,
              takeCare: e.takeCare,
              latestAssignHistory: e.latestAssignHistory
            } as any))
          );
          this.loggerService.log(this.context, &#x60;Renewed ${executeResult.length} normal leads expired !!!&#x60;);
        }
        this.loggerService.log(this.context, &#x27;Finish renewing normal leads&#x27;);
    }

    async reAssignHotLead(recordNumber) {
        this.loggerService.log(this.context, &#x27;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;Start reassigning hot leads...&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x3D;&#x27;);

        const repo &#x3D; await this.leadRepoQueryRepo.findMany({
            projection: { id: 1, name: 1, code: 1, configHot: 1 },
        });
        if (!repo?.length) return;
        const bulkRepos &#x3D; [];
        await Bluebird.map(repo, async (r) &#x3D;&gt; {
            let shouldUpdate &#x3D; false;
            const now &#x3D; new Date();
            const day &#x3D; now.getDay() &#x3D;&#x3D;&#x3D; 0 ? 7 : now.getDay() - 1;
            if (r.configHot.isWorkingTime &amp;&amp; r.configHot.workingTime[day] &amp;&amp; r.configHot.workingTime[day].startTime &amp;&amp; r.configHot.workingTime[day].endTime) {
                if (now &lt; new Date(&#x60;${moment(now).format(&#x27;YYYY-MM-DD&#x27;)} ${r.configHot.workingTime[day].startTime}&#x60;) ||
                    now &gt; new Date(&#x60;${moment(now).format(&#x27;YYYY-MM-DD&#x27;)} ${r.configHot.workingTime[day].endTime}&#x60;)) {
                    return;
                }
            }
            const hotleads &#x3D; await this.leadQueryRepo.findMany(
                {
                    $or: [
                        {
                            isHot: true,
                            repoId: r.id,
                            expireTime: {
                                $lt: new Date(),
                            },
                            assignDuration: { $ne: -1 },
                            exploitStatus: {
                                $in: [
                                    ExploitEnum.ASSIGN,
                                    ExploitEnum.REASSIGN,
                                    ExploitEnum.PROCESSING,
                                ],
                            },
                        },
                        {
                            isHot: true,
                            repoId: r.id,
                            exploitStatus: {
                                $in: [ExploitEnum.NEW, ExploitEnum.RENEW],
                            },
                        },
                    ],
                },
                {
                    // exploitHistory: 1,
                    exploitStatus: 1,
                    takeCare: 1,
                    id: 1,
                    pos: 1,
                    expireTime: 1,
                    code: 1,
                    createdDate: 1,
                    name: 1,
                    phone: 1,
                    email: 1,
                    isHot: 1,
                    countAssign: 1,
                    notiUser: 1,
                }, recordNumber
            );

            if (!hotleads?.length) return;

            const { newConfig, result } &#x3D; await this.leadDelivery(
                hotleads,
                r.configHot as LeadRepoConfig
            );

            if (!result?.length) return;
            // exe 100 records
            const executeNum &#x3D; Math.floor(result.length / 100) + Math.floor(result.length % 100);
            for (let i &#x3D; 0; i &lt; executeNum; i++) {
              const executeResult &#x3D; result.slice((i * 100), (i * 100) + 100);
              const importCommandId &#x3D; uuid.v4();
              await this.executeCommand(
                Action.RENEW_LEAD,
                &#x27;Deliver normal lead&#x27;,
                importCommandId,
                // result as any,
                executeResult.map(e &#x3D;&gt; {
                  return {
                    id: e.id,
                    exploitStatus: e.exploitStatus,
                    takeCare: e.takeCare,
                    assignDuration: e.assignDuration,
                    visiblePhone: e.visiblePhone,
                    expireTime: e.expireTime,
                    latestAssignHistory: e.latestAssignHistory,
                    isHot: e.isHot,
                    name: e.name,
                    phone: e.phone,
                    email: e.email,
                    code: e.code,
                    pos: e.pos,
                    notiUser: e.notiUser
                  } as any;
                }),
                newConfig.notification || {}
              );
            }
            if (!shouldUpdate) {
                shouldUpdate &#x3D; true;
            }

            if (shouldUpdate) {
              Object.assign(r, { configHot: newConfig, updatedDate: new Date() });
              bulkRepos.push(r);
            }
            this.loggerService.log(this.context, &#x60;Reassigned ${result.length} hot leads in ${r.code} !!!&#x60;);
        });

        if (bulkRepos.length) {
          this.leadRepoDomainSrv.bulkUpdateLeadConfig(bulkRepos);
        }
        this.loggerService.log(this.context, &#x27;Finish reassigning hot leads&#x27;);
    }

    async deliverLeads(dto: DeliverLeadDto[], user) {
        this.loggerService.log(this.context, &#x27;Deliver leads&#x27;);
        let status &#x3D; [ExploitEnum.MANUAL_DELIVER];
        const isRevoke &#x3D; user.roles.includes(PermissionConst.LEAD_REVOKE_DELIVER);
        let query: any &#x3D; {
          id: {
            $in: dto.map(e &#x3D;&gt; e.id)
          }
        };
        if(isRevoke) {
          status.push(ExploitEnum.NEW,ExploitEnum.RENEW,ExploitEnum.ASSIGN,ExploitEnum.REASSIGN);
        } else {
          query[&quot;takeCare.id&quot;] &#x3D; user.id;
        }
        query.exploitStatus &#x3D; status;
        const [leads, manager] &#x3D; await Promise.all([
            this.leadQueryRepo.findMany(query),
            this.employeeRepo.findOne({ id: user.id })
        ]);

        if (!leads || leads.length !&#x3D;&#x3D; dto.length) {
            throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, &#x27;id&#x27;) });
        }

        if (!manager || dto.some(d &#x3D;&gt; !manager.staffIds.includes(d.assignee))) {
            throw new BadRequestException({ errors: ErrorConst.CommonError(ErrorConst.NOT_FOUND, &#x27;id&#x27;) });
        }

        const employees &#x3D; await this.employeeRepo.find({
            id: {
                $in: dto.map(e &#x3D;&gt; e.assignee)
            }
        });

        const leadRepos &#x3D; await this.leadRepoQueryRepo.find({
            id: {
                $in: uniq(leads.map(e &#x3D;&gt; e.repoId))
            }
        });

        // exe 100 records
        const executeNum &#x3D; Math.floor(leads.length / 100) + Math.floor(leads.length % 100);
        for (let i &#x3D; 0; i &lt; executeNum; i++) {
            const executeResult &#x3D; leads.slice((i * 100), (i * 100) + 100);
            const importCommandId &#x3D; uuid.v4();
            await this.executeCommand(
                Action.RENEW_LEAD,
                &#x27;Manual deliver normal lead&#x27;,
                importCommandId,
                // result as any,
                executeResult.map(lead &#x3D;&gt; {
                    const deliverLead &#x3D; dto.find(d &#x3D;&gt; lead.id &#x3D;&#x3D;&#x3D; d.id);
                    const employee &#x3D; employees.find(emp &#x3D;&gt; emp.id &#x3D;&#x3D;&#x3D; deliverLead?.assignee);
                    const repo &#x3D; leadRepos.find(r &#x3D;&gt; r.id &#x3D;&#x3D;&#x3D; lead.repoId);
                    const config &#x3D; lead.isHot ? repo.configHot : repo.configs.find(c &#x3D;&gt; c.code &#x3D;&#x3D;&#x3D; lead.repoConfigCode);

                    const { history, newHistory } &#x3D; this.updateExploitHistory(
                        lead.exploitHistory,
                        ExploitEnum.ASSIGN,
                        employee.id,
                        {
                            id: employee.id,
                            name: employee.name,
                            phone: employee.phone,
                            email: employee.email,
                        },
                    );
                    return {
                        id: lead.id,
                        exploitStatus: ExploitEnum.ASSIGN,
                        takeCare: {
                            id : employee.id,
                            name : employee.name,
                            email : employee.email,
                            phone : employee.phone
                        },
                        assignDuration: config.assignDuration,
                        expireTime: this.getExpireTime(newHistory?.updatedAt, config.assignDuration),
                        visiblePhone: lead.visiblePhone,
                        latestAssignHistory: newHistory,
                        isHot: lead.isHot,
                        name: lead.name,
                        email: lead.email,
                        code: lead.code,
                        phone: lead.phone,
                        pos: lead.pos
                    } as any;
                }),
                {}
            );
        }

        return { success: true };
    }

    private async leadDelivery(data: ILeadDocument[], config: LeadRepoConfig) {
        const result: ILead[] &#x3D; [];
        if (!data?.length) {
            return {
                result,
                newConfig: config,
            };
        }
        const initQueueData &#x3D; await this.initQueueData(config);
        if (initQueueData.size &#x3D;&#x3D;&#x3D; 0) {
          return {
            result,
            newConfig: config,
          };
        }

        const { orgChartQueue &#x3D; [], assignDuration, orgCharts } &#x3D; config;
        // init pos queue
        const posQueue &#x3D; new Queue&lt;PosQueueData&gt;([]);
        initQueueData.forEach((value, key) &#x3D;&gt; {
            const cloneValue &#x3D; cloneDeep(value);
            const existedQueue &#x3D; orgChartQueue.find((item) &#x3D;&gt; item.id &#x3D;&#x3D;&#x3D; key);

            if (existedQueue &amp;&amp; !config.manualDeliver) {
                posQueue.enqueue({
                    id: existedQueue.id,
                    employeeQueue: new Queue(existedQueue.employeeQueue || []),
                });
            } else {
                posQueue.enqueue({
                    id: key,
                    employeeQueue: new Queue(cloneValue),
                });
            }
        });

        const dataQueue &#x3D; new Queue(data, data.length);

        while (!dataQueue.isEmpty()) {
            const orgChart &#x3D; this.getOrgChart(posQueue, initQueueData);
            const {employeeQueue, id} &#x3D; orgChart;
            const lead &#x3D; dataQueue.dequeue() as ILead;
            const posName &#x3D; orgCharts.find((item) &#x3D;&gt; item.id &#x3D;&#x3D;&#x3D; id)?.name;
            const employee &#x3D; employeeQueue.dequeue() as any;
            if (lead.takeCare?.id !&#x3D;&#x3D; employee.id) {
                let exploitStatus &#x3D;
                    lead.exploitStatus &#x3D;&#x3D;&#x3D; ExploitEnum.NEW
                        ? ExploitEnum.ASSIGN
                        : ExploitEnum.REASSIGN;
                if (config.manualDeliver) {
                    exploitStatus &#x3D; ExploitEnum.MANUAL_DELIVER;
                }
                const {history, newHistory} &#x3D; this.updateExploitHistory(
                  lead.exploitHistory,
                  exploitStatus,
                  employee.id,
                  {
                        id: employee.id,
                        name: employee.name,
                        phone: employee.phone,
                        email: employee.email,
                  },
                );

                Object.assign(lead, {
                    exploitStatus,
                    exploitHistory: history,
                    assignDuration,
                    takeCare: employee,
                    expireTime: this.getExpireTime(
                        newHistory?.updatedAt,
                        assignDuration
                    ),
                    latestAssignHistory: newHistory,
                    visiblePhone: config.visiblePhone,
                    pos: { id, name: posName },
                });
                result.push(lead);
            }
            posQueue.enqueue(orgChart);
        }

        Object.assign(config, { orgChartQueue: this.parseQueueData(posQueue) });

        return { result, newConfig: config };
    }

    private parseQueueData(posQueue: Queue&lt;PosQueueData&gt;) {
        return posQueue.data.map((item) &#x3D;&gt; ({
            id: item.id,
            employeeQueue: item.employeeQueue.data,
        }));
    }

    private deliveredToAll(posQueue: Queue&lt;PosQueueData&gt;) {
        return !posQueue.data.some((item) &#x3D;&gt; {
            return !item.employeeQueue.isEmpty();
        });
    }

    private async initQueueData(
        config: LeadRepoConfig
    ): Promise&lt;Map&lt;string, any[]&gt;&gt; {
        const initData &#x3D; new Map&lt;string, any[]&gt;();
        const {orgCharts} &#x3D; config;

        await Bluebird.mapSeries(orgCharts, async org &#x3D;&gt; {
          const {staffIds} &#x3D; org;

          if (config.manualDeliver) {
            const managers &#x3D; await this.employeeRepo.findManyWithSort(
                {
                    managerAt: org.id,
                    active: true
                },
                { id: 1, name: 1, email: 1, phone: 1, code: 1 }
            );
            if (managers?.length &gt; 0) {
                initData.set(
                    org.id,
                    managers.map((e) &#x3D;&gt; ({
                        id: e.id,
                        name: e.name,
                        email: e.email,
                        phone: e.phone,
                    }))
                );
            }
          } else if (staffIds?.length &gt; 0) {
            const employees &#x3D; await this.employeeRepo.findManyWithSort(
                {
                    id: { $in: staffIds },
                    active: true
                },
                { id: 1, name: 1, email: 1, phone: 1, code: 1 }
            );
            if (employees?.length &gt; 0) {
                initData.set(
                    org.id,
                    employees.map((e) &#x3D;&gt; ({
                        id: e.id,
                        name: e.name,
                        email: e.email,
                        phone: e.phone,
                    }))
                );
            }
          }
        });

        return initData;
    }

    private async validateRepo(repoId: string, repoConfigCode: string) {
        const repository &#x3D; await this.leadRepoQueryRepo.findById(repoId, true);

        if (!repository) {
            throw new BadRequestException({
                errors: ErrorConst.CommonError(
                    ErrorConst.NOT_FOUND,
                    &#x27;Lead Repository&#x27;
                ),
            });
        }

        const config &#x3D; repository.configs.find(
            (item) &#x3D;&gt; item.code &#x3D;&#x3D;&#x3D; repoConfigCode
        );
        if (!config) {
            throw new BadRequestException({
                errors: ErrorConst.CommonError(
                    ErrorConst.NOT_FOUND,
                    &#x27;Lead Repository Config&#x27;
                ),
            });
        }

        return { repository, config };
    }

    private async executeCommand&lt;T&gt;(
        action: string,
        actionName: string,
        commandId: string,
        item: CommandModel | CommandModel[],
        additionalData?: T
    ) {
        switch (action) {
            case Action.IMPORT_LEAD:
                return this.commandBus.execute(
                    new ImportLeadCommand(
                        actionName,
                        commandId,
                        item as CommandModel[],
                        additionalData
                    )
                );
            case Action.RENEW_LEAD:
                return this.commandBus.execute(
                    new RenewLeadCommand(
                        actionName,
                        commandId,
                        item as CommandModel[],
                        additionalData
                    )
                );
            default:
                break;
        }
    }

    private updateExploitHistory(
        history: IExploitHistory[] &#x3D; [],
        status: ExploitEnum,
        takeCareId?: string,
        takeCareInfo?: ITakeCare,
    ) {
        const newHistory: IExploitHistory &#x3D; {
          status, updatedAt: new Date(), updatedBy: takeCareId, takeCareId, takeCareInfo
        };
        if (history.length &gt; 500) {
          history &#x3D; history.slice(history.length - 500);
        }
        history &#x3D; history.concat(newHistory);

        return {history, newHistory};
    }

    private validateLead(lead) {
      if (isNullOrUndefined(lead.phone) || !lead.phone.toString().match(CommonConst.REGEX_VN_PHONE)){
          return &#x27;Số điện thoại sai&#x27;;
      }
      if (lead.email &amp;&amp; !lead.email.toString().match(CommonConst.REGEX_EMAIL)){
          return &#x27;Email sai&#x27;;
      }
      return &#x27;&#x27;;
  }

  private async validateEmployee(employee, orgChartGroup: Map&lt;string, string[]&gt;) {
    const pos: any &#x3D; {};
    orgChartGroup.forEach((v, k) &#x3D;&gt; {
        if (v.includes(employee?.id)) {
            Object.assign(pos, k);
        }
    });

    if (!isEmpty(pos)) {
        return {
          pos
        };
    }

    return {
        error: &#x60;Nhân viên ${employee?.code} không tồn tại trên sàn chỉ định&#x60;,
    };
  }

  private async preProcessLead(lead, repository: LeadRepo, config: LeadRepoConfig, user, timezoneClient, source) {
    const code &#x3D; await this.genCodeSrv.generateCode(&#x27;YC-&#x27;);
    const project &#x3D; !lead.isHot ? config.project : {};
    const result &#x3D; {
        ...lead,
        repoId: repository.id,
        repoConfigCode: config.code,
        importedBy: {
            id: user?.id,
            name: user?.name,
        },
        isHot: lead.isHot ?? false,
        id: uuid.v4(),
        type: CommonConst.TYPE.PRIMARY,
        status: StatusEnum.GREEN,
        lifeCycleStatus: LifeCycleStatusEnum.IN_POOL,
        visiblePhone: config.visiblePhone,
        timezoneClient,
        project,
        code,
        source,
    };
    const { history } &#x3D; this.updateExploitHistory(
        result.exploitHistory,
        ExploitEnum.NEW
    );
    Object.assign(result, {
      _id: result.id,
      exploitStatus: ExploitEnum.NEW,
      exploitHistory: history,
    });

    return result;
  }

  private updateConfigData(src: LeadRepoConfig[], code: string, config: LeadRepoConfig) {
    const index &#x3D; src.findIndex(item &#x3D;&gt; item.code &#x3D;&#x3D;&#x3D; code);
    if (!code) {
      src &#x3D; src.concat(config);
    }
    if (code) {
      src &#x3D; [...src.slice(0, index), config, ...src.slice(index + 1)];
    }
  }

  private getExpireTime(start: Date, duration: number) {
    return new Date(moment(start).valueOf() + duration * 60000);
  }

  private getOrgChart(posQueue: Queue&lt;PosQueueData&gt;, initData: Map&lt;string, any[]&gt;) {
    const orgChart &#x3D; posQueue.dequeue() as PosQueueData;
    const { employeeQueue, id } &#x3D; orgChart;
    if (!employeeQueue.isEmpty()) {
      return orgChart;
    }
    if (employeeQueue.isEmpty()) {
        if (this.deliveredToAll(posQueue)) {
            posQueue.data.forEach((item) &#x3D;&gt; {
                const inQueueData &#x3D; cloneDeep(initData.get(item.id));
                item.employeeQueue.reset(inQueueData);
            });
            const currentData &#x3D; cloneDeep(initData.get(id)); 
            orgChart.employeeQueue.reset(currentData);
            return orgChart;
        } else {
          posQueue.enqueue(orgChart);
          return this.getOrgChart(posQueue, initData);
        }
    }
  }

  async createLeadCommon(
    options: any,
    actionName: string,
    timezoneClient?: string,
    user?: any
  ) {
    const { repoConfigCode, repoId, source, posId } &#x3D; options;
    const { repository, config } &#x3D; await this.validateRepo(repoId, repoConfigCode);

    if (!source) throw new BadRequestException(&#x27;source is required!&#x27;);
    const leadSource: any &#x3D; await this.sourceRepository.findOne({ name: source });
    if (!leadSource) this.sourceRepository.create({ name: source });
    const orgChartGroup &#x3D; new Map();
    if (config.orgCharts?.length) {
      config.orgCharts.forEach(item &#x3D;&gt; {
        orgChartGroup.set({ id: item.id, name: item.name }, item.staffIds);
      });
    }

    const hotLeads &#x3D; [];
    const normalLeads &#x3D; [];
    const assignedLeads &#x3D; [];
    const validateLead &#x3D; this.validateLead(options);
    if (validateLead) {
      return validateLead;
    }
    const lead &#x3D; await this.preProcessLead(
      options,
      repository,
      config,
      user,
      timezoneClient,
      source,
    );

    const manager &#x3D; await this.employeeRepo.getManagerByPos(posId);

    let assignee &#x3D; null;
    if(posId &amp;&amp; options?.assignee) {
      assignee &#x3D; options?.assignee;
    }
    if(posId &amp;&amp; !options?.assignee) {
      assignee &#x3D; manager ? manager[0].code : null;
    }
    if (assignee) {
      const employee &#x3D; await this.employeeRepo.findOne({ code: assignee });
      const { error, pos } &#x3D; await this.validateEmployee(employee, orgChartGroup);
      if (error) {
        return error;
      }
      const { history, newHistory } &#x3D; this.updateExploitHistory(
        lead.exploitHistory,
        ExploitEnum.ASSIGN,
        employee.id,
        {
          id: employee.id,
          name: employee.name,
          phone: employee.phone,
          email: employee.email,
        }
      );
      Object.assign(lead, {
        takeCare: {
          id: employee.id,
          name: employee.name,
          email: employee.email,
          phone: employee.phone,
        },
        assignDuration: config.assignDuration,
        expireTime: this.getExpireTime(
          newHistory?.updatedAt,
          config.assignDuration
        ),
        latestAssignHistory: newHistory,
        exploitStatus: ExploitEnum.ASSIGN,
        exploitHistory: history,
        pos,
      });
      assignedLeads.push(lead);
    } else {
      Object.assign(lead, {
        takeCare: {}
      });
      if (lead.isHot) {
        hotLeads.push(lead);
      } else {
        normalLeads.push(lead);
      }
    }
    const { newConfig, result } &#x3D; await this.leadDelivery(
      hotLeads,
      repository.configHot as LeadRepoConfig
    );

    const records &#x3D; [...normalLeads, ...assignedLeads];

    if (result.length) {
      const importCommandId &#x3D; uuid.v4();
      await this.executeCommand(
        Action.IMPORT_LEAD,
        actionName,
        importCommandId,
        result as any[],
        newConfig.notification || {}
      );

      await this.leadRepoDomainSrv.updateLeadRepoMain(
        {
          id: repository.id,
          configHot: newConfig as LeadRepoConfigHot,
          name: repository.name,
        },
        actionName,
        user?.id,
        timezoneClient,
        true
      );
    }

    if (records.length) {
      const importCommandId &#x3D; uuid.v4();
      await this.executeCommand(
        Action.IMPORT_LEAD,
        actionName,
        importCommandId,
        records,
        config.notification || {}
      );
    }
    return this.response;
  }
}
</code></pre>
    </div>

</div>







                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'LeadDomainServiceExtends.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
