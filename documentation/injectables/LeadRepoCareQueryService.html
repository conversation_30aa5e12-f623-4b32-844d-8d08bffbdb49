<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>msx-lead documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	      <link rel="stylesheet" href="../styles/style.css">
    </head>
    <body>

        <div class="navbar navbar-default navbar-fixed-top visible-xs">
            <a href="../" class="navbar-brand">msx-lead documentation</a>
            <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="hidden-xs menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content injectable">
                   <div class="content-data">







<ol class="breadcrumb">
  <li>Injectables</li>
  <li>LeadRepoCareQueryService</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="active">
            <a href="#info" role="tab" id="info-tab" data-toggle="tab" data-link="info">Info</a>
        </li>
        <li >
            <a href="#source" role="tab" id="source-tab" data-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="c-info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/modules/leadRepoCare.queryside/service.ts</code>
        </p>




            <section>
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getLeadRepoCareByProject">getLeadRepoCareByProject</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getLeadRepoCareConfigsByProjectId">getLeadRepoCareConfigsByProjectId</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getLeadRepoCareDetail">getLeadRepoCareDetail</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getLeadRepoCareList">getLeadRepoCareList</a>
                            </li>
                            <li>
                                    <span class="modifier">Async</span>
                                <a href="#getSurveys">getSurveys</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

            <section>
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(repository: <a href="../injectables/LeadRepoCareQueryRepository.html">LeadRepoCareQueryRepository</a>, propertyClient: <a href="../injectables/PropertyClient.html">PropertyClient</a>)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="11" class="link-to-prism">src/modules/leadRepoCare.queryside/service.ts:11</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>repository</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/LeadRepoCareQueryRepository.html" target="_self" >LeadRepoCareQueryRepository</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                                <tr>
                                                        <td>propertyClient</td>
                                                  
                                                        <td>
                                                                        <code><a href="../injectables/PropertyClient.html" target="_self" >PropertyClient</a></code>
                                                        </td>
                                                  
                                                    <td>
                                                            No
                                                    </td>
                                                    
                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

            <section>
    
    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getLeadRepoCareByProject"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getLeadRepoCareByProject
                        </b>
                        <a href="#getLeadRepoCareByProject"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getLeadRepoCareByProject(projectId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="86"
                            class="link-to-prism">src/modules/leadRepoCare.queryside/service.ts:86</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>projectId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getLeadRepoCareConfigsByProjectId"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getLeadRepoCareConfigsByProjectId
                        </b>
                        <a href="#getLeadRepoCareConfigsByProjectId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getLeadRepoCareConfigsByProjectId(query: literal type)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="52"
                            class="link-to-prism">src/modules/leadRepoCare.queryside/service.ts:52</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>query</td>
                                    <td>
                                            <code>literal type</code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getLeadRepoCareDetail"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getLeadRepoCareDetail
                        </b>
                        <a href="#getLeadRepoCareDetail"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getLeadRepoCareDetail(id: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="48"
                            class="link-to-prism">src/modules/leadRepoCare.queryside/service.ts:48</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>id</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getLeadRepoCareList"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getLeadRepoCareList
                        </b>
                        <a href="#getLeadRepoCareList"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getLeadRepoCareList(page: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>, pageSize: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>, keywords: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, user)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="17"
                            class="link-to-prism">src/modules/leadRepoCare.queryside/service.ts:17</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>page</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>pageSize</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>keywords</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                                <tr>
                                    <td>user</td>
                                    <td>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getSurveys"></a>
                    <span class="name">
                        <b>
                            <span class="modifier">Async</span>
                            getSurveys
                        </b>
                        <a href="#getSurveys"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                    <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>getSurveys(configId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">
                    <div class="io-line">Defined in <a href="" data-line="89"
                            class="link-to-prism">src/modules/leadRepoCare.queryside/service.ts:89</a></div>
                </td>
            </tr>


            <tr>
                <td class="col-md-4">

                    <div class="io-description">
                        <b>Parameters :</b>
                        <table class="params">
                            <thead>
                                <tr>
                                    <td>Name</td>
                                    <td>Type</td>
                                    <td>Optional</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>configId</td>
                                    <td>
                                                <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                    </td>

                                    <td>
                                        No
                                    </td>


                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                    </div>
                    <div class="io-description">
                        <b>Returns : </b>    <code>{}</code>

                    </div>
                    <div class="io-description">
                        
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

    </div>


    <div class="tab-pane fade  tab-source-code" id="c-source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Inject, Injectable } from &#x27;@nestjs/common&#x27;;
import { CodeGenerateService } from &#x27;../code-generate/service&#x27;;
import { CommonConst } from &#x27;../shared/constant/common.const&#x27;;
import { FindManyFilter, SearchRegex, WhereFilter } from &#x27;../shared/interfaces/baseFilter.interface&#x27;;
import { LeadRepoCare } from &#x27;../shared/models/leadRepoCare/model&#x27;;
import { LeadRepoCareQueryRepository } from &#x27;./repositories/query.repository&#x27;;
import { PropertyClient } from &#x27;../mgs-sender/property.client&#x27;;
import { CmdPatternConst } from &#x27;../shared/constant/cmd-pattern.const&#x27;;

@Injectable()
export class LeadRepoCareQueryService {
    constructor(
      protected readonly repository: LeadRepoCareQueryRepository,
      protected readonly propertyClient: PropertyClient,
      ) {}

    async getLeadRepoCareList(page: number, pageSize: number, keywords: string, user) {
        const whereFilterWithSearch &#x3D; {
          $or: [
            {code: {$regex: new RegExp(keywords), $options: &#x27;i&#x27;}},
            {name: {$regex: new RegExp(keywords), $options: &#x27;i&#x27;}}
          ]
        };
        const projectIds &#x3D; await this.propertyClient.sendDataPromise({id:user.id}, CmdPatternConst.PROJECT.GET_PROJECT_BY_CUSTOMER_SERVICE)
        if(!projectIds || !projectIds.length){
            return []
        }
        whereFilterWithSearch[&#x27;configs.project.id&#x27;] &#x3D; { $in: projectIds};
        const total &#x3D; await this.repository.count(whereFilterWithSearch as any);
        const data &#x3D; await this.repository.findMany({
            where: whereFilterWithSearch as any,
            skip: Math.floor(pageSize * page - pageSize),
            limit: pageSize,
            sort: {
                createdDate: -1,
            },
        });

        return {
            page,
            pageSize,
            rows: data,
            total,
            totalPages: Math.ceil(total / pageSize),
        };
    }

    async getLeadRepoCareDetail(id: string) {
        return this.repository.findById(id);
    }
    
    async getLeadRepoCareConfigsByProjectId(query : {projectId: string, type: string, target: string}) {
      let { projectId, type, target } &#x3D; query;

      // if(type){ 
      //   type &#x3D; type.trim().toLowerCase();
      // }

      const now &#x3D; new Date();
      const where: any &#x3D; { 
        &quot;configs.projectId&quot;: projectId, 
        &quot;configs.exploitTime.from&quot; : { 
          $lte : now,
        },
        &quot;configs.exploitTime.to&quot; : { 
          $gt : now,
        }
      }
      if(type) {
        let typeSplit &#x3D; type.split(&#x27;,&#x27;)
        where.type&#x3D; {
          $in: typeSplit
        }
      }

      const leadRepoCares &#x3D; await this.repository.findByQuery(where) || [];
      let configs &#x3D; []; 
      
      leadRepoCares.forEach((l) &#x3D;&gt; { 
        const filteredConfigs &#x3D;  l.configs.filter((item)&#x3D;&gt; item.projectId &#x3D;&#x3D;&#x3D; projectId &amp;&amp; item.active &amp;&amp; item.forLoggedUser &amp;&amp; item.target &#x3D;&#x3D;&#x3D; target);
        configs.push(...filteredConfigs);
      });

      return configs;
    }
    async getLeadRepoCareByProject(projectId: string) {
      return await this.repository.getLeadRepoCareByProject(projectId) || [];
    }
    async getSurveys(configId: string){ 
      const now &#x3D; new Date();
      const where &#x3D; { 
          &quot;configs._id&quot;: configId,
          &quot;configs.exploitTime.from&quot; : { 
            $lte : now,
          },
          &quot;configs.exploitTime.to&quot; : { 
            $gt : now,
          }
      }
      
      const leadRepoCares &#x3D; await this.repository.findByQuery(where);
      
      let surveys &#x3D; []; 

      leadRepoCares.forEach((l)&#x3D;&gt; { 
        l.configs.forEach((c) &#x3D;&gt; { 
          if(c.surveys &amp;&amp; c.surveys.length) { 
            surveys.push(...c.surveys)
          }
        })
      })

      return surveys;
    }
}
</code></pre>
    </div>

</div>







                   




                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> result-matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'injectable';
            var COMPODOC_CURRENT_PAGE_URL = 'LeadRepoCareQueryService.html';
       </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>
       <!-- Required to polyfill modern browsers as code is ES5 for IE... -->
       <script src="../js/libs/custom-elements-es5-adapter.js" charset="utf-8" defer></script>
       <script src="../js/menu-wc.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>
       <script src="../js/libs/zepto.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
